# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a full-stack SvelteKit 2 application using Svelte 5 runes, built as a "seed" framework for SaaS applications with multi-tenancy support. The project integrates GraphQL (Apollo Client), TailwindCSS v4, Skeleton UI components, and deploys to Cloudflare Pages.

## Development Commands

### Development Server

```bash
npm run dev          # Start development server
npm run build        # Production build
npm run preview      # Preview production build
```

### Code Quality

```bash
npm run lint         # Run Prettier + ESLint (auto-fixes)
npm run format       # Format code with Prettier
npm run check        # SvelteKit sync + TypeScript checking
npm run check:watch  # Type checking in watch mode
```

### Testing

```bash
npm test             # Run all tests (integration + unit)
npm run test:unit    # Run Vitest unit tests
npm run test:integration  # Run Playwright tests
npm run test:ui      # Open Vitest UI
```

### GraphQL

```bash
npm run generate     # Generate all GraphQL types
npm run generate:gateway        # Generate gateway types
npm run generate:subscriptions  # Generate subscription types
```

## Architecture

### Core Tech Stack

- **Frontend**: Svelte 5 with runes ($state, $derived, $effect)
- **Framework**: SvelteKit 2 with Cloudflare adapter
- **Styling**: TailwindCSS v4 + Skeleton UI components
- **Data**: Apollo Client for GraphQL with auto-generated types
- **Auth**: Auth.js/NextAuth.js for SvelteKit
- **Node**: v24 (see .nvmrc)

### Key Architectural Patterns

#### Seed vs Project-Specific Structure

The codebase follows a "seed" pattern where generic/reusable components are separated from project-specific code:

- **`src/lib/modules/`** - Seed-only, generic modules (AppShell, Dashboard, DataTable, Form, etc.)
- **`src/lib/components/`** - Mixed seed and project-specific components
- **`src/routes/`** - Project-specific application routes

#### Route Structure

- `(app)/` - Main authenticated application
- `(auth)/` - Authentication pages
- `(marketing)/` - Public marketing pages
- `(markdown)/` - Static content (legal, blog)
- `backoffice/` - Multi-tenant admin interface

#### State Management

Uses Svelte 5 runes pattern:

- `$state()` for reactive state
- `$derived()` for computed values
- `$effect()` for side effects
- Context-based state sharing via `createContext()` utility

### GraphQL Integration

- Auto-generated TypeScript types from schema
- Apollo Client with authentication and error handling
- Real-time subscriptions via WebSocket
- Persisted queries for performance

### Multi-tenancy

- Organization-based routing (`/org/[id]/`)
- Role-based access control
- Custom domain support
- Backoffice administration interface

## Development Guidelines

### Svelte 5 Conventions

- Use runes (`$state`, `$derived`, `$effect`) instead of stores
- Event handlers use standard HTML attributes (`onclick` vs `on:click`)
- Component props use `$props()` rune
- Avoid legacy Svelte 4 patterns

### Code Style

- Use tabs for indentation
- Single quotes, no semicolons
- 120 character line width
- Imports grouped by type, alphabetically sorted

### File Naming

- Components: PascalCase (e.g., `AppShell.svelte`)
- Actions: camelCase with `.svelte.ts` suffix
- State/Context: camelCase with `.svelte.ts` suffix
- Forms: camelCase with `.form.svelte.ts` suffix

### Testing Philosophy

- Prioritize integration tests over unit tests
- Test user workflows, not implementation details
- Use real implementations, avoid excessive mocking
- Run `npm test` before committing

## Common Patterns

### Creating Context

```typescript
// Use the createContext utility for shared state
import { createContext } from '$lib/services/create-context'

const [getContext, setContext] = createContext<StateType>('ContextName')
```

### Form Building

Use the FormBuilder pattern from `src/lib/modules/Form/`:

- `FormBuilder.svelte` for form containers
- `FormField.svelte` for individual fields
- `.form.svelte.ts` files for form state management

### Data Tables

Use `DataTable.svelte` from `src/lib/modules/DataTable/` with:

- Built-in sorting, filtering, pagination
- TypeScript interfaces in `data-table.interface.ts`

### Notifications

Use the toaster system from `src/lib/modules/Notifications/`:

```typescript
import { toaster } from '$lib/modules/Notifications/toaster.svelte.ts'
toaster.success('Message')
```

## Environment Configuration

Environment files follow this pattern:

- `.env.local` - Local development
- `.env.develop` - Development environment
- `.env.staging` - Staging environment
- `.env.main` - Production environment

Key variables:

- `VITE_ENVIRONMENT` - Current environment
- `VITE_GATEWAY_URL` - GraphQL API endpoint
- `VITE_SUBSCRIPTIONS_URL` - GraphQL subscriptions endpoint
- `VITE_OIDC_ISSUER` - Authentication provider

## Deployment

The application deploys to Cloudflare Pages:

```bash
npm run cloudflare:deploy:develop  # Deploy to develop
npm run cloudflare:deploy:stage    # Deploy to staging
npm run cloudflare:deploy:main     # Deploy to production
```

Git workflow uses conventional commits with Commitizen (`npm run commit`).
