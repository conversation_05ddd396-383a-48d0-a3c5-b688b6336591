import { sveltekit } from '@sveltejs/kit/vite'
import tailwindcss from '@tailwindcss/vite'
import { svelteTesting } from '@testing-library/svelte/vite'
import { defineConfig } from 'vite'
import devtoolsJson from 'vite-plugin-devtools-json'
import mkcert from 'vite-plugin-mkcert'

export default defineConfig({
	plugins: [tailwindcss(), sveltekit(), mkcert(), svelteTesting(), devtoolsJson()],
	ssr: {
		// format: 'cjs',
		noExternal: ['echarts', 'zrender'],
	},
	server: { proxy: {} },
	test: {
		globals: true,
		environment: 'happy-dom',
		setupFiles: ['vitest-setup.ts'],
		include: ['src/**/*.{test,spec}.?(c|m)[jt]s?(x)'],
		coverage: { exclude: ['vitest-setup.ts'] },
	},
})
