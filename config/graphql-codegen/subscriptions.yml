hooks:
  afterAllFileWrite:
    - prettier --write ./src/lib/graphql/generated/subscriptions/
overwrite: true
schema: http://localhost:4002/graphql
documents:
  - src/**/subscriptions/*.graphql
generates:
  src/lib/graphql/generated/subscriptions/sdk.ts:
    plugins:
      - typescript
      - typescript-operations
      - graphql-codegen-svelte-apollo
    config:
      clientPath: $lib/graphql/subscriptions-apollo-client
      asyncQuery: true
      useTypeImports: true
  src/lib/graphql/generated/subscriptions/possible-types.ts:
    plugins:
      - fragment-matcher
    config:
      apolloClientVersion: 3
      federation: true
      useTypeImports: true
  src/lib/graphql/generated/subscriptions/apollo-helpers.ts:
    plugins:
      - typescript-apollo-client-helpers
    config:
      useTypeImports: true
  src/lib/graphql/generated/subscriptions/index.ts:
    plugins:
      - add:
          content: "export * from './sdk'"
      - add:
          content: "export * from './possible-types'"
      - add:
          content: "export * from './apollo-helpers'"
    config:
      useTypeImports: true
