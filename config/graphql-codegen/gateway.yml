hooks:
  afterAllFileWrite:
    - prettier --write ./src/lib/graphql/generated/gateway/
overwrite: true
schema: http://localhost:3100/graphql
documents:
  - src/**/(gateway|fragments)/*.graphql
generates:
  src/lib/graphql/generated/gateway/sdk.ts:
    plugins:
      - typescript
      - typescript-operations
      - graphql-codegen-svelte-apollo
    config:
      clientPath: $lib/graphql/apollo-client
      asyncQuery: true
      useTypeImports: true
  src/lib/graphql/generated/gateway/possible-types.ts:
    plugins:
      - fragment-matcher
    config:
      apolloClientVersion: 3
      federation: true
      useTypeImports: true
  src/lib/graphql/generated/gateway/apollo-helpers.ts:
    plugins:
      - typescript-apollo-client-helpers
    config:
      useTypeImports: true
  src/lib/graphql/generated/gateway/index.ts:
    plugins:
      - add:
          content: "export * from './sdk'"
      - add:
          content: "export * from './possible-types'"
      - add:
          content: "export * from './apollo-helpers'"
    config:
      useTypeImports: true
