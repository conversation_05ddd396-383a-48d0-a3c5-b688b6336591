[data-theme='custom-theme'] {
	/* --text-scaling: 1.067; */
	--text-scaling: 1;
	--base-font-color: var(--color-surface-950);
	--base-font-color-dark: var(--color-surface-50);
	--base-font-family: system-ui;
	--base-font-size: inherit;
	--base-line-height: inherit;
	--base-font-weight: normal;
	--base-font-style: normal;
	--base-letter-spacing: 0em;
	--heading-font-color: inherit;
	--heading-font-color-dark: inherit;
	--heading-font-family: inherit;
	--heading-font-weight: 500;
	--heading-font-style: normal;
	--heading-letter-spacing: inherit;
	--anchor-font-color: var(--color-primary-500);
	--anchor-font-color-dark: var(--color-primary-400);
	--anchor-font-family: inherit;
	--anchor-font-size: inherit;
	--anchor-line-height: inherit;
	--anchor-font-weight: inherit;
	--anchor-font-style: inherit;
	--anchor-letter-spacing: inherit;
	--anchor-text-decoration: none;
	--anchor-text-decoration-hover: underline;
	--anchor-text-decoration-active: none;
	--anchor-text-decoration-focus: none;
	--spacing: 0.25rem;
	--radius-base: 0.25rem;
	--radius-container: 0.25rem;
	--default-border-width: 1px;
	--default-divide-width: 1px;
	--default-ring-width: 1px;
	--body-background-color: var(--color-surface-50);
	--body-background-color-dark: var(--color-surface-950);
	--color-primary-50: oklch(92% 0.03 3.74deg);
	--color-primary-100: oklch(83.05% 0.07 4.92deg);
	--color-primary-200: oklch(74.57% 0.11 7.04deg);
	--color-primary-300: oklch(66.75% 0.16 10.08deg);
	--color-primary-400: oklch(60.23% 0.19 13.97deg);
	--color-primary-500: oklch(55.71% 0.21 19.55deg);
	--color-primary-600: oklch(51.41% 0.2 19.27deg);
	--color-primary-700: oklch(47.21% 0.18 19.04deg);
	--color-primary-800: oklch(42.66% 0.16 18.94deg);
	--color-primary-900: oklch(38.26% 0.14 18.6deg);
	--color-primary-950: oklch(33.54% 0.12 17.98deg);
	--color-primary-contrast-dark: var(--color-primary-950);
	--color-primary-contrast-light: var(--color-primary-50);
	--color-primary-contrast-50: var(--color-primary-contrast-dark);
	--color-primary-contrast-100: var(--color-primary-contrast-dark);
	--color-primary-contrast-200: var(--color-primary-contrast-dark);
	--color-primary-contrast-300: var(--color-primary-contrast-dark);
	--color-primary-contrast-400: var(--color-primary-contrast-light);
	--color-primary-contrast-500: var(--color-primary-contrast-light);
	--color-primary-contrast-600: var(--color-primary-contrast-light);
	--color-primary-contrast-700: var(--color-primary-contrast-light);
	--color-primary-contrast-800: var(--color-primary-contrast-light);
	--color-primary-contrast-900: var(--color-primary-contrast-light);
	--color-primary-contrast-950: var(--color-primary-contrast-light);
	--color-secondary-50: oklch(94.03% 0.01 233.69deg);
	--color-secondary-100: oklch(87.13% 0.03 236.19deg);
	--color-secondary-200: oklch(80.12% 0.04 237.74deg);
	--color-secondary-300: oklch(73.32% 0.06 236.84deg);
	--color-secondary-400: oklch(66.26% 0.08 238.42deg);
	--color-secondary-500: oklch(59.26% 0.09 239.95deg);
	--color-secondary-600: oklch(54.74% 0.08 240.34deg);
	--color-secondary-700: oklch(50.38% 0.08 239.53deg);
	--color-secondary-800: oklch(45.67% 0.07 240.4deg);
	--color-secondary-900: oklch(41.09% 0.06 239.35deg);
	--color-secondary-950: oklch(36.12% 0.05 239.95deg);
	--color-secondary-contrast-dark: var(--color-secondary-950);
	--color-secondary-contrast-light: var(--color-secondary-50);
	--color-secondary-contrast-50: var(--color-secondary-contrast-dark);
	--color-secondary-contrast-100: var(--color-secondary-contrast-dark);
	--color-secondary-contrast-200: var(--color-secondary-contrast-dark);
	--color-secondary-contrast-300: var(--color-secondary-contrast-dark);
	--color-secondary-contrast-400: var(--color-secondary-contrast-dark);
	--color-secondary-contrast-500: var(--color-secondary-contrast-light);
	--color-secondary-contrast-600: var(--color-secondary-contrast-light);
	--color-secondary-contrast-700: var(--color-secondary-contrast-light);
	--color-secondary-contrast-800: var(--color-secondary-contrast-light);
	--color-secondary-contrast-900: var(--color-secondary-contrast-light);
	--color-secondary-contrast-950: var(--color-secondary-contrast-light);
	--color-tertiary-50: oklch(96.87% 0 18.01deg);
	--color-tertiary-100: oklch(93.3% 0 39.8deg);
	--color-tertiary-200: oklch(89.51% 0.01 31.28deg);
	--color-tertiary-300: oklch(85.98% 0.01 27.4deg);
	--color-tertiary-400: oklch(82.11% 0.01 25.22deg);
	--color-tertiary-500: oklch(78.4% 0.01 31.17deg);
	--color-tertiary-600: oklch(72.28% 0.01 25.22deg);
	--color-tertiary-700: oklch(66.29% 0.01 34.39deg);
	--color-tertiary-800: oklch(59.9% 0.01 27.38deg);
	--color-tertiary-900: oklch(53.61% 0.01 39.49deg);
	--color-tertiary-950: oklch(46.84% 0.01 31.17deg);
	--color-tertiary-contrast-dark: var(--color-tertiary-950);
	--color-tertiary-contrast-light: var(--color-tertiary-50);
	--color-tertiary-contrast-50: var(--color-tertiary-contrast-dark);
	--color-tertiary-contrast-100: var(--color-tertiary-contrast-dark);
	--color-tertiary-contrast-200: var(--color-tertiary-contrast-dark);
	--color-tertiary-contrast-300: var(--color-tertiary-contrast-dark);
	--color-tertiary-contrast-400: var(--color-tertiary-contrast-dark);
	--color-tertiary-contrast-500: var(--color-tertiary-contrast-dark);
	--color-tertiary-contrast-600: var(--color-tertiary-contrast-dark);
	--color-tertiary-contrast-700: var(--color-tertiary-contrast-light);
	--color-tertiary-contrast-800: var(--color-tertiary-contrast-light);
	--color-tertiary-contrast-900: var(--color-tertiary-contrast-light);
	--color-tertiary-contrast-950: var(--color-tertiary-contrast-light);
	--color-success-50: oklch(97.91% 0.02 122.93deg);
	--color-success-100: oklch(95.42% 0.03 124.42deg);
	--color-success-200: oklch(93.05% 0.05 124.33deg);
	--color-success-300: oklch(90.79% 0.07 125.56deg);
	--color-success-400: oklch(88.44% 0.08 125.56deg);
	--color-success-500: oklch(86% 0.1 126.06deg);
	--color-success-600: oklch(79.24% 0.09 126.15deg);
	--color-success-700: oklch(72.62% 0.08 125.76deg);
	--color-success-800: oklch(65.55% 0.07 125.83deg);
	--color-success-900: oklch(58.58% 0.06 125.25deg);
	--color-success-950: oklch(51.08% 0.05 125.23deg);
	--color-success-contrast-dark: var(--color-success-950);
	--color-success-contrast-light: var(--color-success-50);
	--color-success-contrast-50: var(--color-success-contrast-dark);
	--color-success-contrast-100: var(--color-success-contrast-dark);
	--color-success-contrast-200: var(--color-success-contrast-dark);
	--color-success-contrast-300: var(--color-success-contrast-dark);
	--color-success-contrast-400: var(--color-success-contrast-dark);
	--color-success-contrast-500: var(--color-success-contrast-dark);
	--color-success-contrast-600: var(--color-success-contrast-dark);
	--color-success-contrast-700: var(--color-success-contrast-dark);
	--color-success-contrast-800: var(--color-success-contrast-light);
	--color-success-contrast-900: var(--color-success-contrast-light);
	--color-success-contrast-950: var(--color-success-contrast-light);
	--color-warning-50: oklch(97.3% 0.02 91.54deg);
	--color-warning-100: oklch(94.27% 0.04 92.65deg);
	--color-warning-200: oklch(91.16% 0.07 91.18deg);
	--color-warning-300: oklch(88.22% 0.09 91.76deg);
	--color-warning-400: oklch(85.22% 0.11 90.74deg);
	--color-warning-500: oklch(82.4% 0.13 90.68deg);
	--color-warning-600: oklch(76.03% 0.12 90.46deg);
	--color-warning-700: oklch(69.53% 0.1 90deg);
	--color-warning-800: oklch(62.98% 0.09 91.35deg);
	--color-warning-900: oklch(56.16% 0.08 90.88deg);
	--color-warning-950: oklch(49.1% 0.07 90.52deg);
	--color-warning-contrast-dark: var(--color-warning-950);
	--color-warning-contrast-light: var(--color-warning-50);
	--color-warning-contrast-50: var(--color-warning-contrast-dark);
	--color-warning-contrast-100: var(--color-warning-contrast-dark);
	--color-warning-contrast-200: var(--color-warning-contrast-dark);
	--color-warning-contrast-300: var(--color-warning-contrast-dark);
	--color-warning-contrast-400: var(--color-warning-contrast-dark);
	--color-warning-contrast-500: var(--color-warning-contrast-dark);
	--color-warning-contrast-600: var(--color-warning-contrast-dark);
	--color-warning-contrast-700: var(--color-warning-contrast-light);
	--color-warning-contrast-800: var(--color-warning-contrast-light);
	--color-warning-contrast-900: var(--color-warning-contrast-light);
	--color-warning-contrast-950: var(--color-warning-contrast-light);
	--color-error-50: oklch(95.25% 0.01 17.52deg);
	--color-error-100: oklch(89.78% 0.03 15.11deg);
	--color-error-200: oklch(84.37% 0.05 16.51deg);
	--color-error-300: oklch(79.12% 0.06 17.38deg);
	--color-error-400: oklch(73.81% 0.08 18.33deg);
	--color-error-500: oklch(68.53% 0.1 18.56deg);
	--color-error-600: oklch(63.39% 0.1 18.38deg);
	--color-error-700: oklch(58.01% 0.09 18.09deg);
	--color-error-800: oklch(52.61% 0.08 18.9deg);
	--color-error-900: oklch(46.97% 0.07 18.6deg);
	--color-error-950: oklch(41.28% 0.06 18.29deg);
	--color-error-contrast-dark: var(--color-error-950);
	--color-error-contrast-light: var(--color-error-50);
	--color-error-contrast-50: var(--color-error-contrast-dark);
	--color-error-contrast-100: var(--color-error-contrast-dark);
	--color-error-contrast-200: var(--color-error-contrast-dark);
	--color-error-contrast-300: var(--color-error-contrast-dark);
	--color-error-contrast-400: var(--color-error-contrast-dark);
	--color-error-contrast-500: var(--color-error-contrast-dark);
	--color-error-contrast-600: var(--color-error-contrast-light);
	--color-error-contrast-700: var(--color-error-contrast-light);
	--color-error-contrast-800: var(--color-error-contrast-light);
	--color-error-contrast-900: var(--color-error-contrast-light);
	--color-error-contrast-950: var(--color-error-contrast-light);
	--color-surface-50: oklch(98.46% 0 247.73deg);
	--color-surface-100: oklch(86.68% 0.01 268.61deg);
	--color-surface-200: oklch(74.27% 0.01 277.03deg);
	--color-surface-300: oklch(61.63% 0.02 279.32deg);
	--color-surface-400: oklch(48.01% 0.03 280.46deg);
	--color-surface-500: oklch(33.64% 0.04 278.24deg);
	--color-surface-600: oklch(31.53% 0.04 277.53deg);
	--color-surface-700: oklch(29.13% 0.03 279.24deg);
	--color-surface-800: oklch(26.99% 0.03 278.68deg);
	--color-surface-900: oklch(24.49% 0.03 281.07deg);
	--color-surface-950: oklch(22.2% 0.02 280.45deg);
	--color-surface-contrast-dark: var(--color-surface-950);
	--color-surface-contrast-light: var(--color-surface-50);
	--color-surface-contrast-50: var(--color-surface-contrast-dark);
	--color-surface-contrast-100: var(--color-surface-contrast-dark);
	--color-surface-contrast-200: var(--color-surface-contrast-dark);
	--color-surface-contrast-300: var(--color-surface-contrast-dark);
	--color-surface-contrast-400: var(--color-surface-contrast-light);
	--color-surface-contrast-500: var(--color-surface-contrast-light);
	--color-surface-contrast-600: var(--color-surface-contrast-light);
	--color-surface-contrast-700: var(--color-surface-contrast-light);
	--color-surface-contrast-800: var(--color-surface-contrast-light);
	--color-surface-contrast-900: var(--color-surface-contrast-light);
	--color-surface-contrast-950: var(--color-surface-contrast-light);
}
