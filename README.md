# daptapgo.io

<p align="center">
  <img src=".github/assets/logo.png" />
</p>

## Build Status

| Service | Status                                                                                                               |
| ------- | -------------------------------------------------------------------------------------------------------------------- |
| Master  | ![CI/CD](https://github.com/phigital-loyalty/daptapgo.io/workflows/Continuous%20Deployment/badge.svg?branch=main)    |
| Develop | ![CI/CD](https://github.com/phigital-loyalty/daptapgo.io/workflows/Continuous%20Deployment/badge.svg?branch=develop) |

This project was generated using [`create-svelte`](https://github.com/sveltejs/kit/tree/master/packages/create-svelte).

## Local setup

```bash
# install dependencies
npm i
```

## Developing

```bash
npm start

# or start the server and open the app in a new browser tab
npm start -- --open
```

## Building

To create a production version of your app:

```bash
npm run build
```

You can preview the production build with `npm run preview`.

## Further help

[Svelte](https://svelte.dev/docs/introduction)

[SvelteKit](https://kit.svelte.dev/docs/introduction)

[Skeleton Ui](https://www.skeleton.dev/docs/get-started)

[Tailwind](https://tailwindcss.com/docs/utility-first)

[Auth.js](https://authjs.dev/reference/sveltekit)
