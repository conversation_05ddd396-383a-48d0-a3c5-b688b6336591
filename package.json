{"name": "daptap", "version": "0.0.1", "private": true, "type": "module", "scripts": {"start": "NODE_TLS_REJECT_UNAUTHORIZED=0 vite dev", "dev": "NODE_TLS_REJECT_UNAUTHORIZED=0 vite dev", "build": "vite build", "preview": "vite preview", "test": "npm run test:integration && npm run test:unit", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint . --fix", "format": "prettier --write .", "test:integration": "playwright test", "test:unit": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest", "pre-commit": "lint-staged", "commit": "cz", "prepare": "husky", "update": "npx npm-check-updates -u --install always", "prestart": "npm run generate", "generate": "npm run generate:gateway && npm run generate:subscriptions", "generate:gateway": "graphql-codegen --config config/graphql-codegen/gateway.yml", "generate:subscriptions": "graphql-codegen --config config/graphql-codegen/subscriptions.yml", "cloudflare:deploy": "npm run cloudflare:deploy:develop", "cloudflare:deploy:develop": "CF_PAGES=true vite build --mode develop && npx wrangler pages deploy .svelte-kit/cloudflare --branch develop --project-name daptapgo-io", "cloudflare:deploy:stage": "CF_PAGES=true vite build --mode staging && npx wrangler pages deploy .svelte-kit/cloudflare --branch stage --project-name daptapgo-io", "cloudflare:deploy:main": "CF_PAGES=true vite build --mode main && npx wrangler pages deploy .svelte-kit/cloudflare --branch main --project-name daptapgo-io"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/cz-commitlint": "^19.8.1", "@graphql-codegen/add": "^5.0.3", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/fragment-matcher": "^5.1.0", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-apollo-client-helpers": "^3.0.1", "@graphql-codegen/typescript-operations": "^4.6.1", "@playwright/test": "^1.54.1", "@skeletonlabs/skeleton": "^3.1.7", "@skeletonlabs/skeleton-svelte": "^1.3.1", "@stylistic/eslint-plugin": "^5.2.1", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/adapter-cloudflare": "^7.1.0", "@sveltejs/eslint-config": "^8.3.3", "@sveltejs/kit": "^2.25.1", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@tailwindcss/forms": "0.5.10", "@tailwindcss/typography": "0.5.16", "@tailwindcss/vite": "^4.1.11", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "@testing-library/user-event": "^14.6.1", "@types/beyonk__gdpr-cookie-consent-banner": "^9.0.4", "@types/gtag.js": "^0.0.20", "@types/node": "24.0.15", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "10.4.21", "commitizen": "^4.3.1", "eslint": "^9.31.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "globals": "^16.3.0", "graphql-codegen-svelte-apollo": "^1.1.0", "happy-dom": "^18.0.1", "husky": "^9.1.7", "inquirer": "^12.8.2", "lint-staged": "^16.1.2", "mdsvex": "^0.12.6", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "rehype-slug": "^6.0.0", "super-sitemap": "^1.0.4", "svelte": "^5.36.13", "svelte-check": "^4.3.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tslib": "^2.8.1", "typescript": "^5.8.3", "vite": "^7.0.5", "vite-plugin-devtools-json": "^0.3.0", "vite-plugin-mkcert": "^1.17.8", "vitest": "^3.2.4"}, "dependencies": {"@apollo/client": "^3.13.8", "@auth/core": "^0.40.0", "@auth/sveltekit": "^1.10.0", "@floating-ui/dom": "1.7.2", "@svelte-plugins/datepicker": "^1.0.11", "@zag-js/svelte": "^1.19.0", "@zag-js/toggle-group": "^1.19.0", "crypto-hash": "^3.1.0", "date-fns": "^4.1.0", "echarts": "^5.6.0", "echarts-stat": "^1.2.0", "es-cookie": "^1.5.0", "graphql": "^16.11.0", "graphql-ws": "^6.0.6", "i18n-iso-countries": "^7.14.0", "iconify-icon": "^3.0.0", "just-clone": "^6.2.0", "runed": "^0.31.0", "svelte-apollo": "^0.5.0", "udsv": "^0.7.3"}, "overrides": {"svelte-apollo": {"svelte": "$svelte"}, "@commitlint/cz-commitlint": {"inquirer": "$inquirer"}}, "lint-staged": {"*": "prettier --write --ignore-unknown", "*.{js,ts,svelte}": "eslint --fix"}}