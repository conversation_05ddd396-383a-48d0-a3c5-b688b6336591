These instructions apply to the frontend of the project.

# Project Context

- Framework: Svelte 5 with SvelteKit
- Styling: TailwindCSS V4 with PostCSS
- Components: Skeleton V3, Runed, and Iconify
- Data Layer: GraphQL with Svelte Apollo (schema at ./src/lib/graphql/generated/gateway/schema.json)
- Use Svelte 5 runes for state management and reactivity
- Prefer existing modules/components in `src/lib/modules` and `src/lib/components`
- Do not edit files in `src/lib/graphql/generated`
- For directory structure, architecture, code style, and workflow, see `.github/instructions/seed.instructions.md`
- For Svelte 5 migration and runes, see `.github/instructions/svelte-5-migration.instructions.md`
- Reference Tailwind, Skeleton, and Runed docs as needed
- Project Structure: Use the canonical SvelteKit file structure

For directory structure and ownership, see `instructions/seed.instructions.md`.

## Internal Components & Modules

### Key Modules (from src/lib/modules)

- **AppShell**: Main application layout wrapper
- **Dashboard**: Dashboard components and analytics
- **DataTable**: Enhanced data table with sorting, filtering, etc.
- **Form**: Form building utilities including FormBuilder, FormField, etc.
- **Table**: Base table components (Table, TableHead, TableRow, etc.)
- **FilterBar**: Filtering components
- **Search**: Search functionality components

### Reusable Components (from src/lib/components)

- **SideBarMenu**: Navigation components including AppRailAnchor
- **Backoffice**: Admin-specific components
- **Forms**: Form-related components
- **Logo**: Brand elements
- **Drawer**: Slide-out panels
- **Footer**: Application footer

When implementing features, prefer using these existing modules and components rather than creating new ones.

# Code Style

- Follow the project's existing Prettier configuration:
- Use tabs for indentation
- Use single quotes
- Omit semicolons
- 120 character line width
- Follow ESLint rules for imports:
- Group imports by type with newlines between groups
- Alphabetize imports
- Do not add global function error handling unless already present
- Use TypeScript for type safety

## Clean Code Principles

- Code should be easy to read and understand.
- Keep the code as simple as possible. Avoid unnecessary complexity.
- Use meaningful names for variables, functions, etc. Names should reveal intent.
- Functions should be small and do one thing well.
- Function names should describe the action being performed.
- Prefer fewer arguments in functions.
- Only use comments when necessary, as they can become outdated. Instead, strive to make the code self-explanatory.
- When comments are used, they should add useful information that is not readily apparent from the code itself.
- Properly handle errors and exceptions to ensure the software's robustness.
- Use exceptions rather than error codes for handling errors.
- Consider security implications of the code. Implement security best practices to protect against vulnerabilities and attacks.

# Testing Philosophy

- Prioritize integration tests over isolated unit tests
- Focus on testing critical functionality without excessive complexity
- Test internal components through higher-level integration tests
- Use real implementations where feasible, minimizing mocks
- Only mock external dependencies when necessary
- Write tests for actual use cases rather than implementation details
- Avoid temporary workarounds or mocking to force test results

# Formatting & Conventions

- Use Commitlint conventional commit messages
- Respect the project's Tailwind configuration
- Always run code through Prettier before submitting

## Commit Message Examples

```
feat(component): add user authentication
fix(core): update import statements to use type imports
chore: update dependencies and migrate eslint configs to ESM
refactor(database): simplify query construction
docs: update README with new installation steps
test: add integration tests for auth workflow
```

# Environment Configuration

The project uses different environment configurations:

- Local development: `.env.local`
- Development environment: `.env.develop`
- Staging environment: `.env.staging`
- Production environment: `.env.main`

Key environment variables include:

- `VITE_ENVIRONMENT`: Current environment (local, develop, stage, production)
- `VITE_OIDC_ISSUER`: Authentication provider URL
- `VITE_GATEWAY_URL`: GraphQL API endpoint
- `VITE_SUBSCRIPTIONS_URL`: GraphQL subscriptions endpoint
- `VITE_SUBSCRIPTIONS_WS_URL`: WebSocket endpoint for subscriptions

# General Guidelines

- Maintain consistent code structure and style across the project
- Provide rationale for any significant logic changes
- Ask questions when facing uncertainty about implementation approaches
- Use Svelte runes ($state, $derived, etc.) for state management
- Leverage the project's Apollo GraphQL setup for data fetching

# Development Tools & Infrastructure

- Package Manager: npm
- Testing: Playwright (integration), Vitest (unit)
- GraphQL Codegen: Used for generating types from GraphQL schemas
- Linting: ESLint with custom configuration
- Formatting: Prettier with project-specific rules
- CI/CD: Deployment to Cloudflare Pages
- Version Control: Git with conventional commits

---

For project structure and merge policy, see `instructions/seed.instructions.md`.
For Svelte 5 migration and runes, see `instructions/svelte-5-migration.instructions.md`.
