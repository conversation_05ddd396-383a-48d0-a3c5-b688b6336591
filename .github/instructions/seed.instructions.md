---
description: Seed Project Integration Instructions (Frontend)
---

These instructions define how to work with the Seed project and project-specific code in this repository. They are optimized for LLMs and Copilot. Follow all rules and patterns below.

# Directory Ownership

- **Seed-only directories** are maintained upstream in the Seed project and merged into this repository. Do not add project-specific logic to these directories.
- **Project-specific directories** are unique to this repository. Add custom features and logic here.
- When adding new shared functionality, prefer the Seed project if it is generic and reusable.
- Treat `src/lib/modules` as Seed-only. It may be extracted to an external library in the future.
- The `src/lib/graphql/generated` directory is auto-generated. Do not edit files here manually.

# Directory Structure

```
src/
  lib/
    actions/         # Svelte actions (seed and project-specific)
    blog/            # Blog posts and related logic (seed and project-specific)
    components/      # Reusable Svelte components (seed and project-specific)
    graphql/         # GraphQL-related code (seed and project-specific)
      generated/     # Auto-generated GraphQL types (do not edit)
    links.ts         # Links and navigation (seed and project-specific)
    modals/          # Modal Svelte components (seed and project-specific)
    modules/         # Seed-only UI modules (treat as upstream)
    runes/           # runes/utilities (seed and project-specific)
    services/        # Data services (seed and project-specific)
    shared/          # Shared utilities and types (seed and project-specific)
      forms/         # Shared forms logic (seed and project-specific)

  routes/
    (app)/             # App-specific pages (seed and project-specific)
      org/             # Organization settings pages (mostly seed)
      settings/        # User settings (mostly seed)
    (auth)/            # Authentication pages (seed only)
    (markdown)/        # Markdown pages (seed only)
    (marketing)/       # Marketing pages (mostly project-specific)
    backoffice/        # Admin-specific pages
      [[orgId]]/
        organizations/ # Organization-specific pages (mostly seed)
        settings/      # Organization settings (mostly seed)
        users/         # Users and user management (mostly seed)
```

# Architecture Patterns

- **Modules**: UI modules in `src/lib/modules` are Seed-only and should not contain project-specific logic.
- **Components**: Reusable Svelte components should be placed in `src/lib/components`. Prefer using existing modules/components before creating new ones.
- **GraphQL**: Use Svelte Apollo and the auto-generated types in `src/lib/graphql/generated` for all GraphQL operations.
- **State Management**: Use Svelte 5 runes (`$state`, `$derived`, etc.) for reactivity and state.
- **Styling**: Use TailwindCSS v4 and follow the project's Prettier and ESLint configurations.
- **Routing**: Use SvelteKit's file-based routing in `src/routes` for both Seed and project-specific pages.

# Development Workflow

- Keep Seed and project-specific changes in separate commits. Do not mix them.
- When merging from Seed, resolve conflicts by favoring upstream changes unless a project-specific override is required. Document overrides in the commit message.
- When adding new core functionality, prefer the Seed project if it is generic and reusable.
- When adding project-specific features, keep them isolated from Seed code to minimize merge conflicts.
- Document any changes that affect both Seed and project-specific code.

# Do/Don't Rules

- **Do**: Use Seed-only directories for generic, reusable code.
- **Do**: Use project-specific directories for custom logic.
- **Do**: Keep commits for Seed and project-specific changes separate.
- **Don't**: Add project-specific logic to Seed-only directories (especially `src/lib/modules`).
- **Don't**: Mix Seed and project-specific changes in a single commit.
- **Don't**: Manually edit files in `src/lib/graphql/generated`.

# FAQ

- **Should this code go in Seed or project-specific?**
  - If it is generic and reusable, put it in Seed (especially in `src/lib/modules` or shared libs).
  - If it is unique to this project, keep it project-specific.
- **How do I upstream changes?**
  - Create a separate commit or PR with only Seed-related changes and submit it to the Seed repository.
- **How do I merge Seed updates?**
  - Regularly pull from the Seed repository and resolve any conflicts, keeping Seed and project-specific changes separate.

---

For more details, see the Seed project documentation: https://github.com/reality-connect/seed
