---
description: Svelte 5 vs Svelte 4 Migration and Usage Reference
---

This file provides migration notes and usage patterns for Svelte 5 runes, event handling, and component structure. Reference this for Svelte 5-specific syntax and best practices.

# Overview of Changes

Svelte 5 introduces runes, a set of advanced primitives for controlling reactivity. The runes replace certain non-runes features and provide more explicit control over state and effects.

Snippets, along with render tags, help create reusable chunks of markup inside your components, reducing duplication and enhancing maintainability.

# Event Handlers in Svelte 5

In Svelte 5, event handlers are treated as standard HTML properties rather than Svelte-specific directives, simplifying their use and integrating them more closely with the rest of the properties in the component.

## Svelte 4 vs. Svelte 5:

**Before (Svelte 4):**

```html
<script lang="ts">
	let count: Number = 0
	$: double = count * 2
	$: {
		if (count > 10) alert('Too high!')
	}
</script>
<button on:click="{()" ="">count++}> {count} / {double}</button>
```

**After (Svelte 5):**

```html
<script lang="ts">
	import { $state, $effect, $derived } from 'svelte'

	// Define state with runes
	let count = $state<Number>(0)

	// Option 1: Using $derived for computed values
	let double = $derived<Number>(count * 2)

	// Reactive effects using runes
	$effect(() => {
		if (count > 10) alert('Too high!')
	})
</script>

<!-- Standard HTML event attributes instead of Svelte directives -->
<button onclick="{()" ="">count++}> {count} / {double}</button>

<!-- Alternatively, you can compute values inline -->
<!-- <button onclick={() => count++}>
  {count} / {count * 2}
</button> -->
```

# Key Differences:

1. **Reactivity is Explicit**:
   - Svelte 5 uses `$state()` to explicitly mark reactive variables
   - `$derived()` replaces `$:` for computed values
   - `$effect()` replaces `$: {}` blocks for side effects

2. **Event Handling is Standardized**:
   - Svelte 4: `on:click={handler}`
   - Svelte 5: `onclick={handler}`

3. **Import Runes**:
   - All runes must be imported from 'svelte': `import { $state, $effect, $derived, $props, $slots } from 'svelte';`

4. **No More Event Modifiers**:
   - Svelte 4: `on:click|preventDefault={handler}`
   - Svelte 5: `onclick={e => { e.preventDefault(); handler(e); }}`

This creates clearer, more maintainable components compared to Svelte 4's previous syntax by making reactivity explicit and using standardized web platform features.

# Documentation

You can find more information about Svelte 5 and runes in the following resources:

- [Svelte 5 Migration Guide](https://svelte.dev/docs/svelte/v5-migration-guide)
- [Svelte 5 Runes Documentation](https://svelte-5-preview.vercel.app/docs/runes)

# Usage Examples

## Svelte Runes

- `$state`: Declare reactive state
  ```typescript
  let count = $state<Number>(0)
  ```
- `$derived`: Compute derived values
  ```typescript
  let doubled = $derived<Number>(count * 2)
  ```
- `$effect`: Manage side effects and lifecycle
  ```typescript
  $effect(() => {
  	console.log(`Count is now ${count}`)
  })
  ```
- `$props`: Declare component props
  ```typescript
  let { optionalProp = 42, requiredProp }: { optionalProp?: number; requiredProp: string } = $props()
  ```
- `$bindable`: Create two-way bindable props
  ```typescript
  let { bindableProp = $bindable() }: { bindableProp?: number } = $props()
  ```
- `$inspect`: Debug reactive state (development only)
  ```typescript
  $inspect(count)
  ```

# State Management

- Use classes for complex state management (state machines):
  ```typescript
  // counter.svelte.ts
  class Counter {
  	count = $state<Number>(0)
  	incrementor = $state<Number>(1)
  	increment() {
  		this.count += this.incrementor
  	}
  	resetCount() {
  		this.count = 0
  	}
  	resetIncrementor() {
  		this.incrementor = 1
  	}
  }
  export const counter = new Counter()
  ```
- Use in components:
  ```svelte
  <br />
  import { counter } from './counter.svelte.ts';
  <br />
  <button onclick={() => counter.increment()}>
    Count: {counter.count}
  ```
