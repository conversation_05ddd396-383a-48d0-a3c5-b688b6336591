name: Continuous Deployment

on:
  push:
    branches: [main, develop, stage]
    tags-ignore: ['**']

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: '24'

jobs:
  deploy-cloudflare:
    name: Deploy to Cloudflare Pages
    runs-on: ubuntu-latest
    permissions:
      contents: read
      deployments: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: 'https://npm.pkg.github.com'
          scope: '@reality-connect'
          always-auth: true

      - name: Install Dependencies
        run: npm ci && npx playwright install
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Lint
        run: npm run lint

      - name: Svelte Check
        run: npm run check

      - name: Unit Test
        run: npm run test:unit

      - name: Integration Test
        run: npm run test:integration

      - name: Build
        run: CF_PAGES=true npx vite build --mode ${GITHUB_REF#refs/heads/}

      - name: Publish to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ vars.CLOUDFLARE_ACCOUNT_ID }}
          projectName: ${{ vars.CLOUDFLARE_PROJECT_NAME }}
          directory: .svelte-kit/cloudflare
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
