name: EKS

on:
  push:
    branches: [develop, stage, main]
    paths:
      - './eks'
      - '.github/workflows/eks-workflow.yml'
      - '!**/*.md'
      - '!.*ignore'

  pull_request:
    paths:
      - './eks'
      - '.github/workflows/eks-workflow.yml'
      - '!**/*.md'
      - '!.*ignore'

permissions:
  actions: write
  id-token: write
  contents: write
  packages: write

jobs:
  plan:
    name: Plan
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./eks

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: mcblair/configure-aws-profile-action@v1.0.0
        with:
          role-arn: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/github-oidc-provider-aws
          region: ${{ vars.AWS_DEFAULT_REGION }}
          profile-name: phigitalloyalty

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3

      - name: Terraform Init
        id: tf-init
        run: |
          terraform init

      # Created the appropriate Terraform Worspace based on the target branch's name
      # Note main branch will create a production workspace named production
      - name: Set Terraform Workspace
        run: |
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            if [[ "${{ github.event.pull_request.base.ref }}" == "develop" ]]; then
              BRANCH_NAME="develop"
            elif [[ "${{ github.event.pull_request.base.ref }}" == "stage" ]]; then
              BRANCH_NAME="stage"
            elif [[ "${{ github.event.pull_request.base.ref }}" == "main" ]]; then
              BRANCH_NAME="production"
            fi
          else
            BRANCH_NAME=$(echo "${GITHUB_REF#refs/heads/}")
          fi

          if [[ "${BRANCH_NAME}" =~ ^(develop|stage|production)$ ]]; then
            echo "Branch name: $BRANCH_NAME"
            terraform workspace select -or-create $BRANCH_NAME
          else
            echo "Not a develop, stage, or main branch, skipping workspace setup."
          fi

      - name: Terraform Plan
        id: tf-plan
        run: |
          terraform plan -no-color -input=false

  deploy:
    name: Deploy
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./eks

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: mcblair/configure-aws-profile-action@v1.0.0
        with:
          role-arn: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/github-oidc-provider-aws
          region: ${{ vars.AWS_DEFAULT_REGION }}
          profile-name: phigitalloyalty

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3

      - name: Terraform Init
        id: tf-init
        run: |
          terraform init

      # Created the appropriate Terraform Worspace based on the target branch's name
      # Note main branch will create a production workspace named production
      - name: Set Terraform Workspace
        run: |
          if [[ "${{ github.event_name }}" == "push" ]]; then
            if [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
              BRANCH_NAME="develop"
            elif [[ "${{ github.ref }}" == "refs/heads/stage" ]]; then
              BRANCH_NAME="stage"
            elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
              BRANCH_NAME="production"
            fi

            if [[ "${BRANCH_NAME}" =~ ^(develop|stage|production)$ ]]; then
              echo "Branch name: $BRANCH_NAME"
              terraform workspace select -or-create $BRANCH_NAME
            else
              echo "Not a develop, stage, or main branch, skipping workspace setup."
            fi
          fi

      - name: Terraform Apply
        id: tf-apply
        run: |
          terraform apply -auto-approve
