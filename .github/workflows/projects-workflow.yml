name: Projects

on:
  push:
    branches: [main]
    paths:
      - './projects'
      - '.github/workflows/projects-workflow.yml'
      - '!**/*.md'
      - '!.*ignore'

  pull_request:
    paths:
      - './projects'
      - '.github/workflows/projects-workflow.yml'
      - '!**/*.md'
      - '!.*ignore'

permissions:
  actions: write
  id-token: write
  contents: write
  packages: write

jobs:
  plan:
    name: Plan
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./projects

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: mcblair/configure-aws-profile-action@v1.0.0
        with:
          role-arn: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/github-oidc-provider-aws
          region: ${{ vars.AWS_DEFAULT_REGION }}
          profile-name: phigitalloyalty

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3

      - name: Terraform Init
        id: tf-init
        run: |
          terraform init
          terraform workspace select default

      - name: Terraform Plan
        id: tf-plan
        run: |
          terraform plan -no-color -input=false

  deploy:
    name: Deploy
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./projects

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: mcblair/configure-aws-profile-action@v1.0.0
        with:
          role-arn: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/github-oidc-provider-aws
          region: ${{ vars.AWS_DEFAULT_REGION }}
          profile-name: phigitalloyalty

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3

      - name: Terraform Init
        id: tf-init
        run: |
          terraform init
          terraform workspace select default

      - name: Terraform Apply
        id: tf-applyphigitalloyalty
        run: |
          terraform apply -auto-approve
