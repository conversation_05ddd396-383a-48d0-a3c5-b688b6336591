import { expect, test } from '@playwright/test'

test('/sitemap.xml is valid', async ({ page }) => {
	const response = await page.goto('/sitemap.xml')
	expect(response?.status()).toBe(200)

	// Ensure XML is valid. <PERSON><PERSON> parses the XML here and will error if it cannot be parsed.
	const urls = await page.$$eval('url', (urls) =>
		urls.map((url) => ({
			loc: url.querySelector('loc')?.textContent,
		})),
	)

	// Sanity check
	expect(urls.length).toBeGreaterThan(5)

	// Ensure entries are in a valid format.
	for (const url of urls) {
		expect(url.loc).toBeTruthy()
		expect(() => new URL(url.loc!)).not.toThrow()
	}
})
