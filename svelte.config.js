import adapter from '@sveltejs/adapter-cloudflare'
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte'
import { mdsvex } from 'mdsvex'

import mdsvexConfig from './mdsvex.config.js'

/** @type {import('@sveltejs/kit').Config} */
const config = {
	extensions: ['.svelte', ...mdsvexConfig.extensions],

	// Consult https://kit.svelte.dev/docs/integrations#preprocessors
	// for more information about preprocessors
	preprocess: [vitePreprocess(), mdsvex(mdsvexConfig)],

	vitePlugin: {
		inspector: true,
	},

	kit: {
		adapter: adapter({
			routes: {
				include: ['/*'],
				exclude: ['/images/*', '/img/*', '/icons/*', '/favicon.*', '<build>', '<prerendered>'],
			},
		}),
	},
}
export default config
