# AGENTS.md

## Build, Lint, and Test Commands

- **Install dependencies:** `npm install`
- **Start dev server:** `npm start` or `npm run dev`
- **Build:** `npm run build`
- **Preview build:** `npm run preview`
- **Lint:** `npm run lint`
- **Format:** `npm run format`
- **Typecheck:** `npm run check`
- **Test all:** `npm test`
- **Integration tests:** `npm run test:integration` (Playwright)
- **Unit tests:** `npm run test:unit` (Vitest)
- **Run a single unit test:** `vitest run path/to/file.test.ts`
- **Test UI/watch:** `npm run test:ui` or `npm run test:watch`

## Code Style Guidelines

- **Formatting:** Use Prettier (tabs, single quotes, no semicolons, 120-char lines)
- **Imports:** Group by type, alphabetize, add newlines between groups
- **Types:** Use TypeScript everywhere
- **Naming:** Use meaningful, descriptive names; functions should do one thing
- **Error Handling:** Use exceptions, not error codes; do not add global error handling unless present
- **Comments:** Minimize; code should be self-explanatory
- **Testing:** Prefer integration tests, minimize mocks, test real use cases
- **Svelte:** Use runes for state, prefer existing modules/components
- **Commit messages:** Use conventional commits (see .github/copilot-instructions.md for examples)
- **Do not edit generated GraphQL files**

See `.github/copilot-instructions.md` for more details on architecture, style, and workflow.
