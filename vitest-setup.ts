import '@testing-library/jest-dom/vitest'
import { PropertySymbol } from 'happy-dom'

const browserWindow = global.document[PropertySymbol.window]

global.setTimeout = browserWindow.setTimeout
global.clearTimeout = browserWindow.clearTimeout
global.setInterval = browserWindow.setInterval
global.clearInterval = browserWindow.clearInterval
global.requestAnimationFrame = browserWindow.requestAnimationFrame
global.cancelAnimationFrame = browserWindow.cancelAnimationFrame
global.queueMicrotask = browserWindow.queueMicrotask

// Mock Web Animations API for testing
if (!Element.prototype.animate) {
	Element.prototype.animate = function () {
		return {
			addEventListener: () => {
				/* no-op */
			},
			removeEventListener: () => {
				/* no-op */
			},
			finish: () => {
				/* no-op */
			},
			cancel: () => {
				/* no-op */
			},
			pause: () => {
				/* no-op */
			},
			play: () => {
				/* no-op */
			},
			reverse: () => {
				/* no-op */
			},
			playbackRate: 1,
			startTime: 0,
			currentTime: 0,
			playState: 'finished',
			pending: false,
			ready: Promise.resolve(),
			finished: Promise.resolve(),
		} as never
	}
}
