import js from '@eslint/js'
import prettier from 'eslint-config-prettier'
import { flatConfigs } from 'eslint-plugin-import'
import svelte from 'eslint-plugin-svelte'
import globals from 'globals'
import { configs, parser } from 'typescript-eslint'

import svelteConfig from './svelte.config.js'

/** @type {import('eslint').Linter.Config[]} */
export default [
	{
		ignores: [
			// .gitignore
			'test-results',
			'node_modules',
			'.output',
			'.vercel',
			'.netlify',
			'.wrangler',
			'.svelte-kit',
			'build',
			'.DS_Store',
			'Thumbs.db',
			'.env',
			'.env.*',
			'!.env.example',
			'!.env.test',
			'vite.config.js.timestamp-*',
			'vite.config.ts.timestamp-*',
			'src/lib/paraglide',
			'*storybook.log',
			'storybook-static',
			'.vscode/*',
			'!.vscode/settings.json',
			'!.vscode/tasks.json',
			'!.vscode/launch.json',
			'!.vscode/extensions.json',
			'!.vscode/mcp.json',
			'.qodo',
			'.aider*',
			'!.aider.conf.yml',

			// eslint
			'commitlint.config.cjs',
			'eslint.config.mjs',
			'svelte.config.js',
			'playwright.config.ts',
			'mdsvex.config.js',
			'vite.config.ts',
			'vitest-setup.ts',

			// extra
			'**/generated/**/*',
			'**/deprecated/**/*',
		],
	},
	js.configs.recommended,
	...configs.recommended,
	...svelte.configs.recommended,
	prettier,
	...svelte.configs.prettier,
	flatConfigs.recommended,
	flatConfigs.typescript,
	{
		languageOptions: {
			globals: { ...globals.browser, ...globals.node },
		},
		rules: {
			// typescript-eslint strongly recommend that you do not use the no-undef lint rule on TypeScript projects.
			// see: https://typescript-eslint.io/troubleshooting/faqs/eslint/#i-get-errors-from-the-no-undef-rule-about-global-variables-not-being-defined-even-though-there-are-no-typescript-errors
			'no-undef': 'off',
		},
	},
	{
		languageOptions: {
			parserOptions: {
				projectService: true,
				ecmaVersion: 'latest',
				extraFileExtensions: ['.svelte'],
				parser: parser,
				svelteConfig,
			},
		},

		settings: {
			'import/internal-regex': '$lib',
			'import/resolver': {
				typescript: {
					alwaysTryTypes: true,
				},
				node: true,
			},
		},

		rules: {
			'no-duplicate-imports': 'off', // Allows importing types and code separately
			'svelte/no-at-html-tags': 'off', // For markdown

			'no-console': ['error', { allow: ['warn', 'error'] }],

			'@typescript-eslint/ban-ts-comment': 'error',
			'@typescript-eslint/no-empty-function': 'error',
			'@typescript-eslint/no-explicit-any': 'error',
			'@typescript-eslint/no-this-alias': 'error',

			'@typescript-eslint/no-empty-object-type': 'error',
			'@typescript-eslint/no-unsafe-function-type': 'error',
			'@typescript-eslint/no-wrapper-object-types': 'error',

			'@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],

			'import/no-unresolved': [
				'error',
				{
					ignore: [
						'^\\$app/(environment|forms|navigation|paths|store|state)$',
						'^\\$env/(static|dynamic)/(private|public)$',
						'^\\$service-worker$',
					],
				},
			],

			'import/order': [
				'error',
				{
					groups: ['builtin', 'external', 'internal', ['sibling', 'parent', 'index'], 'object', 'type'],
					pathGroups: [
						{
							pattern: '$app/**',
							group: 'external',
							position: 'after',
						},
						{
							pattern: '**/*.svelte',
							group: 'internal',
							position: 'after',
						},
					],
					'newlines-between': 'always',
					warnOnUnassignedImports: true,
					alphabetize: {
						order: 'asc',
						caseInsensitive: true,
					},
				},
			],

			// Reenable once <https://github.com/import-js/eslint-plugin-import/issues/1479> is fixed.
			'import/no-duplicates': 'off',
			'import/namespace': 'off',
		},
	},
	{
		files: ['*.svelte', '*.svelte.ts', '*.svelte.js'],
		languageOptions: {
			parserOptions: {
				projectService: true,
				ecmaVersion: 'latest',
				extraFileExtensions: ['.svelte'],
				parser: parser,
				svelteConfig,
			},
		},
	},
]
