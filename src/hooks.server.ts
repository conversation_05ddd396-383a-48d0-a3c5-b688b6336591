import { type Handle, redirect } from '@sveltejs/kit'
import { sequence } from '@sveltejs/kit/hooks'

import { appPath, authPathsRegex, customDomainPathsRegex, internalUrls } from '$lib/config'
import { AsyncOrganizationFromCustomDomain } from '$lib/graphql/generated/gateway'
import { handle as auth } from '$lib/server/auth-provider'

if (process.env.NODE_ENV === 'development') {
	try {
		const originalEmitWarning = process.emitWarning

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		process.emitWarning = (warning: string | Error, ...args: any) => {
			if (typeof warning === 'string' && warning.includes('NODE_TLS_REJECT_UNAUTHORIZED')) {
				// node will only emit the warning once
				// https://github.com/nodejs/node/blob/82f89ec8c1554964f5029fab1cf0f4fad1fa55a8/lib/_tls_wrap.js#L1378-L1384
				process.emitWarning = originalEmitWarning

				return
			}

			return originalEmitWarning.call(process, warning, ...args)
		}
	} catch {
		// Do Nothing
	}
}

const session: Handle = async ({ event, resolve }) => {
	event.locals.session = (await event.locals.auth()) as unknown as App.CustomSession

	if (!event.locals.session && event.url.pathname !== '/login' && authPathsRegex.test(event.url.pathname)) {
		redirect(303, '/login?postLoginRedirect=' + encodeURIComponent(event.url.toString().replace(event.url.origin, '')))
	}

	if (event.locals.session?.error === 'RefreshAccessTokenError' && event.url.pathname !== '/signin') {
		redirect(303, '/signin?postLoginRedirect=' + encodeURIComponent(event.url.toString().replace(event.url.origin, '')))
	}

	return resolve(event)
}

const customDomain: Handle = async ({ event, resolve }) => {
	if (event.url.origin !== 'http://sveltekit-prerender' && !internalUrls.includes(event.url.host) && event.url.host) {
		event.locals.customDomain = event.url.host

		if (!event.locals.session || event.locals.session.error) {
			if (event.locals.session?.error === 'RefreshAccessTokenError' || event.url.pathname === '/signin') {
				return resolve(event)
			}

			redirect(
				303,
				'/signin?postLoginRedirect=' + encodeURIComponent(event.url.toString().replace(event.url.origin, '')),
			)
		}

		if (appPath === '' && event.url.pathname !== '/') {
			if (!customDomainPathsRegex.test(event.url.pathname)) {
				redirect(303, `/${appPath}`)
			}
		}

		const { data } = await AsyncOrganizationFromCustomDomain({
			variables: {
				domain: event.locals.customDomain,
			},
			...(event.locals.session?.access_token && {
				context: {
					session: event.locals.session,
				},
			}),
		})

		if (!data?.oidcClient) {
			redirect(
				303,
				'/unauthorized?postLoginRedirect=' + encodeURIComponent(event.url.toString().replace(event.url.origin, '')),
			)
		}

		event.locals.organizationId = data.oidcClient.organizationId
		event.locals.organization = data.oidcClient.organization
	}

	return resolve(event)
}

export const handle = sequence(auth, session, customDomain)
