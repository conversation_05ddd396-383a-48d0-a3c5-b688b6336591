// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces

// and what to do when importing types
declare namespace App {
	interface Locals {
		session?: CustomSession
		customDomain?: string
		organizationId?: string
		organization?: OrganizationFromCustomDomainQuery['oidcClient']['organization']
	}
	interface PageData {
		session?: CustomSession
		customDomain?: string
		organizationId?: string
		organization?: OrganizationFromCustomDomainQuery['oidcClient']['organization']
	}
	// interface Error {}
	// interface Platform {}

	interface MdsvexFile {
		default: import('svelte/internal').SvelteComponent
		metadata: Record<string, string>
	}

	type MdsvexResolver = () => Promise<MdsvexFile>

	interface BlogPost {
		slug: string
		title: string
		author: string
		description: string
		date: string
		published: boolean
	}

	interface CustomSession {
		user_id: string
		access_token: string
		expires_at: number
		id_token: string
		user?: DefaultSession['user']
		error?: string
	}
}
