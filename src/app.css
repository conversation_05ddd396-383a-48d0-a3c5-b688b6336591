@import 'tailwindcss';
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

@import '@skeletonlabs/skeleton';
@import '@skeletonlabs/skeleton/optional/presets';

/*
  Add your theme import for your theme: "custom-theme" here
*/
/* @import '@skeletonlabs/skeleton/themes/crimson'; */
@import '../custom-theme';

@source '../node_modules/@skeletonlabs/skeleton-svelte/dist';

@custom-variant dark (&:where([data-mode="dark"], [data-mode="dark"] *));

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}
}

html,
body {
	height: 100dvh;
	scroll-behavior: smooth;
}

/* Local Utilities --- */

.flex-centered {
	display: flex;
	align-items: center;
	justify-content: center;
}

/* https://stackoverflow.com/a/13996191 */
.bg-noise {
	background-image: url(data:image/png;base64,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);
	background-blend-mode: overlay;
}

/* Floating UI */
/* https://floating-ui.com/docs/tutorial#making-the-tooltip-float */
/* https://floating-ui-svelte.vercel.app/docs/getting-started */

.floating {
	width: max-content;
	position: absolute;
	top: 0;
	left: 0;
}

/* useTransition - https://floating-ui.com/docs/useTransition#usetransitionstatus */
.floating {
	transition-property: opacity;
}
.floating[data-status='open'],
.floating[data-status='close'] {
	transition-duration: 250ms;
}
.floating[data-status='initial'],
.floating[data-status='close'] {
	opacity: 0;
}

*:focus-visible {
	outline: none;
}

iconify-icon {
	display: inline-block;
	min-width: 1rem;
	min-height: 1rem;
}

iconify-icon[width='20'] {
	min-width: 1rem;
}
iconify-icon[height='20'] {
	min-height: 1rem;
}

iconify-icon[width='24'] {
	min-width: 1.25rem;
}
iconify-icon[height='24'] {
	min-height: 1.25rem;
}

.btn {
	iconify-icon {
		min-width: 1rem;
		min-height: 1rem;
	}
}

.btn-sm {
	iconify-icon {
		min-width: 0.75rem;
		min-height: 0.75rem;
	}
}

.btn-lg {
	iconify-icon {
		min-width: 1.25rem;
		min-height: 1.25rem;
	}
}

.btn-icon {
	iconify-icon {
		min-width: 1.5rem;
		min-height: 1.5rem;
	}
}

/* Override Skeleton's styles */
.btn-icon {
	width: auto;
	height: auto;
}

/* Fix for Tabs - https://github.com/skeletonlabs/skeleton/pull/3436 */
button[data-scope='tabs'] .btn {
	&:hover {
		@media (hover: hover) {
			@apply filter-none;
		}
	}
}
