import SideBarMenu from '$lib/components/SideBarMenu'
import { AnalyticsToolbox } from '$lib/modules/Dashboard'
import { drawer, type ModalComponentConfig, type ModalPresetProps } from '$lib/modules/Modal'
import NotificationsDrawer from '$lib/modules/Notifications'

import BackofficeSideBarMenu from '$lib/components/Backoffice/BackofficeSideBarMenu.svelte'

import ConfirmModal from './ConfirmModal.svelte'

export const modalComponentRegistry: Record<string, ModalPresetProps & ModalComponentConfig> = {
	'confirm-list': { component: ConfirmModal },

	// Drawers
	'side-menu': {
		component: SideBarMenu,
		...drawer('left'),
	},
	'side-menu-backoffice': {
		component: BackofficeSideBarMenu,
		...drawer('left'),
	},
	// 'side-menu-project': {
	// 	component: SideBarMenu,
	// 	...drawer('left'),
	// },
	notifications: {
		component: NotificationsDrawer,
		...drawer('right'),
		width: 'w-full md:w-[700px]',
		rounded: 'rounded-xl',
		margin: 'p-4',
	},
	invitations: {
		component: NotificationsDrawer,
		props: {
			mode: 'invitations',
		},
		...drawer('right'),
		width: 'w-full md:w-[700px]',
		rounded: 'rounded-xl',
		margin: 'p-4',
	},
	'analytics-toolbox': {
		component: AnalyticsToolbox,
		...drawer('right'),
	},
	// 'manage-oidc-client': {
	// 	component: ManageOidcClient,
	// 	...drawer('right'),
	// },
	// 'add-admin': {
	// 	component: AddAdmin,
	// 	...drawer('right'),
	// },
	// 'manage-invitation': {
	// 	component: ManageInvitation,
	// 	...drawer('right'),
	// },
}
