<script lang="ts">
	import { ModalComponent, type ModalComponentProps } from '$lib/modules/Modal'
	import ToggleGroup from '$lib/modules/ToggleGroup'

	const {
		closeLabel,
		closeClasses,
		submitLabel,
		submitClasses,
		onSubmit,
		onClose,
		meta,
		...rest
	}: ModalComponentProps = $props()

	let selected = $state<string[]>([])

	if (meta?.items) {
		selected = meta.items.map((item: { label: string; value: string }) => item.value)
	}
</script>

<ModalComponent {...rest} {onClose}>
	{#if meta?.items?.length}
		<ToggleGroup
			value={selected}
			onValueChange={(e) => {
				if (meta?.disabled) return
				selected = e.value
			}}
			classes="border-surface-500 rounded-container border p-4 w-full"
			orientation="vertical"
			multiple
			disabled={meta?.disabled}
		>
			{#each meta?.items as item (item.value)}
				<ToggleGroup.Item value={item.value}>{item.label}</ToggleGroup.Item>
			{/each}
		</ToggleGroup>
	{:else}
		<p class="p-4">No items to select.</p>
	{/if}

	{#snippet footer()}
		<button class="btn {closeClasses}" onclick={onClose}>{closeLabel}</button>
		<button class="btn {submitClasses}" onclick={() => onSubmit?.(selected)}>{submitLabel}</button>
	{/snippet}
</ModalComponent>
