<script lang="ts">
	import { ModalComponent, type ModalComponentProps } from '$lib/modules/Modal'

	import { clipboard } from '$lib/actions/clipboard.svelte'

	const {
		clientId,
		clientSecret,

		title,

		...rest
	}: ModalComponentProps & {
		clientId: string
		clientSecret: string
	} = $props()
</script>

<ModalComponent {...rest}>
	{#snippet header()}
		{title ?? 'New Application'}
	{/snippet}

	{#snippet article()}
		<p class="mb-6">Make sure to copy your new client secret now. You won't be able to see it again.</p>

		<div>
			<p class="font-bold">
				Client ID
				<button class="btn-icon focus:outline-0" use:clipboard={clientId} aria-label="Copy Client ID">
					<iconify-icon icon="radix-icons:copy" inline></iconify-icon>
				</button>
			</p>

			<p class="break-all">
				{clientId}
			</p>
		</div>

		<div class="mt-4">
			<p class="font-bold">
				Client Secret
				<button class="btn-icon focus:outline-0" use:clipboard={clientSecret} aria-label="Copy Client Secret">
					<iconify-icon icon="radix-icons:copy" inline></iconify-icon>
				</button>
			</p>

			<p class="break-all">
				{clientSecret}
			</p>
		</div>
	{/snippet}
</ModalComponent>
