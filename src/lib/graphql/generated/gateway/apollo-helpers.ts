import type { FieldPolicy, FieldReadFunction, TypePolicies, TypePolicy } from '@apollo/client/cache'
export type AccountKeySpecifier = (
	| 'business_profile'
	| 'business_type'
	| 'capabilities'
	| 'charges_enabled'
	| 'company'
	| 'controller'
	| 'country'
	| 'created'
	| 'default_currency'
	| 'deleted'
	| 'details_submitted'
	| 'email'
	| 'external_accounts'
	| 'future_requirements'
	| 'id'
	| 'individual'
	| 'metadata'
	| 'object'
	| 'payouts_enabled'
	| 'requirements'
	| 'settings'
	| 'tos_acceptance'
	| 'type'
	| AccountKeySpecifier
)[]
export type AccountFieldPolicy = {
	business_profile?: FieldPolicy<any> | FieldReadFunction<any>
	business_type?: FieldPolicy<any> | FieldReadFunction<any>
	capabilities?: FieldPolicy<any> | FieldReadFunction<any>
	charges_enabled?: FieldPolicy<any> | FieldReadFunction<any>
	company?: FieldPolicy<any> | FieldReadFunction<any>
	controller?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	default_currency?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	details_submitted?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	external_accounts?: FieldPolicy<any> | FieldReadFunction<any>
	future_requirements?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	individual?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	payouts_enabled?: FieldPolicy<any> | FieldReadFunction<any>
	requirements?: FieldPolicy<any> | FieldReadFunction<any>
	settings?: FieldPolicy<any> | FieldReadFunction<any>
	tos_acceptance?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingCardPaymentsKeySpecifier = (
	| 'decline_on'
	| 'statement_descriptor_prefix'
	| 'statement_descriptor_prefix_kana'
	| 'statement_descriptor_prefix_kanji'
	| AccountSettingCardPaymentsKeySpecifier
)[]
export type AccountSettingCardPaymentsFieldPolicy = {
	decline_on?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor_prefix?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor_prefix_kana?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor_prefix_kanji?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingCardPaymentsDeclineOnKeySpecifier = (
	| 'avs_failure'
	| 'cvc_failure'
	| AccountSettingCardPaymentsDeclineOnKeySpecifier
)[]
export type AccountSettingCardPaymentsDeclineOnFieldPolicy = {
	avs_failure?: FieldPolicy<any> | FieldReadFunction<any>
	cvc_failure?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingDashboardKeySpecifier = ('display_name' | 'timezone' | AccountSettingDashboardKeySpecifier)[]
export type AccountSettingDashboardFieldPolicy = {
	display_name?: FieldPolicy<any> | FieldReadFunction<any>
	timezone?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingPaymentsKeySpecifier = (
	| 'statement_descriptor'
	| 'statement_descriptor_kana'
	| 'statement_descriptor_kanji'
	| 'statement_descriptor_prefix_kana'
	| 'statement_descriptor_prefix_kanji'
	| AccountSettingPaymentsKeySpecifier
)[]
export type AccountSettingPaymentsFieldPolicy = {
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor_kana?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor_kanji?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor_prefix_kana?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor_prefix_kanji?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingPayoutsKeySpecifier = (
	| 'debit_negative_balances'
	| 'schedule'
	| 'statement_descriptor'
	| AccountSettingPayoutsKeySpecifier
)[]
export type AccountSettingPayoutsFieldPolicy = {
	debit_negative_balances?: FieldPolicy<any> | FieldReadFunction<any>
	schedule?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingPayoutsScheduleKeySpecifier = (
	| 'delay_days'
	| 'interval'
	| 'monthly_anchor'
	| 'weekly_anchor'
	| AccountSettingPayoutsScheduleKeySpecifier
)[]
export type AccountSettingPayoutsScheduleFieldPolicy = {
	delay_days?: FieldPolicy<any> | FieldReadFunction<any>
	interval?: FieldPolicy<any> | FieldReadFunction<any>
	monthly_anchor?: FieldPolicy<any> | FieldReadFunction<any>
	weekly_anchor?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingSepaDebitPaymentsKeySpecifier = (
	| 'creditor_id'
	| AccountSettingSepaDebitPaymentsKeySpecifier
)[]
export type AccountSettingSepaDebitPaymentsFieldPolicy = {
	creditor_id?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingTreasuryKeySpecifier = ('tos_acceptance' | AccountSettingTreasuryKeySpecifier)[]
export type AccountSettingTreasuryFieldPolicy = {
	tos_acceptance?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingsKeySpecifier = (
	| 'bacs_debit_payments'
	| 'branding'
	| 'card_issuing'
	| 'card_payments'
	| 'dashboard'
	| 'payments'
	| 'payouts'
	| 'sepa_debit_payments'
	| 'treasury'
	| AccountSettingsKeySpecifier
)[]
export type AccountSettingsFieldPolicy = {
	bacs_debit_payments?: FieldPolicy<any> | FieldReadFunction<any>
	branding?: FieldPolicy<any> | FieldReadFunction<any>
	card_issuing?: FieldPolicy<any> | FieldReadFunction<any>
	card_payments?: FieldPolicy<any> | FieldReadFunction<any>
	dashboard?: FieldPolicy<any> | FieldReadFunction<any>
	payments?: FieldPolicy<any> | FieldReadFunction<any>
	payouts?: FieldPolicy<any> | FieldReadFunction<any>
	sepa_debit_payments?: FieldPolicy<any> | FieldReadFunction<any>
	treasury?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingsBacsDebitPaymentsKeySpecifier = (
	| 'display_name'
	| 'service_user_number'
	| AccountSettingsBacsDebitPaymentsKeySpecifier
)[]
export type AccountSettingsBacsDebitPaymentsFieldPolicy = {
	display_name?: FieldPolicy<any> | FieldReadFunction<any>
	service_user_number?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingsBrandingKeySpecifier = (
	| 'icon'
	| 'logo'
	| 'primary_color'
	| 'secondary_color'
	| AccountSettingsBrandingKeySpecifier
)[]
export type AccountSettingsBrandingFieldPolicy = {
	icon?: FieldPolicy<any> | FieldReadFunction<any>
	logo?: FieldPolicy<any> | FieldReadFunction<any>
	primary_color?: FieldPolicy<any> | FieldReadFunction<any>
	secondary_color?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingsCardIssuingKeySpecifier = ('tos_acceptance' | AccountSettingsCardIssuingKeySpecifier)[]
export type AccountSettingsCardIssuingFieldPolicy = {
	tos_acceptance?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AccountSettingsCardIssuingTosAcceptanceKeySpecifier = (
	| 'date'
	| 'ip'
	| 'user_agent'
	| AccountSettingsCardIssuingTosAcceptanceKeySpecifier
)[]
export type AccountSettingsCardIssuingTosAcceptanceFieldPolicy = {
	date?: FieldPolicy<any> | FieldReadFunction<any>
	ip?: FieldPolicy<any> | FieldReadFunction<any>
	user_agent?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AchCreditTransferKeySpecifier = (
	| 'account_number'
	| 'bank_name'
	| 'fingerprint'
	| 'refund_account_holder_name'
	| 'refund_account_holder_type'
	| 'refund_routing_number'
	| 'routing_number'
	| 'swift_code'
	| AchCreditTransferKeySpecifier
)[]
export type AchCreditTransferFieldPolicy = {
	account_number?: FieldPolicy<any> | FieldReadFunction<any>
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_name?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_type?: FieldPolicy<any> | FieldReadFunction<any>
	refund_routing_number?: FieldPolicy<any> | FieldReadFunction<any>
	routing_number?: FieldPolicy<any> | FieldReadFunction<any>
	swift_code?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AchDebitKeySpecifier = (
	| 'bank_name'
	| 'country'
	| 'fingerprint'
	| 'last4'
	| 'routing_number'
	| 'type'
	| AchDebitKeySpecifier
)[]
export type AchDebitFieldPolicy = {
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	routing_number?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AcssDebitKeySpecifier = (
	| 'bank_name'
	| 'fingerprint'
	| 'institution_number'
	| 'last4'
	| 'transit_number'
	| AcssDebitKeySpecifier
)[]
export type AcssDebitFieldPolicy = {
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	institution_number?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	transit_number?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ActivationKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'date'
	| 'deletedAt'
	| 'description'
	| 'filter'
	| 'filterProducts'
	| 'filterPrograms'
	| 'limit'
	| 'location'
	| 'name'
	| 'numberOfParticipants'
	| 'organization'
	| 'organizationId'
	| 'participantIds'
	| 'participants'
	| 'picture'
	| 'runFrom'
	| 'runTo'
	| 'type'
	| 'updatedAt'
	| 'url'
	| ActivationKeySpecifier
)[]
export type ActivationFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	date?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	filter?: FieldPolicy<any> | FieldReadFunction<any>
	filterProducts?: FieldPolicy<any> | FieldReadFunction<any>
	filterPrograms?: FieldPolicy<any> | FieldReadFunction<any>
	limit?: FieldPolicy<any> | FieldReadFunction<any>
	location?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	numberOfParticipants?: FieldPolicy<any> | FieldReadFunction<any>
	organization?: FieldPolicy<any> | FieldReadFunction<any>
	organizationId?: FieldPolicy<any> | FieldReadFunction<any>
	participantIds?: FieldPolicy<any> | FieldReadFunction<any>
	participants?: FieldPolicy<any> | FieldReadFunction<any>
	picture?: FieldPolicy<any> | FieldReadFunction<any>
	runFrom?: FieldPolicy<any> | FieldReadFunction<any>
	runTo?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ActivationFilterKeySpecifier = (
	| 'countries'
	| 'labels'
	| 'productIds'
	| 'programIds'
	| 'registeredMaxDate'
	| 'registeredMinDate'
	| ActivationFilterKeySpecifier
)[]
export type ActivationFilterFieldPolicy = {
	countries?: FieldPolicy<any> | FieldReadFunction<any>
	labels?: FieldPolicy<any> | FieldReadFunction<any>
	productIds?: FieldPolicy<any> | FieldReadFunction<any>
	programIds?: FieldPolicy<any> | FieldReadFunction<any>
	registeredMaxDate?: FieldPolicy<any> | FieldReadFunction<any>
	registeredMinDate?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AddressKeySpecifier = (
	| 'country'
	| 'formatted'
	| 'locality'
	| 'postalCode'
	| 'region'
	| 'streetAddress'
	| AddressKeySpecifier
)[]
export type AddressFieldPolicy = {
	country?: FieldPolicy<any> | FieldReadFunction<any>
	formatted?: FieldPolicy<any> | FieldReadFunction<any>
	locality?: FieldPolicy<any> | FieldReadFunction<any>
	postalCode?: FieldPolicy<any> | FieldReadFunction<any>
	region?: FieldPolicy<any> | FieldReadFunction<any>
	streetAddress?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AddressKanaKeySpecifier = (
	| 'city'
	| 'country'
	| 'line1'
	| 'line2'
	| 'postal_code'
	| 'state'
	| 'town'
	| AddressKanaKeySpecifier
)[]
export type AddressKanaFieldPolicy = {
	city?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	line1?: FieldPolicy<any> | FieldReadFunction<any>
	line2?: FieldPolicy<any> | FieldReadFunction<any>
	postal_code?: FieldPolicy<any> | FieldReadFunction<any>
	state?: FieldPolicy<any> | FieldReadFunction<any>
	town?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AddressKanjiKeySpecifier = (
	| 'city'
	| 'country'
	| 'line1'
	| 'line2'
	| 'postal_code'
	| 'state'
	| 'town'
	| AddressKanjiKeySpecifier
)[]
export type AddressKanjiFieldPolicy = {
	city?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	line1?: FieldPolicy<any> | FieldReadFunction<any>
	line2?: FieldPolicy<any> | FieldReadFunction<any>
	postal_code?: FieldPolicy<any> | FieldReadFunction<any>
	state?: FieldPolicy<any> | FieldReadFunction<any>
	town?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AdminsKeySpecifier = (
	| '_id'
	| 'additionalEmail'
	| 'additionalEmailVerified'
	| 'address'
	| 'birthdate'
	| 'blocked'
	| 'blockedIds'
	| 'contactEmail'
	| 'contactEmailVerified'
	| 'createdAt'
	| 'deletedAt'
	| 'email'
	| 'emailVerified'
	| 'familyName'
	| 'followIds'
	| 'followers'
	| 'following'
	| 'friendRequests'
	| 'friends'
	| 'gender'
	| 'givenName'
	| 'identities'
	| 'installations'
	| 'isBlocked'
	| 'isFollower'
	| 'isFollowing'
	| 'isFriend'
	| 'locale'
	| 'middleName'
	| 'name'
	| 'nickname'
	| 'organizations'
	| 'phoneNumber'
	| 'phoneNumberVerified'
	| 'picture'
	| 'recoveryEmail'
	| 'recoveryEmailVerified'
	| 'roles'
	| 'specialBadge'
	| 'updatedAt'
	| 'website'
	| 'zoneinfo'
	| AdminsKeySpecifier
)[]
export type AdminsFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	additionalEmail?: FieldPolicy<any> | FieldReadFunction<any>
	additionalEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	address?: FieldPolicy<any> | FieldReadFunction<any>
	birthdate?: FieldPolicy<any> | FieldReadFunction<any>
	blocked?: FieldPolicy<any> | FieldReadFunction<any>
	blockedIds?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmail?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	emailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	familyName?: FieldPolicy<any> | FieldReadFunction<any>
	followIds?: FieldPolicy<any> | FieldReadFunction<any>
	followers?: FieldPolicy<any> | FieldReadFunction<any>
	following?: FieldPolicy<any> | FieldReadFunction<any>
	friendRequests?: FieldPolicy<any> | FieldReadFunction<any>
	friends?: FieldPolicy<any> | FieldReadFunction<any>
	gender?: FieldPolicy<any> | FieldReadFunction<any>
	givenName?: FieldPolicy<any> | FieldReadFunction<any>
	identities?: FieldPolicy<any> | FieldReadFunction<any>
	installations?: FieldPolicy<any> | FieldReadFunction<any>
	isBlocked?: FieldPolicy<any> | FieldReadFunction<any>
	isFollower?: FieldPolicy<any> | FieldReadFunction<any>
	isFollowing?: FieldPolicy<any> | FieldReadFunction<any>
	isFriend?: FieldPolicy<any> | FieldReadFunction<any>
	locale?: FieldPolicy<any> | FieldReadFunction<any>
	middleName?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	nickname?: FieldPolicy<any> | FieldReadFunction<any>
	organizations?: FieldPolicy<any> | FieldReadFunction<any>
	phoneNumber?: FieldPolicy<any> | FieldReadFunction<any>
	phoneNumberVerified?: FieldPolicy<any> | FieldReadFunction<any>
	picture?: FieldPolicy<any> | FieldReadFunction<any>
	recoveryEmail?: FieldPolicy<any> | FieldReadFunction<any>
	recoveryEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	roles?: FieldPolicy<any> | FieldReadFunction<any>
	specialBadge?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	website?: FieldPolicy<any> | FieldReadFunction<any>
	zoneinfo?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AdvancingKeySpecifier = ('target_frozen_time' | AdvancingKeySpecifier)[]
export type AdvancingFieldPolicy = {
	target_frozen_time?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AmountKeySpecifier = ('monetary' | 'type' | AmountKeySpecifier)[]
export type AmountFieldPolicy = {
	monetary?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AmountMonetaryKeySpecifier = ('currency' | 'value' | AmountMonetaryKeySpecifier)[]
export type AmountMonetaryFieldPolicy = {
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	value?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AnnualRevenueKeySpecifier = ('amount' | 'currency' | 'fiscal_year_end' | AnnualRevenueKeySpecifier)[]
export type AnnualRevenueFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	fiscal_year_end?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ApplicationKeySpecifier = ('deleted' | 'id' | 'name' | 'object' | ApplicationKeySpecifier)[]
export type ApplicationFieldPolicy = {
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ApplicationFeeKeySpecifier = (
	| 'account'
	| 'amount'
	| 'amount_refunded'
	| 'application'
	| 'balance_transaction'
	| 'charge'
	| 'created'
	| 'currency'
	| 'fee_source'
	| 'id'
	| 'livemode'
	| 'object'
	| 'originating_transaction'
	| 'refunded'
	| 'refunds'
	| ApplicationFeeKeySpecifier
)[]
export type ApplicationFeeFieldPolicy = {
	account?: FieldPolicy<any> | FieldReadFunction<any>
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_refunded?: FieldPolicy<any> | FieldReadFunction<any>
	application?: FieldPolicy<any> | FieldReadFunction<any>
	balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	fee_source?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	originating_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	refunded?: FieldPolicy<any> | FieldReadFunction<any>
	refunds?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AppliesToKeySpecifier = ('products' | AppliesToKeySpecifier)[]
export type AppliesToFieldPolicy = {
	products?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AuBecsDebitKeySpecifier = ('bsb_number' | 'fingerprint' | 'last4' | AuBecsDebitKeySpecifier)[]
export type AuBecsDebitFieldPolicy = {
	bsb_number?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AuditEventKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'entity'
	| 'entityIds'
	| 'fingerprint'
	| 'owners'
	| 'relations'
	| 'request'
	| 'updatedAt'
	| AuditEventKeySpecifier
)[]
export type AuditEventFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	entity?: FieldPolicy<any> | FieldReadFunction<any>
	entityIds?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	owners?: FieldPolicy<any> | FieldReadFunction<any>
	relations?: FieldPolicy<any> | FieldReadFunction<any>
	request?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type AutomaticPaymentMethodsKeySpecifier = ('enabled' | AutomaticPaymentMethodsKeySpecifier)[]
export type AutomaticPaymentMethodsFieldPolicy = {
	enabled?: FieldPolicy<any> | FieldReadFunction<any>
}
export type BacsDebitKeySpecifier = ('fingerprint' | 'last4' | 'sort_code' | BacsDebitKeySpecifier)[]
export type BacsDebitFieldPolicy = {
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	sort_code?: FieldPolicy<any> | FieldReadFunction<any>
}
export type BalanceTransactionKeySpecifier = (
	| 'amount'
	| 'available_on'
	| 'created'
	| 'currency'
	| 'description'
	| 'exchange_rate'
	| 'fee'
	| 'fee_details'
	| 'id'
	| 'net'
	| 'object'
	| 'reporting_category'
	| 'source'
	| 'status'
	| 'type'
	| BalanceTransactionKeySpecifier
)[]
export type BalanceTransactionFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	available_on?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	exchange_rate?: FieldPolicy<any> | FieldReadFunction<any>
	fee?: FieldPolicy<any> | FieldReadFunction<any>
	fee_details?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	net?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	reporting_category?: FieldPolicy<any> | FieldReadFunction<any>
	source?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type BankAccountKeySpecifier = (
	| 'account'
	| 'account_holder_name'
	| 'account_holder_type'
	| 'account_type'
	| 'available_payout_methods'
	| 'bank_name'
	| 'country'
	| 'currency'
	| 'customer'
	| 'default_for_currency'
	| 'fingerprint'
	| 'id'
	| 'last4'
	| 'metadata'
	| 'object'
	| 'routing_number'
	| 'status'
	| BankAccountKeySpecifier
)[]
export type BankAccountFieldPolicy = {
	account?: FieldPolicy<any> | FieldReadFunction<any>
	account_holder_name?: FieldPolicy<any> | FieldReadFunction<any>
	account_holder_type?: FieldPolicy<any> | FieldReadFunction<any>
	account_type?: FieldPolicy<any> | FieldReadFunction<any>
	available_payout_methods?: FieldPolicy<any> | FieldReadFunction<any>
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	default_for_currency?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	routing_number?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
}
export type BillingCycleAnchorConfigKeySpecifier = (
	| 'day_of_month'
	| 'hour'
	| 'minute'
	| 'month'
	| 'second'
	| BillingCycleAnchorConfigKeySpecifier
)[]
export type BillingCycleAnchorConfigFieldPolicy = {
	day_of_month?: FieldPolicy<any> | FieldReadFunction<any>
	hour?: FieldPolicy<any> | FieldReadFunction<any>
	minute?: FieldPolicy<any> | FieldReadFunction<any>
	month?: FieldPolicy<any> | FieldReadFunction<any>
	second?: FieldPolicy<any> | FieldReadFunction<any>
}
export type BillingDetailsKeySpecifier = ('address' | 'email' | 'name' | 'phone' | BillingDetailsKeySpecifier)[]
export type BillingDetailsFieldPolicy = {
	address?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	phone?: FieldPolicy<any> | FieldReadFunction<any>
}
export type BillingThresholdsKeySpecifier = (
	| 'amount_gte'
	| 'reset_billing_cycle_anchor'
	| BillingThresholdsKeySpecifier
)[]
export type BillingThresholdsFieldPolicy = {
	amount_gte?: FieldPolicy<any> | FieldReadFunction<any>
	reset_billing_cycle_anchor?: FieldPolicy<any> | FieldReadFunction<any>
}
export type BoletoKeySpecifier = ('tax_id' | BoletoKeySpecifier)[]
export type BoletoFieldPolicy = {
	tax_id?: FieldPolicy<any> | FieldReadFunction<any>
}
export type BusinessProfileKeySpecifier = (
	| 'annual_revenue'
	| 'estimated_worker_count'
	| 'mcc'
	| 'name'
	| 'product_description'
	| 'support_address'
	| 'support_email'
	| 'support_phone'
	| 'support_url'
	| 'url'
	| BusinessProfileKeySpecifier
)[]
export type BusinessProfileFieldPolicy = {
	annual_revenue?: FieldPolicy<any> | FieldReadFunction<any>
	estimated_worker_count?: FieldPolicy<any> | FieldReadFunction<any>
	mcc?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	product_description?: FieldPolicy<any> | FieldReadFunction<any>
	support_address?: FieldPolicy<any> | FieldReadFunction<any>
	support_email?: FieldPolicy<any> | FieldReadFunction<any>
	support_phone?: FieldPolicy<any> | FieldReadFunction<any>
	support_url?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CapabilitiesKeySpecifier = (
	| 'acss_debit_payments'
	| 'affirm_payments'
	| 'afterpay_clearpay_payments'
	| 'au_becs_debit_payments'
	| 'bacs_debit_payments'
	| 'bancontact_payments'
	| 'bank_transfer_payments'
	| 'boleto_payments'
	| 'card_issuing'
	| 'card_payments'
	| 'cartes_bancaires_payments'
	| 'eps_payments'
	| 'fpx_payments'
	| 'giropay_payments'
	| 'grabpay_payments'
	| 'ideal_payments'
	| 'jcb_payments'
	| 'klarna_payments'
	| 'konbini_payments'
	| 'legacy_payments'
	| 'link_payments'
	| 'oxxo_payments'
	| 'p24_payments'
	| 'paynow_payments'
	| 'sepa_debit_payments'
	| 'sofort_payments'
	| 'tax_reporting_us_1099_k'
	| 'tax_reporting_us_1099_misc'
	| 'transfers'
	| 'treasury'
	| 'us_bank_account_ach_payments'
	| CapabilitiesKeySpecifier
)[]
export type CapabilitiesFieldPolicy = {
	acss_debit_payments?: FieldPolicy<any> | FieldReadFunction<any>
	affirm_payments?: FieldPolicy<any> | FieldReadFunction<any>
	afterpay_clearpay_payments?: FieldPolicy<any> | FieldReadFunction<any>
	au_becs_debit_payments?: FieldPolicy<any> | FieldReadFunction<any>
	bacs_debit_payments?: FieldPolicy<any> | FieldReadFunction<any>
	bancontact_payments?: FieldPolicy<any> | FieldReadFunction<any>
	bank_transfer_payments?: FieldPolicy<any> | FieldReadFunction<any>
	boleto_payments?: FieldPolicy<any> | FieldReadFunction<any>
	card_issuing?: FieldPolicy<any> | FieldReadFunction<any>
	card_payments?: FieldPolicy<any> | FieldReadFunction<any>
	cartes_bancaires_payments?: FieldPolicy<any> | FieldReadFunction<any>
	eps_payments?: FieldPolicy<any> | FieldReadFunction<any>
	fpx_payments?: FieldPolicy<any> | FieldReadFunction<any>
	giropay_payments?: FieldPolicy<any> | FieldReadFunction<any>
	grabpay_payments?: FieldPolicy<any> | FieldReadFunction<any>
	ideal_payments?: FieldPolicy<any> | FieldReadFunction<any>
	jcb_payments?: FieldPolicy<any> | FieldReadFunction<any>
	klarna_payments?: FieldPolicy<any> | FieldReadFunction<any>
	konbini_payments?: FieldPolicy<any> | FieldReadFunction<any>
	legacy_payments?: FieldPolicy<any> | FieldReadFunction<any>
	link_payments?: FieldPolicy<any> | FieldReadFunction<any>
	oxxo_payments?: FieldPolicy<any> | FieldReadFunction<any>
	p24_payments?: FieldPolicy<any> | FieldReadFunction<any>
	paynow_payments?: FieldPolicy<any> | FieldReadFunction<any>
	sepa_debit_payments?: FieldPolicy<any> | FieldReadFunction<any>
	sofort_payments?: FieldPolicy<any> | FieldReadFunction<any>
	tax_reporting_us_1099_k?: FieldPolicy<any> | FieldReadFunction<any>
	tax_reporting_us_1099_misc?: FieldPolicy<any> | FieldReadFunction<any>
	transfers?: FieldPolicy<any> | FieldReadFunction<any>
	treasury?: FieldPolicy<any> | FieldReadFunction<any>
	us_bank_account_ach_payments?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CardKeySpecifier = (
	| 'account'
	| 'address_city'
	| 'address_country'
	| 'address_line1'
	| 'address_line1_check'
	| 'address_line2'
	| 'address_state'
	| 'address_zip'
	| 'address_zip_check'
	| 'available_payout_methods'
	| 'brand'
	| 'country'
	| 'currency'
	| 'customer'
	| 'cvc_check'
	| 'default_for_currency'
	| 'description'
	| 'dynamic_last4'
	| 'exp_month'
	| 'exp_year'
	| 'fingerprint'
	| 'funding'
	| 'id'
	| 'iin'
	| 'issuer'
	| 'last4'
	| 'metadata'
	| 'name'
	| 'object'
	| 'regulated_status'
	| 'status'
	| 'tokenization_method'
	| CardKeySpecifier
)[]
export type CardFieldPolicy = {
	account?: FieldPolicy<any> | FieldReadFunction<any>
	address_city?: FieldPolicy<any> | FieldReadFunction<any>
	address_country?: FieldPolicy<any> | FieldReadFunction<any>
	address_line1?: FieldPolicy<any> | FieldReadFunction<any>
	address_line1_check?: FieldPolicy<any> | FieldReadFunction<any>
	address_line2?: FieldPolicy<any> | FieldReadFunction<any>
	address_state?: FieldPolicy<any> | FieldReadFunction<any>
	address_zip?: FieldPolicy<any> | FieldReadFunction<any>
	address_zip_check?: FieldPolicy<any> | FieldReadFunction<any>
	available_payout_methods?: FieldPolicy<any> | FieldReadFunction<any>
	brand?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	cvc_check?: FieldPolicy<any> | FieldReadFunction<any>
	default_for_currency?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	dynamic_last4?: FieldPolicy<any> | FieldReadFunction<any>
	exp_month?: FieldPolicy<any> | FieldReadFunction<any>
	exp_year?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	funding?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	iin?: FieldPolicy<any> | FieldReadFunction<any>
	issuer?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	regulated_status?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	tokenization_method?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CardChecksKeySpecifier = (
	| 'address_line1_check'
	| 'address_postal_code_check'
	| 'cvc_check'
	| CardChecksKeySpecifier
)[]
export type CardChecksFieldPolicy = {
	address_line1_check?: FieldPolicy<any> | FieldReadFunction<any>
	address_postal_code_check?: FieldPolicy<any> | FieldReadFunction<any>
	cvc_check?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CardNetworksKeySpecifier = ('available' | 'preferred' | CardNetworksKeySpecifier)[]
export type CardNetworksFieldPolicy = {
	available?: FieldPolicy<any> | FieldReadFunction<any>
	preferred?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CardPresentKeySpecifier = (
	| 'brand'
	| 'brand_product'
	| 'cardholder_name'
	| 'country'
	| 'description'
	| 'exp_month'
	| 'exp_year'
	| 'fingerprint'
	| 'funding'
	| 'iin'
	| 'issuer'
	| 'last4'
	| 'networks'
	| 'offline'
	| 'preferred_locales'
	| 'read_method'
	| CardPresentKeySpecifier
)[]
export type CardPresentFieldPolicy = {
	brand?: FieldPolicy<any> | FieldReadFunction<any>
	brand_product?: FieldPolicy<any> | FieldReadFunction<any>
	cardholder_name?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	exp_month?: FieldPolicy<any> | FieldReadFunction<any>
	exp_year?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	funding?: FieldPolicy<any> | FieldReadFunction<any>
	iin?: FieldPolicy<any> | FieldReadFunction<any>
	issuer?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	networks?: FieldPolicy<any> | FieldReadFunction<any>
	offline?: FieldPolicy<any> | FieldReadFunction<any>
	preferred_locales?: FieldPolicy<any> | FieldReadFunction<any>
	read_method?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CardThreeDSecureUsageKeySpecifier = ('supported' | CardThreeDSecureUsageKeySpecifier)[]
export type CardThreeDSecureUsageFieldPolicy = {
	supported?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CardWalletKeySpecifier = (
	| 'amex_express_checkout'
	| 'apple_pay'
	| 'dynamic_last4'
	| 'google_pay'
	| 'masterpass'
	| 'samsung_pay'
	| 'type'
	| 'visa_checkout'
	| CardWalletKeySpecifier
)[]
export type CardWalletFieldPolicy = {
	amex_express_checkout?: FieldPolicy<any> | FieldReadFunction<any>
	apple_pay?: FieldPolicy<any> | FieldReadFunction<any>
	dynamic_last4?: FieldPolicy<any> | FieldReadFunction<any>
	google_pay?: FieldPolicy<any> | FieldReadFunction<any>
	masterpass?: FieldPolicy<any> | FieldReadFunction<any>
	samsung_pay?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	visa_checkout?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CardWalletMasterpassKeySpecifier = (
	| 'billing_address'
	| 'email'
	| 'name'
	| 'shipping_address'
	| CardWalletMasterpassKeySpecifier
)[]
export type CardWalletMasterpassFieldPolicy = {
	billing_address?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_address?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CardWalletVisaCheckoutKeySpecifier = (
	| 'billing_address'
	| 'email'
	| 'name'
	| 'shipping_address'
	| CardWalletVisaCheckoutKeySpecifier
)[]
export type CardWalletVisaCheckoutFieldPolicy = {
	billing_address?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_address?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CashBalanceKeySpecifier = (
	| 'available'
	| 'customer'
	| 'livemode'
	| 'object'
	| 'settings'
	| CashBalanceKeySpecifier
)[]
export type CashBalanceFieldPolicy = {
	available?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	settings?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CashBalanceSettingsKeySpecifier = (
	| 'reconciliation_mode'
	| 'using_merchant_default'
	| CashBalanceSettingsKeySpecifier
)[]
export type CashBalanceSettingsFieldPolicy = {
	reconciliation_mode?: FieldPolicy<any> | FieldReadFunction<any>
	using_merchant_default?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ChargeKeySpecifier = (
	| 'amount'
	| 'amount_captured'
	| 'amount_refunded'
	| 'application'
	| 'application_fee'
	| 'application_fee_amount'
	| 'authorization_code'
	| 'balance_transaction'
	| 'billing_details'
	| 'calculated_statement_descriptor'
	| 'captured'
	| 'created'
	| 'currency'
	| 'customer'
	| 'description'
	| 'destination'
	| 'disputed'
	| 'failure_balance_transaction'
	| 'failure_code'
	| 'failure_message'
	| 'fraud_details'
	| 'id'
	| 'invoice'
	| 'level3'
	| 'livemode'
	| 'metadata'
	| 'object'
	| 'on_behalf_of'
	| 'outcome'
	| 'paid'
	| 'payment_intent'
	| 'payment_method'
	| 'payment_method_details'
	| 'radar_options'
	| 'receipt_email'
	| 'receipt_number'
	| 'receipt_url'
	| 'refunded'
	| 'refunds'
	| 'review'
	| 'shipping'
	| 'source'
	| 'source_transfer'
	| 'statement_descriptor'
	| 'statement_descriptor_suffix'
	| 'status'
	| 'transfer'
	| 'transfer_data'
	| 'transfer_group'
	| ChargeKeySpecifier
)[]
export type ChargeFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_captured?: FieldPolicy<any> | FieldReadFunction<any>
	amount_refunded?: FieldPolicy<any> | FieldReadFunction<any>
	application?: FieldPolicy<any> | FieldReadFunction<any>
	application_fee?: FieldPolicy<any> | FieldReadFunction<any>
	application_fee_amount?: FieldPolicy<any> | FieldReadFunction<any>
	authorization_code?: FieldPolicy<any> | FieldReadFunction<any>
	balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	billing_details?: FieldPolicy<any> | FieldReadFunction<any>
	calculated_statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
	captured?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	destination?: FieldPolicy<any> | FieldReadFunction<any>
	disputed?: FieldPolicy<any> | FieldReadFunction<any>
	failure_balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	failure_code?: FieldPolicy<any> | FieldReadFunction<any>
	failure_message?: FieldPolicy<any> | FieldReadFunction<any>
	fraud_details?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	level3?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	on_behalf_of?: FieldPolicy<any> | FieldReadFunction<any>
	outcome?: FieldPolicy<any> | FieldReadFunction<any>
	paid?: FieldPolicy<any> | FieldReadFunction<any>
	payment_intent?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_details?: FieldPolicy<any> | FieldReadFunction<any>
	radar_options?: FieldPolicy<any> | FieldReadFunction<any>
	receipt_email?: FieldPolicy<any> | FieldReadFunction<any>
	receipt_number?: FieldPolicy<any> | FieldReadFunction<any>
	receipt_url?: FieldPolicy<any> | FieldReadFunction<any>
	refunded?: FieldPolicy<any> | FieldReadFunction<any>
	refunds?: FieldPolicy<any> | FieldReadFunction<any>
	review?: FieldPolicy<any> | FieldReadFunction<any>
	shipping?: FieldPolicy<any> | FieldReadFunction<any>
	source?: FieldPolicy<any> | FieldReadFunction<any>
	source_transfer?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor_suffix?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	transfer?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_data?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_group?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ChargeTransferDataKeySpecifier = ('amount' | 'destination' | ChargeTransferDataKeySpecifier)[]
export type ChargeTransferDataFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	destination?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ChipKeySpecifier = ('serialNumber' | 'type' | ChipKeySpecifier)[]
export type ChipFieldPolicy = {
	serialNumber?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CodeVerificationKeySpecifier = ('attempts_remaining' | 'status' | CodeVerificationKeySpecifier)[]
export type CodeVerificationFieldPolicy = {
	attempts_remaining?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CommentKeySpecifier = (
	| '_id'
	| 'body'
	| 'children'
	| 'createdAt'
	| 'deletedAt'
	| 'parent'
	| 'parentId'
	| 'totalChildren'
	| 'updatedAt'
	| 'userId'
	| CommentKeySpecifier
)[]
export type CommentFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	body?: FieldPolicy<any> | FieldReadFunction<any>
	children?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
	parentId?: FieldPolicy<any> | FieldReadFunction<any>
	totalChildren?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CompanyKeySpecifier = (
	| 'address'
	| 'address_kana'
	| 'address_kanji'
	| 'directors_provided'
	| 'executives_provided'
	| 'name'
	| 'name_kana'
	| 'name_kanji'
	| 'owners_provided'
	| 'ownership_declaration'
	| 'phone'
	| 'structure'
	| 'tax_id_provided'
	| 'tax_id_registrar'
	| 'vat_id_provided'
	| 'verification'
	| CompanyKeySpecifier
)[]
export type CompanyFieldPolicy = {
	address?: FieldPolicy<any> | FieldReadFunction<any>
	address_kana?: FieldPolicy<any> | FieldReadFunction<any>
	address_kanji?: FieldPolicy<any> | FieldReadFunction<any>
	directors_provided?: FieldPolicy<any> | FieldReadFunction<any>
	executives_provided?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	name_kana?: FieldPolicy<any> | FieldReadFunction<any>
	name_kanji?: FieldPolicy<any> | FieldReadFunction<any>
	owners_provided?: FieldPolicy<any> | FieldReadFunction<any>
	ownership_declaration?: FieldPolicy<any> | FieldReadFunction<any>
	phone?: FieldPolicy<any> | FieldReadFunction<any>
	structure?: FieldPolicy<any> | FieldReadFunction<any>
	tax_id_provided?: FieldPolicy<any> | FieldReadFunction<any>
	tax_id_registrar?: FieldPolicy<any> | FieldReadFunction<any>
	vat_id_provided?: FieldPolicy<any> | FieldReadFunction<any>
	verification?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CompanyVerificationKeySpecifier = ('document' | CompanyVerificationKeySpecifier)[]
export type CompanyVerificationFieldPolicy = {
	document?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CompanyVerificationDocumentKeySpecifier = (
	| 'back'
	| 'details'
	| 'details_code'
	| 'front'
	| CompanyVerificationDocumentKeySpecifier
)[]
export type CompanyVerificationDocumentFieldPolicy = {
	back?: FieldPolicy<any> | FieldReadFunction<any>
	details?: FieldPolicy<any> | FieldReadFunction<any>
	details_code?: FieldPolicy<any> | FieldReadFunction<any>
	front?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ComputedKeySpecifier = ('recurring' | 'upfront' | ComputedKeySpecifier)[]
export type ComputedFieldPolicy = {
	recurring?: FieldPolicy<any> | FieldReadFunction<any>
	upfront?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ComputedBreakdownKeySpecifier = ('discounts' | 'taxes' | ComputedBreakdownKeySpecifier)[]
export type ComputedBreakdownFieldPolicy = {
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	taxes?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ComputedBreakdownDiscountKeySpecifier = ('amount' | 'discount' | ComputedBreakdownDiscountKeySpecifier)[]
export type ComputedBreakdownDiscountFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	discount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ComputedBreakdownTaxKeySpecifier = (
	| 'amount'
	| 'rate'
	| 'taxability_reason'
	| 'taxable_amount'
	| ComputedBreakdownTaxKeySpecifier
)[]
export type ComputedBreakdownTaxFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	rate?: FieldPolicy<any> | FieldReadFunction<any>
	taxability_reason?: FieldPolicy<any> | FieldReadFunction<any>
	taxable_amount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ComputedRecurringKeySpecifier = (
	| 'amount_subtotal'
	| 'amount_total'
	| 'interval'
	| 'interval_count'
	| 'total_details'
	| ComputedRecurringKeySpecifier
)[]
export type ComputedRecurringFieldPolicy = {
	amount_subtotal?: FieldPolicy<any> | FieldReadFunction<any>
	amount_total?: FieldPolicy<any> | FieldReadFunction<any>
	interval?: FieldPolicy<any> | FieldReadFunction<any>
	interval_count?: FieldPolicy<any> | FieldReadFunction<any>
	total_details?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ComputedRecurringTotalDetailsKeySpecifier = (
	| 'amount_discount'
	| 'amount_shipping'
	| 'amount_tax'
	| 'breakdown'
	| ComputedRecurringTotalDetailsKeySpecifier
)[]
export type ComputedRecurringTotalDetailsFieldPolicy = {
	amount_discount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_shipping?: FieldPolicy<any> | FieldReadFunction<any>
	amount_tax?: FieldPolicy<any> | FieldReadFunction<any>
	breakdown?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ComputedUpfrontKeySpecifier = (
	| 'amount_subtotal'
	| 'amount_total'
	| 'line_items'
	| 'total_details'
	| ComputedUpfrontKeySpecifier
)[]
export type ComputedUpfrontFieldPolicy = {
	amount_subtotal?: FieldPolicy<any> | FieldReadFunction<any>
	amount_total?: FieldPolicy<any> | FieldReadFunction<any>
	line_items?: FieldPolicy<any> | FieldReadFunction<any>
	total_details?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ComputedUpfrontTotalDetailsKeySpecifier = (
	| 'amount_discount'
	| 'amount_shipping'
	| 'amount_tax'
	| 'breakdown'
	| ComputedUpfrontTotalDetailsKeySpecifier
)[]
export type ComputedUpfrontTotalDetailsFieldPolicy = {
	amount_discount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_shipping?: FieldPolicy<any> | FieldReadFunction<any>
	amount_tax?: FieldPolicy<any> | FieldReadFunction<any>
	breakdown?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ConnectCollectionTransferKeySpecifier = (
	| 'amount'
	| 'currency'
	| 'destination'
	| 'id'
	| 'livemode'
	| 'object'
	| ConnectCollectionTransferKeySpecifier
)[]
export type ConnectCollectionTransferFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	destination?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CouponKeySpecifier = (
	| 'amount_off'
	| 'applies_to'
	| 'created'
	| 'currency'
	| 'deleted'
	| 'duration'
	| 'duration_in_months'
	| 'id'
	| 'livemode'
	| 'max_redemptions'
	| 'metadata'
	| 'name'
	| 'object'
	| 'percent_off'
	| 'redeem_by'
	| 'times_redeemed'
	| 'valid'
	| CouponKeySpecifier
)[]
export type CouponFieldPolicy = {
	amount_off?: FieldPolicy<any> | FieldReadFunction<any>
	applies_to?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	duration?: FieldPolicy<any> | FieldReadFunction<any>
	duration_in_months?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	max_redemptions?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	percent_off?: FieldPolicy<any> | FieldReadFunction<any>
	redeem_by?: FieldPolicy<any> | FieldReadFunction<any>
	times_redeemed?: FieldPolicy<any> | FieldReadFunction<any>
	valid?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CreditBalanceTransactionKeySpecifier = (
	| 'created'
	| 'credit'
	| 'credit_grant'
	| 'debit'
	| 'effective_at'
	| 'id'
	| 'livemode'
	| 'object'
	| 'test_clock'
	| 'type'
	| CreditBalanceTransactionKeySpecifier
)[]
export type CreditBalanceTransactionFieldPolicy = {
	created?: FieldPolicy<any> | FieldReadFunction<any>
	credit?: FieldPolicy<any> | FieldReadFunction<any>
	credit_grant?: FieldPolicy<any> | FieldReadFunction<any>
	debit?: FieldPolicy<any> | FieldReadFunction<any>
	effective_at?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	test_clock?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CreditBalanceTransactionCreditCreditsApplicationInvoiceVoidedKeySpecifier = (
	| 'invoice'
	| 'invoice_line_item'
	| CreditBalanceTransactionCreditCreditsApplicationInvoiceVoidedKeySpecifier
)[]
export type CreditBalanceTransactionCreditCreditsApplicationInvoiceVoidedFieldPolicy = {
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_line_item?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CreditNoteKeySpecifier = (
	| 'amount'
	| 'amount_shipping'
	| 'created'
	| 'currency'
	| 'customer'
	| 'customer_balance_transaction'
	| 'discount_amount'
	| 'discount_amounts'
	| 'effective_at'
	| 'id'
	| 'invoice'
	| 'lines'
	| 'livemode'
	| 'memo'
	| 'metadata'
	| 'number'
	| 'object'
	| 'out_of_band_amount'
	| 'pdf'
	| 'pretax_credit_amounts'
	| 'reason'
	| 'refund'
	| 'shipping_cost'
	| 'status'
	| 'subtotal'
	| 'subtotal_excluding_tax'
	| 'tax_amounts'
	| 'total'
	| 'total_excluding_tax'
	| 'type'
	| 'voided_at'
	| CreditNoteKeySpecifier
)[]
export type CreditNoteFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_shipping?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	customer_balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	discount_amount?: FieldPolicy<any> | FieldReadFunction<any>
	discount_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	effective_at?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	lines?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	memo?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	number?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	out_of_band_amount?: FieldPolicy<any> | FieldReadFunction<any>
	pdf?: FieldPolicy<any> | FieldReadFunction<any>
	pretax_credit_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	reason?: FieldPolicy<any> | FieldReadFunction<any>
	refund?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_cost?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	subtotal?: FieldPolicy<any> | FieldReadFunction<any>
	subtotal_excluding_tax?: FieldPolicy<any> | FieldReadFunction<any>
	tax_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	total?: FieldPolicy<any> | FieldReadFunction<any>
	total_excluding_tax?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	voided_at?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CreditNoteLineItemKeySpecifier = (
	| 'amount'
	| 'amount_excluding_tax'
	| 'description'
	| 'discount_amount'
	| 'discount_amounts'
	| 'id'
	| 'invoice_line_item'
	| 'livemode'
	| 'object'
	| 'pretax_credit_amounts'
	| 'quantity'
	| 'tax_amounts'
	| 'tax_rates'
	| 'type'
	| 'unit_amount'
	| 'unit_amount_decimal'
	| 'unit_amount_excluding_tax'
	| CreditNoteLineItemKeySpecifier
)[]
export type CreditNoteLineItemFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_excluding_tax?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	discount_amount?: FieldPolicy<any> | FieldReadFunction<any>
	discount_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_line_item?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	pretax_credit_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	quantity?: FieldPolicy<any> | FieldReadFunction<any>
	tax_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	tax_rates?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	unit_amount?: FieldPolicy<any> | FieldReadFunction<any>
	unit_amount_decimal?: FieldPolicy<any> | FieldReadFunction<any>
	unit_amount_excluding_tax?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CreditNoteLineItemListKeySpecifier = (
	| 'data'
	| 'has_more'
	| 'object'
	| 'url'
	| CreditNoteLineItemListKeySpecifier
)[]
export type CreditNoteLineItemListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CurrencyOptionsKeySpecifier = ('amount' | 'tax_behavior' | CurrencyOptionsKeySpecifier)[]
export type CurrencyOptionsFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	tax_behavior?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CurrentPhaseKeySpecifier = ('end_date' | 'start_date' | CurrentPhaseKeySpecifier)[]
export type CurrentPhaseFieldPolicy = {
	end_date?: FieldPolicy<any> | FieldReadFunction<any>
	start_date?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CustomFieldKeySpecifier = ('name' | 'value' | CustomFieldKeySpecifier)[]
export type CustomFieldFieldPolicy = {
	name?: FieldPolicy<any> | FieldReadFunction<any>
	value?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CustomUnitAmountKeySpecifier = ('maximum' | 'minimum' | 'preset' | CustomUnitAmountKeySpecifier)[]
export type CustomUnitAmountFieldPolicy = {
	maximum?: FieldPolicy<any> | FieldReadFunction<any>
	minimum?: FieldPolicy<any> | FieldReadFunction<any>
	preset?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CustomerKeySpecifier = (
	| 'address'
	| 'balance'
	| 'created'
	| 'currency'
	| 'default_source'
	| 'deleted'
	| 'delinquent'
	| 'description'
	| 'discount'
	| 'email'
	| 'id'
	| 'invoice_prefix'
	| 'invoice_settings'
	| 'livemode'
	| 'metadata'
	| 'name'
	| 'next_invoice_sequence'
	| 'object'
	| 'phone'
	| 'preferred_locales'
	| 'shipping'
	| 'sources'
	| 'subscriptions'
	| 'tax'
	| 'tax_exempt'
	| 'tax_ids'
	| 'test_clock'
	| CustomerKeySpecifier
)[]
export type CustomerFieldPolicy = {
	address?: FieldPolicy<any> | FieldReadFunction<any>
	balance?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	default_source?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	delinquent?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	discount?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_prefix?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_settings?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	next_invoice_sequence?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	phone?: FieldPolicy<any> | FieldReadFunction<any>
	preferred_locales?: FieldPolicy<any> | FieldReadFunction<any>
	shipping?: FieldPolicy<any> | FieldReadFunction<any>
	sources?: FieldPolicy<any> | FieldReadFunction<any>
	subscriptions?: FieldPolicy<any> | FieldReadFunction<any>
	tax?: FieldPolicy<any> | FieldReadFunction<any>
	tax_exempt?: FieldPolicy<any> | FieldReadFunction<any>
	tax_ids?: FieldPolicy<any> | FieldReadFunction<any>
	test_clock?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CustomerBalanceTransactionKeySpecifier = (
	| 'amount'
	| 'created'
	| 'credit_note'
	| 'currency'
	| 'customer'
	| 'description'
	| 'ending_balance'
	| 'id'
	| 'invoice'
	| 'livemode'
	| 'metadata'
	| 'object'
	| 'type'
	| CustomerBalanceTransactionKeySpecifier
)[]
export type CustomerBalanceTransactionFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	credit_note?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	ending_balance?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CustomerBalanceTransactionListKeySpecifier = (
	| 'data'
	| 'has_more'
	| 'object'
	| 'url'
	| CustomerBalanceTransactionListKeySpecifier
)[]
export type CustomerBalanceTransactionListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CustomerListKeySpecifier = ('data' | 'has_more' | 'object' | 'url' | CustomerListKeySpecifier)[]
export type CustomerListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CustomerSearchResultsKeySpecifier = (
	| 'data'
	| 'has_more'
	| 'next_page'
	| 'object'
	| 'total_count'
	| 'url'
	| CustomerSearchResultsKeySpecifier
)[]
export type CustomerSearchResultsFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	next_page?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	total_count?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CustomerShippingKeySpecifier = (
	| 'carrier'
	| 'name'
	| 'phone'
	| 'tracking_number'
	| CustomerShippingKeySpecifier
)[]
export type CustomerShippingFieldPolicy = {
	carrier?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	phone?: FieldPolicy<any> | FieldReadFunction<any>
	tracking_number?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CustomerSourceListKeySpecifier = ('data' | 'has_more' | 'object' | 'url' | CustomerSourceListKeySpecifier)[]
export type CustomerSourceListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CustomerTaxIdKeySpecifier = ('type' | 'value' | CustomerTaxIdKeySpecifier)[]
export type CustomerTaxIdFieldPolicy = {
	type?: FieldPolicy<any> | FieldReadFunction<any>
	value?: FieldPolicy<any> | FieldReadFunction<any>
}
export type DebitCreditsAppliedKeySpecifier = ('invoice' | 'invoice_line_item' | DebitCreditsAppliedKeySpecifier)[]
export type DebitCreditsAppliedFieldPolicy = {
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_line_item?: FieldPolicy<any> | FieldReadFunction<any>
}
export type DefaultSettingsKeySpecifier = (
	| 'application_fee_percent'
	| 'automatic_tax'
	| 'billing_cycle_anchor'
	| 'billing_thresholds'
	| 'collection_method'
	| 'default_payment_method'
	| 'description'
	| 'invoice_settings'
	| 'on_behalf_of'
	| 'transfer_data'
	| DefaultSettingsKeySpecifier
)[]
export type DefaultSettingsFieldPolicy = {
	application_fee_percent?: FieldPolicy<any> | FieldReadFunction<any>
	automatic_tax?: FieldPolicy<any> | FieldReadFunction<any>
	billing_cycle_anchor?: FieldPolicy<any> | FieldReadFunction<any>
	billing_thresholds?: FieldPolicy<any> | FieldReadFunction<any>
	collection_method?: FieldPolicy<any> | FieldReadFunction<any>
	default_payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_settings?: FieldPolicy<any> | FieldReadFunction<any>
	on_behalf_of?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_data?: FieldPolicy<any> | FieldReadFunction<any>
}
export type DiscountKeySpecifier = (
	| 'checkout_session'
	| 'coupon'
	| 'customer'
	| 'deleted'
	| 'end'
	| 'id'
	| 'invoice'
	| 'invoice_item'
	| 'object'
	| 'promotion_code'
	| 'start'
	| 'subscription'
	| 'subscription_item'
	| DiscountKeySpecifier
)[]
export type DiscountFieldPolicy = {
	checkout_session?: FieldPolicy<any> | FieldReadFunction<any>
	coupon?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	end?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_item?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	promotion_code?: FieldPolicy<any> | FieldReadFunction<any>
	start?: FieldPolicy<any> | FieldReadFunction<any>
	subscription?: FieldPolicy<any> | FieldReadFunction<any>
	subscription_item?: FieldPolicy<any> | FieldReadFunction<any>
}
export type DiscountAmountKeySpecifier = ('amount' | 'discount' | DiscountAmountKeySpecifier)[]
export type DiscountAmountFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	discount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type DisputeKeySpecifier = (
	| 'amount'
	| 'balance_transactions'
	| 'charge'
	| 'created'
	| 'currency'
	| 'enhanced_eligibility_types'
	| 'evidence'
	| 'evidence_details'
	| 'id'
	| 'is_charge_refundable'
	| 'livemode'
	| 'metadata'
	| 'network_reason_code'
	| 'object'
	| 'payment_intent'
	| 'reason'
	| 'status'
	| DisputeKeySpecifier
)[]
export type DisputeFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	balance_transactions?: FieldPolicy<any> | FieldReadFunction<any>
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	enhanced_eligibility_types?: FieldPolicy<any> | FieldReadFunction<any>
	evidence?: FieldPolicy<any> | FieldReadFunction<any>
	evidence_details?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	is_charge_refundable?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	network_reason_code?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	payment_intent?: FieldPolicy<any> | FieldReadFunction<any>
	reason?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
}
export type DisputedTransactionKeySpecifier = (
	| 'customer_account_id'
	| 'customer_device_fingerprint'
	| 'customer_device_id'
	| 'customer_email_address'
	| 'customer_purchase_ip'
	| 'merchandise_or_services'
	| 'product_description'
	| 'shipping_address'
	| DisputedTransactionKeySpecifier
)[]
export type DisputedTransactionFieldPolicy = {
	customer_account_id?: FieldPolicy<any> | FieldReadFunction<any>
	customer_device_fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	customer_device_id?: FieldPolicy<any> | FieldReadFunction<any>
	customer_email_address?: FieldPolicy<any> | FieldReadFunction<any>
	customer_purchase_ip?: FieldPolicy<any> | FieldReadFunction<any>
	merchandise_or_services?: FieldPolicy<any> | FieldReadFunction<any>
	product_description?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_address?: FieldPolicy<any> | FieldReadFunction<any>
}
export type DobKeySpecifier = ('day' | 'month' | 'year' | DobKeySpecifier)[]
export type DobFieldPolicy = {
	day?: FieldPolicy<any> | FieldReadFunction<any>
	month?: FieldPolicy<any> | FieldReadFunction<any>
	year?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EmailTemplateKeySpecifier = ('name' | 'provider' | 'type' | EmailTemplateKeySpecifier)[]
export type EmailTemplateFieldPolicy = {
	name?: FieldPolicy<any> | FieldReadFunction<any>
	provider?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EnhancedEligibilityKeySpecifier = ('visa_compelling_evidence_3' | EnhancedEligibilityKeySpecifier)[]
export type EnhancedEligibilityFieldPolicy = {
	visa_compelling_evidence_3?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EnhancedEligibilityVisaCompellingEvidence3KeySpecifier = (
	| 'required_actions'
	| 'status'
	| EnhancedEligibilityVisaCompellingEvidence3KeySpecifier
)[]
export type EnhancedEligibilityVisaCompellingEvidence3FieldPolicy = {
	required_actions?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EnhancedEvidenceKeySpecifier = ('visa_compelling_evidence_3' | EnhancedEvidenceKeySpecifier)[]
export type EnhancedEvidenceFieldPolicy = {
	visa_compelling_evidence_3?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EnhancedEvidenceVisaCompellingEvidence3KeySpecifier = (
	| 'disputed_transaction'
	| 'prior_undisputed_transactions'
	| EnhancedEvidenceVisaCompellingEvidence3KeySpecifier
)[]
export type EnhancedEvidenceVisaCompellingEvidence3FieldPolicy = {
	disputed_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	prior_undisputed_transactions?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EntityCreatedKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'entity'
	| 'entityIds'
	| 'fingerprint'
	| 'owners'
	| 'relations'
	| 'request'
	| 'type'
	| 'updatedAt'
	| EntityCreatedKeySpecifier
)[]
export type EntityCreatedFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	entity?: FieldPolicy<any> | FieldReadFunction<any>
	entityIds?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	owners?: FieldPolicy<any> | FieldReadFunction<any>
	relations?: FieldPolicy<any> | FieldReadFunction<any>
	request?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EntityDeleteResultKeySpecifier = ('_id' | EntityDeleteResultKeySpecifier)[]
export type EntityDeleteResultFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EntityDeletedKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'entity'
	| 'entityIds'
	| 'fingerprint'
	| 'owners'
	| 'relations'
	| 'request'
	| 'type'
	| 'updatedAt'
	| EntityDeletedKeySpecifier
)[]
export type EntityDeletedFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	entity?: FieldPolicy<any> | FieldReadFunction<any>
	entityIds?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	owners?: FieldPolicy<any> | FieldReadFunction<any>
	relations?: FieldPolicy<any> | FieldReadFunction<any>
	request?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EntityDisabledKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'entity'
	| 'entityIds'
	| 'fingerprint'
	| 'owners'
	| 'relations'
	| 'request'
	| 'type'
	| 'updatedAt'
	| EntityDisabledKeySpecifier
)[]
export type EntityDisabledFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	entity?: FieldPolicy<any> | FieldReadFunction<any>
	entityIds?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	owners?: FieldPolicy<any> | FieldReadFunction<any>
	relations?: FieldPolicy<any> | FieldReadFunction<any>
	request?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EntityEnabledKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'entity'
	| 'entityIds'
	| 'fingerprint'
	| 'owners'
	| 'relations'
	| 'request'
	| 'type'
	| 'updatedAt'
	| EntityEnabledKeySpecifier
)[]
export type EntityEnabledFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	entity?: FieldPolicy<any> | FieldReadFunction<any>
	entityIds?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	owners?: FieldPolicy<any> | FieldReadFunction<any>
	relations?: FieldPolicy<any> | FieldReadFunction<any>
	request?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EntityEventKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'entity'
	| 'entityIds'
	| 'owners'
	| 'relations'
	| 'updatedAt'
	| EntityEventKeySpecifier
)[]
export type EntityEventFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	entity?: FieldPolicy<any> | FieldReadFunction<any>
	entityIds?: FieldPolicy<any> | FieldReadFunction<any>
	owners?: FieldPolicy<any> | FieldReadFunction<any>
	relations?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EntityRelationsKeySpecifier = ('entityIds' | 'id' | 'type' | EntityRelationsKeySpecifier)[]
export type EntityRelationsFieldPolicy = {
	entityIds?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EntityRestoredKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'entity'
	| 'entityIds'
	| 'fingerprint'
	| 'owners'
	| 'relations'
	| 'request'
	| 'type'
	| 'updatedAt'
	| EntityRestoredKeySpecifier
)[]
export type EntityRestoredFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	entity?: FieldPolicy<any> | FieldReadFunction<any>
	entityIds?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	owners?: FieldPolicy<any> | FieldReadFunction<any>
	relations?: FieldPolicy<any> | FieldReadFunction<any>
	request?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EntityUpdatedKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'entity'
	| 'entityIds'
	| 'fingerprint'
	| 'owners'
	| 'relations'
	| 'request'
	| 'type'
	| 'updatedAt'
	| EntityUpdatedKeySpecifier
)[]
export type EntityUpdatedFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	entity?: FieldPolicy<any> | FieldReadFunction<any>
	entityIds?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	owners?: FieldPolicy<any> | FieldReadFunction<any>
	relations?: FieldPolicy<any> | FieldReadFunction<any>
	request?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EpsKeySpecifier = ('bank' | EpsKeySpecifier)[]
export type EpsFieldPolicy = {
	bank?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EvidenceKeySpecifier = (
	| 'access_activity_log'
	| 'billing_address'
	| 'cancellation_policy'
	| 'cancellation_policy_disclosure'
	| 'cancellation_rebuttal'
	| 'customer_communication'
	| 'customer_email_address'
	| 'customer_name'
	| 'customer_purchase_ip'
	| 'customer_signature'
	| 'duplicate_charge_documentation'
	| 'duplicate_charge_explanation'
	| 'duplicate_charge_id'
	| 'enhanced_evidence'
	| 'product_description'
	| 'receipt'
	| 'refund_policy'
	| 'refund_policy_disclosure'
	| 'refund_refusal_explanation'
	| 'service_date'
	| 'service_documentation'
	| 'shipping_address'
	| 'shipping_carrier'
	| 'shipping_date'
	| 'shipping_documentation'
	| 'shipping_tracking_number'
	| 'uncategorized_file'
	| 'uncategorized_text'
	| EvidenceKeySpecifier
)[]
export type EvidenceFieldPolicy = {
	access_activity_log?: FieldPolicy<any> | FieldReadFunction<any>
	billing_address?: FieldPolicy<any> | FieldReadFunction<any>
	cancellation_policy?: FieldPolicy<any> | FieldReadFunction<any>
	cancellation_policy_disclosure?: FieldPolicy<any> | FieldReadFunction<any>
	cancellation_rebuttal?: FieldPolicy<any> | FieldReadFunction<any>
	customer_communication?: FieldPolicy<any> | FieldReadFunction<any>
	customer_email_address?: FieldPolicy<any> | FieldReadFunction<any>
	customer_name?: FieldPolicy<any> | FieldReadFunction<any>
	customer_purchase_ip?: FieldPolicy<any> | FieldReadFunction<any>
	customer_signature?: FieldPolicy<any> | FieldReadFunction<any>
	duplicate_charge_documentation?: FieldPolicy<any> | FieldReadFunction<any>
	duplicate_charge_explanation?: FieldPolicy<any> | FieldReadFunction<any>
	duplicate_charge_id?: FieldPolicy<any> | FieldReadFunction<any>
	enhanced_evidence?: FieldPolicy<any> | FieldReadFunction<any>
	product_description?: FieldPolicy<any> | FieldReadFunction<any>
	receipt?: FieldPolicy<any> | FieldReadFunction<any>
	refund_policy?: FieldPolicy<any> | FieldReadFunction<any>
	refund_policy_disclosure?: FieldPolicy<any> | FieldReadFunction<any>
	refund_refusal_explanation?: FieldPolicy<any> | FieldReadFunction<any>
	service_date?: FieldPolicy<any> | FieldReadFunction<any>
	service_documentation?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_address?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_carrier?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_date?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_documentation?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_tracking_number?: FieldPolicy<any> | FieldReadFunction<any>
	uncategorized_file?: FieldPolicy<any> | FieldReadFunction<any>
	uncategorized_text?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EvidenceDetailsKeySpecifier = (
	| 'due_by'
	| 'enhanced_eligibility'
	| 'has_evidence'
	| 'past_due'
	| 'submission_count'
	| EvidenceDetailsKeySpecifier
)[]
export type EvidenceDetailsFieldPolicy = {
	due_by?: FieldPolicy<any> | FieldReadFunction<any>
	enhanced_eligibility?: FieldPolicy<any> | FieldReadFunction<any>
	has_evidence?: FieldPolicy<any> | FieldReadFunction<any>
	past_due?: FieldPolicy<any> | FieldReadFunction<any>
	submission_count?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ExternalAccountsListKeySpecifier = (
	| 'data'
	| 'has_more'
	| 'object'
	| 'url'
	| ExternalAccountsListKeySpecifier
)[]
export type ExternalAccountsListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FederatedUserKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'ensName'
	| 'externalId'
	| 'provider'
	| 'sub'
	| 'updatedAt'
	| FederatedUserKeySpecifier
)[]
export type FederatedUserFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	ensName?: FieldPolicy<any> | FieldReadFunction<any>
	externalId?: FieldPolicy<any> | FieldReadFunction<any>
	provider?: FieldPolicy<any> | FieldReadFunction<any>
	sub?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FeeDetailKeySpecifier = (
	| 'amount'
	| 'application'
	| 'currency'
	| 'description'
	| 'type'
	| FeeDetailKeySpecifier
)[]
export type FeeDetailFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	application?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FeeRefundKeySpecifier = (
	| 'amount'
	| 'balance_transaction'
	| 'created'
	| 'currency'
	| 'fee'
	| 'id'
	| 'metadata'
	| 'object'
	| FeeRefundKeySpecifier
)[]
export type FeeRefundFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	fee?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FeeRefundListKeySpecifier = ('data' | 'has_more' | 'object' | 'url' | FeeRefundListKeySpecifier)[]
export type FeeRefundListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FeeSourceKeySpecifier = ('charge' | 'payout' | 'type' | FeeSourceKeySpecifier)[]
export type FeeSourceFieldPolicy = {
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	payout?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FileKeySpecifier = (
	| 'created'
	| 'expires_at'
	| 'filename'
	| 'id'
	| 'links'
	| 'object'
	| 'purpose'
	| 'size'
	| 'title'
	| 'type'
	| 'url'
	| FileKeySpecifier
)[]
export type FileFieldPolicy = {
	created?: FieldPolicy<any> | FieldReadFunction<any>
	expires_at?: FieldPolicy<any> | FieldReadFunction<any>
	filename?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	links?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	purpose?: FieldPolicy<any> | FieldReadFunction<any>
	size?: FieldPolicy<any> | FieldReadFunction<any>
	title?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FileLinkKeySpecifier = (
	| 'created'
	| 'expired'
	| 'expires_at'
	| 'file'
	| 'id'
	| 'livemode'
	| 'metadata'
	| 'object'
	| 'url'
	| FileLinkKeySpecifier
)[]
export type FileLinkFieldPolicy = {
	created?: FieldPolicy<any> | FieldReadFunction<any>
	expired?: FieldPolicy<any> | FieldReadFunction<any>
	expires_at?: FieldPolicy<any> | FieldReadFunction<any>
	file?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FileLinkListKeySpecifier = ('data' | 'has_more' | 'object' | 'url' | FileLinkListKeySpecifier)[]
export type FileLinkListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FingerprintKeySpecifier = (
	| 'country'
	| 'ip'
	| 'referer'
	| 'requestId'
	| 'userAgent'
	| 'userId'
	| FingerprintKeySpecifier
)[]
export type FingerprintFieldPolicy = {
	country?: FieldPolicy<any> | FieldReadFunction<any>
	ip?: FieldPolicy<any> | FieldReadFunction<any>
	referer?: FieldPolicy<any> | FieldReadFunction<any>
	requestId?: FieldPolicy<any> | FieldReadFunction<any>
	userAgent?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FlatAmountKeySpecifier = ('amount' | 'currency' | FlatAmountKeySpecifier)[]
export type FlatAmountFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FpxKeySpecifier = ('account_holder_type' | 'bank' | FpxKeySpecifier)[]
export type FpxFieldPolicy = {
	account_holder_type?: FieldPolicy<any> | FieldReadFunction<any>
	bank?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FraudDetailsKeySpecifier = ('stripe_report' | 'user_report' | FraudDetailsKeySpecifier)[]
export type FraudDetailsFieldPolicy = {
	stripe_report?: FieldPolicy<any> | FieldReadFunction<any>
	user_report?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FromInvoiceKeySpecifier = ('action' | 'invoice' | FromInvoiceKeySpecifier)[]
export type FromInvoiceFieldPolicy = {
	action?: FieldPolicy<any> | FieldReadFunction<any>
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FromQuoteKeySpecifier = ('is_revision' | 'quote' | FromQuoteKeySpecifier)[]
export type FromQuoteFieldPolicy = {
	is_revision?: FieldPolicy<any> | FieldReadFunction<any>
	quote?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FundingInstructionsKeySpecifier = (
	| 'bank_transfer'
	| 'currency'
	| 'funding_type'
	| 'livemode'
	| 'object'
	| FundingInstructionsKeySpecifier
)[]
export type FundingInstructionsFieldPolicy = {
	bank_transfer?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	funding_type?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FundingInstructionsBankTransferKeySpecifier = (
	| 'country'
	| 'financial_addresses'
	| 'type'
	| FundingInstructionsBankTransferKeySpecifier
)[]
export type FundingInstructionsBankTransferFieldPolicy = {
	country?: FieldPolicy<any> | FieldReadFunction<any>
	financial_addresses?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FutureRequirementsKeySpecifier = (
	| 'alternatives'
	| 'current_deadline'
	| 'currently_due'
	| 'disabled_reason'
	| 'errors'
	| 'eventually_due'
	| 'past_due'
	| 'pending_verification'
	| FutureRequirementsKeySpecifier
)[]
export type FutureRequirementsFieldPolicy = {
	alternatives?: FieldPolicy<any> | FieldReadFunction<any>
	current_deadline?: FieldPolicy<any> | FieldReadFunction<any>
	currently_due?: FieldPolicy<any> | FieldReadFunction<any>
	disabled_reason?: FieldPolicy<any> | FieldReadFunction<any>
	errors?: FieldPolicy<any> | FieldReadFunction<any>
	eventually_due?: FieldPolicy<any> | FieldReadFunction<any>
	past_due?: FieldPolicy<any> | FieldReadFunction<any>
	pending_verification?: FieldPolicy<any> | FieldReadFunction<any>
}
export type GIAI96ComponentsKeySpecifier = (
	| 'companyPrefix'
	| 'filter'
	| 'individualAssetReference'
	| 'scheme'
	| GIAI96ComponentsKeySpecifier
)[]
export type GIAI96ComponentsFieldPolicy = {
	companyPrefix?: FieldPolicy<any> | FieldReadFunction<any>
	filter?: FieldPolicy<any> | FieldReadFunction<any>
	individualAssetReference?: FieldPolicy<any> | FieldReadFunction<any>
	scheme?: FieldPolicy<any> | FieldReadFunction<any>
}
export type GID96ComponentsKeySpecifier = (
	| 'generalManagerNumber'
	| 'objectClass'
	| 'scheme'
	| 'serialNumber'
	| GID96ComponentsKeySpecifier
)[]
export type GID96ComponentsFieldPolicy = {
	generalManagerNumber?: FieldPolicy<any> | FieldReadFunction<any>
	objectClass?: FieldPolicy<any> | FieldReadFunction<any>
	scheme?: FieldPolicy<any> | FieldReadFunction<any>
	serialNumber?: FieldPolicy<any> | FieldReadFunction<any>
}
export type GRAI96ComponentsKeySpecifier = (
	| 'assetType'
	| 'companyPrefix'
	| 'filter'
	| 'scheme'
	| 'serialNumber'
	| GRAI96ComponentsKeySpecifier
)[]
export type GRAI96ComponentsFieldPolicy = {
	assetType?: FieldPolicy<any> | FieldReadFunction<any>
	companyPrefix?: FieldPolicy<any> | FieldReadFunction<any>
	filter?: FieldPolicy<any> | FieldReadFunction<any>
	scheme?: FieldPolicy<any> | FieldReadFunction<any>
	serialNumber?: FieldPolicy<any> | FieldReadFunction<any>
}
export type GeneratedFromKeySpecifier = (
	| 'charge'
	| 'payment_method_details'
	| 'setup_attempt'
	| GeneratedFromKeySpecifier
)[]
export type GeneratedFromFieldPolicy = {
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_details?: FieldPolicy<any> | FieldReadFunction<any>
	setup_attempt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type GenericAuditEventKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'entity'
	| 'entityIds'
	| 'fingerprint'
	| 'owners'
	| 'relations'
	| 'request'
	| 'type'
	| 'updatedAt'
	| GenericAuditEventKeySpecifier
)[]
export type GenericAuditEventFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	entity?: FieldPolicy<any> | FieldReadFunction<any>
	entityIds?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	owners?: FieldPolicy<any> | FieldReadFunction<any>
	relations?: FieldPolicy<any> | FieldReadFunction<any>
	request?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ISO15963ComponentsKeySpecifier = (
	| 'manufacturerCode'
	| 'scheme'
	| 'serialNumber'
	| ISO15963ComponentsKeySpecifier
)[]
export type ISO15963ComponentsFieldPolicy = {
	manufacturerCode?: FieldPolicy<any> | FieldReadFunction<any>
	scheme?: FieldPolicy<any> | FieldReadFunction<any>
	serialNumber?: FieldPolicy<any> | FieldReadFunction<any>
}
export type IdealKeySpecifier = ('bank' | 'bic' | IdealKeySpecifier)[]
export type IdealFieldPolicy = {
	bank?: FieldPolicy<any> | FieldReadFunction<any>
	bic?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InstallationKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'subscription'
	| 'token'
	| 'type'
	| 'updatedAt'
	| 'userId'
	| InstallationKeySpecifier
)[]
export type InstallationFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	subscription?: FieldPolicy<any> | FieldReadFunction<any>
	token?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvitationKeySpecifier = (
	| '_id'
	| 'code'
	| 'consumerIds'
	| 'consumers'
	| 'createdAt'
	| 'createdBy'
	| 'createdById'
	| 'deletedAt'
	| 'emails'
	| 'expiresAt'
	| 'limit'
	| 'organization'
	| 'organizationId'
	| 'phones'
	| 'roles'
	| 'type'
	| 'updatedAt'
	| 'used'
	| 'usedCount'
	| 'userIds'
	| 'users'
	| InvitationKeySpecifier
)[]
export type InvitationFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	code?: FieldPolicy<any> | FieldReadFunction<any>
	consumerIds?: FieldPolicy<any> | FieldReadFunction<any>
	consumers?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	createdBy?: FieldPolicy<any> | FieldReadFunction<any>
	createdById?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	emails?: FieldPolicy<any> | FieldReadFunction<any>
	expiresAt?: FieldPolicy<any> | FieldReadFunction<any>
	limit?: FieldPolicy<any> | FieldReadFunction<any>
	organization?: FieldPolicy<any> | FieldReadFunction<any>
	organizationId?: FieldPolicy<any> | FieldReadFunction<any>
	phones?: FieldPolicy<any> | FieldReadFunction<any>
	roles?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	used?: FieldPolicy<any> | FieldReadFunction<any>
	usedCount?: FieldPolicy<any> | FieldReadFunction<any>
	userIds?: FieldPolicy<any> | FieldReadFunction<any>
	users?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceKeySpecifier = (
	| 'account_country'
	| 'account_name'
	| 'account_tax_ids'
	| 'amount_due'
	| 'amount_paid'
	| 'amount_remaining'
	| 'amount_shipping'
	| 'application'
	| 'application_fee_amount'
	| 'attempt_count'
	| 'attempted'
	| 'auto_advance'
	| 'automatic_tax'
	| 'automatically_finalizes_at'
	| 'billing_reason'
	| 'charge'
	| 'collection_method'
	| 'created'
	| 'currency'
	| 'custom_fields'
	| 'customer'
	| 'customer_address'
	| 'customer_email'
	| 'customer_name'
	| 'customer_phone'
	| 'customer_shipping'
	| 'customer_tax_exempt'
	| 'customer_tax_ids'
	| 'default_payment_method'
	| 'default_source'
	| 'default_tax_rates'
	| 'deleted'
	| 'description'
	| 'discount'
	| 'discounts'
	| 'due_date'
	| 'effective_at'
	| 'ending_balance'
	| 'footer'
	| 'from_invoice'
	| 'hosted_invoice_url'
	| 'id'
	| 'invoice_pdf'
	| 'issuer'
	| 'last_finalization_error'
	| 'latest_revision'
	| 'lines'
	| 'livemode'
	| 'metadata'
	| 'next_payment_attempt'
	| 'number'
	| 'object'
	| 'on_behalf_of'
	| 'paid'
	| 'paid_out_of_band'
	| 'payment_intent'
	| 'payment_settings'
	| 'period_end'
	| 'period_start'
	| 'post_payment_credit_notes_amount'
	| 'pre_payment_credit_notes_amount'
	| 'quote'
	| 'receipt_number'
	| 'rendering'
	| 'shipping_cost'
	| 'shipping_details'
	| 'starting_balance'
	| 'statement_descriptor'
	| 'status'
	| 'status_transitions'
	| 'subscription'
	| 'subscription_details'
	| 'subscription_proration_date'
	| 'subtotal'
	| 'subtotal_excluding_tax'
	| 'tax'
	| 'test_clock'
	| 'threshold_reason'
	| 'total'
	| 'total_discount_amounts'
	| 'total_excluding_tax'
	| 'total_pretax_credit_amounts'
	| 'total_tax_amounts'
	| 'transfer_data'
	| 'webhooks_delivered_at'
	| InvoiceKeySpecifier
)[]
export type InvoiceFieldPolicy = {
	account_country?: FieldPolicy<any> | FieldReadFunction<any>
	account_name?: FieldPolicy<any> | FieldReadFunction<any>
	account_tax_ids?: FieldPolicy<any> | FieldReadFunction<any>
	amount_due?: FieldPolicy<any> | FieldReadFunction<any>
	amount_paid?: FieldPolicy<any> | FieldReadFunction<any>
	amount_remaining?: FieldPolicy<any> | FieldReadFunction<any>
	amount_shipping?: FieldPolicy<any> | FieldReadFunction<any>
	application?: FieldPolicy<any> | FieldReadFunction<any>
	application_fee_amount?: FieldPolicy<any> | FieldReadFunction<any>
	attempt_count?: FieldPolicy<any> | FieldReadFunction<any>
	attempted?: FieldPolicy<any> | FieldReadFunction<any>
	auto_advance?: FieldPolicy<any> | FieldReadFunction<any>
	automatic_tax?: FieldPolicy<any> | FieldReadFunction<any>
	automatically_finalizes_at?: FieldPolicy<any> | FieldReadFunction<any>
	billing_reason?: FieldPolicy<any> | FieldReadFunction<any>
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	collection_method?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	custom_fields?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	customer_address?: FieldPolicy<any> | FieldReadFunction<any>
	customer_email?: FieldPolicy<any> | FieldReadFunction<any>
	customer_name?: FieldPolicy<any> | FieldReadFunction<any>
	customer_phone?: FieldPolicy<any> | FieldReadFunction<any>
	customer_shipping?: FieldPolicy<any> | FieldReadFunction<any>
	customer_tax_exempt?: FieldPolicy<any> | FieldReadFunction<any>
	customer_tax_ids?: FieldPolicy<any> | FieldReadFunction<any>
	default_payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	default_source?: FieldPolicy<any> | FieldReadFunction<any>
	default_tax_rates?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	discount?: FieldPolicy<any> | FieldReadFunction<any>
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	due_date?: FieldPolicy<any> | FieldReadFunction<any>
	effective_at?: FieldPolicy<any> | FieldReadFunction<any>
	ending_balance?: FieldPolicy<any> | FieldReadFunction<any>
	footer?: FieldPolicy<any> | FieldReadFunction<any>
	from_invoice?: FieldPolicy<any> | FieldReadFunction<any>
	hosted_invoice_url?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_pdf?: FieldPolicy<any> | FieldReadFunction<any>
	issuer?: FieldPolicy<any> | FieldReadFunction<any>
	last_finalization_error?: FieldPolicy<any> | FieldReadFunction<any>
	latest_revision?: FieldPolicy<any> | FieldReadFunction<any>
	lines?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	next_payment_attempt?: FieldPolicy<any> | FieldReadFunction<any>
	number?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	on_behalf_of?: FieldPolicy<any> | FieldReadFunction<any>
	paid?: FieldPolicy<any> | FieldReadFunction<any>
	paid_out_of_band?: FieldPolicy<any> | FieldReadFunction<any>
	payment_intent?: FieldPolicy<any> | FieldReadFunction<any>
	payment_settings?: FieldPolicy<any> | FieldReadFunction<any>
	period_end?: FieldPolicy<any> | FieldReadFunction<any>
	period_start?: FieldPolicy<any> | FieldReadFunction<any>
	post_payment_credit_notes_amount?: FieldPolicy<any> | FieldReadFunction<any>
	pre_payment_credit_notes_amount?: FieldPolicy<any> | FieldReadFunction<any>
	quote?: FieldPolicy<any> | FieldReadFunction<any>
	receipt_number?: FieldPolicy<any> | FieldReadFunction<any>
	rendering?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_cost?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_details?: FieldPolicy<any> | FieldReadFunction<any>
	starting_balance?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	status_transitions?: FieldPolicy<any> | FieldReadFunction<any>
	subscription?: FieldPolicy<any> | FieldReadFunction<any>
	subscription_details?: FieldPolicy<any> | FieldReadFunction<any>
	subscription_proration_date?: FieldPolicy<any> | FieldReadFunction<any>
	subtotal?: FieldPolicy<any> | FieldReadFunction<any>
	subtotal_excluding_tax?: FieldPolicy<any> | FieldReadFunction<any>
	tax?: FieldPolicy<any> | FieldReadFunction<any>
	test_clock?: FieldPolicy<any> | FieldReadFunction<any>
	threshold_reason?: FieldPolicy<any> | FieldReadFunction<any>
	total?: FieldPolicy<any> | FieldReadFunction<any>
	total_discount_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	total_excluding_tax?: FieldPolicy<any> | FieldReadFunction<any>
	total_pretax_credit_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	total_tax_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_data?: FieldPolicy<any> | FieldReadFunction<any>
	webhooks_delivered_at?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceAutomaticTaxKeySpecifier = (
	| 'disabled_reason'
	| 'enabled'
	| 'liability'
	| 'status'
	| InvoiceAutomaticTaxKeySpecifier
)[]
export type InvoiceAutomaticTaxFieldPolicy = {
	disabled_reason?: FieldPolicy<any> | FieldReadFunction<any>
	enabled?: FieldPolicy<any> | FieldReadFunction<any>
	liability?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceItemKeySpecifier = (
	| 'amount'
	| 'currency'
	| 'customer'
	| 'date'
	| 'deleted'
	| 'description'
	| 'discountable'
	| 'discounts'
	| 'id'
	| 'invoice'
	| 'livemode'
	| 'metadata'
	| 'object'
	| 'period'
	| 'plan'
	| 'price'
	| 'proration'
	| 'quantity'
	| 'subscription'
	| 'subscription_item'
	| 'tax_rates'
	| 'test_clock'
	| 'unit_amount'
	| 'unit_amount_decimal'
	| InvoiceItemKeySpecifier
)[]
export type InvoiceItemFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	date?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	discountable?: FieldPolicy<any> | FieldReadFunction<any>
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	period?: FieldPolicy<any> | FieldReadFunction<any>
	plan?: FieldPolicy<any> | FieldReadFunction<any>
	price?: FieldPolicy<any> | FieldReadFunction<any>
	proration?: FieldPolicy<any> | FieldReadFunction<any>
	quantity?: FieldPolicy<any> | FieldReadFunction<any>
	subscription?: FieldPolicy<any> | FieldReadFunction<any>
	subscription_item?: FieldPolicy<any> | FieldReadFunction<any>
	tax_rates?: FieldPolicy<any> | FieldReadFunction<any>
	test_clock?: FieldPolicy<any> | FieldReadFunction<any>
	unit_amount?: FieldPolicy<any> | FieldReadFunction<any>
	unit_amount_decimal?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceLineItemKeySpecifier = (
	| 'amount'
	| 'amount_excluding_tax'
	| 'currency'
	| 'description'
	| 'discount_amounts'
	| 'discountable'
	| 'discounts'
	| 'id'
	| 'invoice'
	| 'invoice_item'
	| 'livemode'
	| 'metadata'
	| 'object'
	| 'period'
	| 'plan'
	| 'pretax_credit_amounts'
	| 'price'
	| 'proration'
	| 'proration_details'
	| 'quantity'
	| 'subscription'
	| 'subscription_item'
	| 'tax_amounts'
	| 'tax_rates'
	| 'type'
	| 'unit_amount_excluding_tax'
	| InvoiceLineItemKeySpecifier
)[]
export type InvoiceLineItemFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_excluding_tax?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	discount_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	discountable?: FieldPolicy<any> | FieldReadFunction<any>
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_item?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	period?: FieldPolicy<any> | FieldReadFunction<any>
	plan?: FieldPolicy<any> | FieldReadFunction<any>
	pretax_credit_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	price?: FieldPolicy<any> | FieldReadFunction<any>
	proration?: FieldPolicy<any> | FieldReadFunction<any>
	proration_details?: FieldPolicy<any> | FieldReadFunction<any>
	quantity?: FieldPolicy<any> | FieldReadFunction<any>
	subscription?: FieldPolicy<any> | FieldReadFunction<any>
	subscription_item?: FieldPolicy<any> | FieldReadFunction<any>
	tax_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	tax_rates?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	unit_amount_excluding_tax?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceLineItemListKeySpecifier = (
	| 'data'
	| 'has_more'
	| 'object'
	| 'url'
	| InvoiceLineItemListKeySpecifier
)[]
export type InvoiceLineItemListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceListKeySpecifier = ('data' | 'has_more' | 'object' | 'url' | InvoiceListKeySpecifier)[]
export type InvoiceListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceSearchResultsKeySpecifier = (
	| 'data'
	| 'has_more'
	| 'next_page'
	| 'object'
	| 'total_count'
	| 'url'
	| InvoiceSearchResultsKeySpecifier
)[]
export type InvoiceSearchResultsFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	next_page?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	total_count?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceSettingsKeySpecifier = (
	| 'custom_fields'
	| 'default_payment_method'
	| 'footer'
	| 'rendering_options'
	| InvoiceSettingsKeySpecifier
)[]
export type InvoiceSettingsFieldPolicy = {
	custom_fields?: FieldPolicy<any> | FieldReadFunction<any>
	default_payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	footer?: FieldPolicy<any> | FieldReadFunction<any>
	rendering_options?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceSettingsCustomFieldKeySpecifier = ('name' | 'value' | InvoiceSettingsCustomFieldKeySpecifier)[]
export type InvoiceSettingsCustomFieldFieldPolicy = {
	name?: FieldPolicy<any> | FieldReadFunction<any>
	value?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceSettingsIssuerKeySpecifier = ('account' | 'type' | InvoiceSettingsIssuerKeySpecifier)[]
export type InvoiceSettingsIssuerFieldPolicy = {
	account?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceSettingsRenderingOptionsKeySpecifier = (
	| 'amount_tax_display'
	| 'template'
	| InvoiceSettingsRenderingOptionsKeySpecifier
)[]
export type InvoiceSettingsRenderingOptionsFieldPolicy = {
	amount_tax_display?: FieldPolicy<any> | FieldReadFunction<any>
	template?: FieldPolicy<any> | FieldReadFunction<any>
}
export type InvoiceTransferDataKeySpecifier = ('amount' | 'destination' | InvoiceTransferDataKeySpecifier)[]
export type InvoiceTransferDataFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	destination?: FieldPolicy<any> | FieldReadFunction<any>
}
export type IpAddressLocationKeySpecifier = (
	| 'city'
	| 'country'
	| 'latitude'
	| 'longitude'
	| 'region'
	| IpAddressLocationKeySpecifier
)[]
export type IpAddressLocationFieldPolicy = {
	city?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	latitude?: FieldPolicy<any> | FieldReadFunction<any>
	longitude?: FieldPolicy<any> | FieldReadFunction<any>
	region?: FieldPolicy<any> | FieldReadFunction<any>
}
export type IssuerKeySpecifier = ('account' | 'type' | IssuerKeySpecifier)[]
export type IssuerFieldPolicy = {
	account?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type IssuingAuthorizationKeySpecifier = ('id' | 'object' | IssuingAuthorizationKeySpecifier)[]
export type IssuingAuthorizationFieldPolicy = {
	id?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
}
export type IssuingDisputeKeySpecifier = ('id' | 'object' | IssuingDisputeKeySpecifier)[]
export type IssuingDisputeFieldPolicy = {
	id?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
}
export type IssuingTransactionKeySpecifier = ('id' | 'object' | IssuingTransactionKeySpecifier)[]
export type IssuingTransactionFieldPolicy = {
	id?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
}
export type KlarnaKeySpecifier = ('dob' | KlarnaKeySpecifier)[]
export type KlarnaFieldPolicy = {
	dob?: FieldPolicy<any> | FieldReadFunction<any>
}
export type KlarnaDobKeySpecifier = ('day' | 'month' | 'year' | KlarnaDobKeySpecifier)[]
export type KlarnaDobFieldPolicy = {
	day?: FieldPolicy<any> | FieldReadFunction<any>
	month?: FieldPolicy<any> | FieldReadFunction<any>
	year?: FieldPolicy<any> | FieldReadFunction<any>
}
export type LabelKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'name'
	| 'type'
	| 'updatedAt'
	| LabelKeySpecifier
)[]
export type LabelFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type LastFinalizationErrorKeySpecifier = (
	| 'charge'
	| 'code'
	| 'decline_code'
	| 'doc_url'
	| 'message'
	| 'param'
	| 'payment_intent'
	| 'payment_method'
	| 'payment_method_type'
	| 'setup_intent'
	| 'source'
	| 'type'
	| LastFinalizationErrorKeySpecifier
)[]
export type LastFinalizationErrorFieldPolicy = {
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	code?: FieldPolicy<any> | FieldReadFunction<any>
	decline_code?: FieldPolicy<any> | FieldReadFunction<any>
	doc_url?: FieldPolicy<any> | FieldReadFunction<any>
	message?: FieldPolicy<any> | FieldReadFunction<any>
	param?: FieldPolicy<any> | FieldReadFunction<any>
	payment_intent?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_type?: FieldPolicy<any> | FieldReadFunction<any>
	setup_intent?: FieldPolicy<any> | FieldReadFunction<any>
	source?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type LastPaymentErrorKeySpecifier = (
	| 'charge'
	| 'code'
	| 'decline_code'
	| 'doc_url'
	| 'message'
	| 'param'
	| 'payment_intent'
	| 'payment_method'
	| 'payment_method_type'
	| 'setup_intent'
	| 'source'
	| 'type'
	| LastPaymentErrorKeySpecifier
)[]
export type LastPaymentErrorFieldPolicy = {
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	code?: FieldPolicy<any> | FieldReadFunction<any>
	decline_code?: FieldPolicy<any> | FieldReadFunction<any>
	doc_url?: FieldPolicy<any> | FieldReadFunction<any>
	message?: FieldPolicy<any> | FieldReadFunction<any>
	param?: FieldPolicy<any> | FieldReadFunction<any>
	payment_intent?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_type?: FieldPolicy<any> | FieldReadFunction<any>
	setup_intent?: FieldPolicy<any> | FieldReadFunction<any>
	source?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type LastSetupErrorKeySpecifier = (
	| 'charge'
	| 'code'
	| 'decline_code'
	| 'doc_url'
	| 'message'
	| 'param'
	| 'payment_intent'
	| 'payment_method'
	| 'payment_method_type'
	| 'setup_intent'
	| 'source'
	| 'type'
	| LastSetupErrorKeySpecifier
)[]
export type LastSetupErrorFieldPolicy = {
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	code?: FieldPolicy<any> | FieldReadFunction<any>
	decline_code?: FieldPolicy<any> | FieldReadFunction<any>
	doc_url?: FieldPolicy<any> | FieldReadFunction<any>
	message?: FieldPolicy<any> | FieldReadFunction<any>
	param?: FieldPolicy<any> | FieldReadFunction<any>
	payment_intent?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_type?: FieldPolicy<any> | FieldReadFunction<any>
	setup_intent?: FieldPolicy<any> | FieldReadFunction<any>
	source?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type Level3KeySpecifier = (
	| 'customer_reference'
	| 'line_items'
	| 'merchant_reference'
	| 'shipping_address_zip'
	| 'shipping_amount'
	| 'shipping_from_zip'
	| Level3KeySpecifier
)[]
export type Level3FieldPolicy = {
	customer_reference?: FieldPolicy<any> | FieldReadFunction<any>
	line_items?: FieldPolicy<any> | FieldReadFunction<any>
	merchant_reference?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_address_zip?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_amount?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_from_zip?: FieldPolicy<any> | FieldReadFunction<any>
}
export type Level3LineItemKeySpecifier = (
	| 'discount_amount'
	| 'product_code'
	| 'product_description'
	| 'quantity'
	| 'tax_amount'
	| 'unit_cost'
	| Level3LineItemKeySpecifier
)[]
export type Level3LineItemFieldPolicy = {
	discount_amount?: FieldPolicy<any> | FieldReadFunction<any>
	product_code?: FieldPolicy<any> | FieldReadFunction<any>
	product_description?: FieldPolicy<any> | FieldReadFunction<any>
	quantity?: FieldPolicy<any> | FieldReadFunction<any>
	tax_amount?: FieldPolicy<any> | FieldReadFunction<any>
	unit_cost?: FieldPolicy<any> | FieldReadFunction<any>
}
export type LiabilityKeySpecifier = ('account' | 'type' | LiabilityKeySpecifier)[]
export type LiabilityFieldPolicy = {
	account?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type LineItemKeySpecifier = (
	| 'amount_discount'
	| 'amount_subtotal'
	| 'amount_tax'
	| 'amount_total'
	| 'currency'
	| 'description'
	| 'discounts'
	| 'id'
	| 'object'
	| 'price'
	| 'product'
	| 'quantity'
	| 'taxes'
	| LineItemKeySpecifier
)[]
export type LineItemFieldPolicy = {
	amount_discount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_subtotal?: FieldPolicy<any> | FieldReadFunction<any>
	amount_tax?: FieldPolicy<any> | FieldReadFunction<any>
	amount_total?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	price?: FieldPolicy<any> | FieldReadFunction<any>
	product?: FieldPolicy<any> | FieldReadFunction<any>
	quantity?: FieldPolicy<any> | FieldReadFunction<any>
	taxes?: FieldPolicy<any> | FieldReadFunction<any>
}
export type LineItemDiscountKeySpecifier = ('amount' | 'discount' | LineItemDiscountKeySpecifier)[]
export type LineItemDiscountFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	discount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type LineItemListKeySpecifier = ('data' | 'has_more' | 'object' | 'url' | LineItemListKeySpecifier)[]
export type LineItemListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type LineItemTaxKeySpecifier = (
	| 'amount'
	| 'rate'
	| 'taxability_reason'
	| 'taxable_amount'
	| LineItemTaxKeySpecifier
)[]
export type LineItemTaxFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	rate?: FieldPolicy<any> | FieldReadFunction<any>
	taxability_reason?: FieldPolicy<any> | FieldReadFunction<any>
	taxable_amount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type LinkKeySpecifier = ('email' | 'persistent_token' | LinkKeySpecifier)[]
export type LinkFieldPolicy = {
	email?: FieldPolicy<any> | FieldReadFunction<any>
	persistent_token?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MandateKeySpecifier = (
	| 'customer_acceptance'
	| 'id'
	| 'livemode'
	| 'multi_use'
	| 'object'
	| 'payment_method'
	| 'payment_method_details'
	| 'single_use'
	| 'status'
	| 'type'
	| MandateKeySpecifier
)[]
export type MandateFieldPolicy = {
	customer_acceptance?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	multi_use?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_details?: FieldPolicy<any> | FieldReadFunction<any>
	single_use?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MandateCustomerAcceptanceKeySpecifier = (
	| 'accepted_at'
	| 'offline'
	| 'online'
	| 'type'
	| MandateCustomerAcceptanceKeySpecifier
)[]
export type MandateCustomerAcceptanceFieldPolicy = {
	accepted_at?: FieldPolicy<any> | FieldReadFunction<any>
	offline?: FieldPolicy<any> | FieldReadFunction<any>
	online?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MandateCustomerAcceptanceOnlineKeySpecifier = (
	| 'ip_address'
	| 'user_agent'
	| MandateCustomerAcceptanceOnlineKeySpecifier
)[]
export type MandateCustomerAcceptanceOnlineFieldPolicy = {
	ip_address?: FieldPolicy<any> | FieldReadFunction<any>
	user_agent?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MandatePaymentMethodDetailsKeySpecifier = (
	| 'acss_debit'
	| 'au_becs_debit'
	| 'bacs_debit'
	| 'card'
	| 'link'
	| 'sepa_debit'
	| 'type'
	| 'us_bank_account'
	| MandatePaymentMethodDetailsKeySpecifier
)[]
export type MandatePaymentMethodDetailsFieldPolicy = {
	acss_debit?: FieldPolicy<any> | FieldReadFunction<any>
	au_becs_debit?: FieldPolicy<any> | FieldReadFunction<any>
	bacs_debit?: FieldPolicy<any> | FieldReadFunction<any>
	card?: FieldPolicy<any> | FieldReadFunction<any>
	link?: FieldPolicy<any> | FieldReadFunction<any>
	sepa_debit?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	us_bank_account?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MandatePaymentMethodDetailsAcssDebitKeySpecifier = (
	| 'default_for'
	| 'interval_description'
	| 'payment_schedule'
	| 'transaction_type'
	| MandatePaymentMethodDetailsAcssDebitKeySpecifier
)[]
export type MandatePaymentMethodDetailsAcssDebitFieldPolicy = {
	default_for?: FieldPolicy<any> | FieldReadFunction<any>
	interval_description?: FieldPolicy<any> | FieldReadFunction<any>
	payment_schedule?: FieldPolicy<any> | FieldReadFunction<any>
	transaction_type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MandatePaymentMethodDetailsAuBecsDebitKeySpecifier = (
	| 'url'
	| MandatePaymentMethodDetailsAuBecsDebitKeySpecifier
)[]
export type MandatePaymentMethodDetailsAuBecsDebitFieldPolicy = {
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MandatePaymentMethodDetailsBacsDebitKeySpecifier = (
	| 'network_status'
	| 'reference'
	| 'revocation_reason'
	| 'url'
	| MandatePaymentMethodDetailsBacsDebitKeySpecifier
)[]
export type MandatePaymentMethodDetailsBacsDebitFieldPolicy = {
	network_status?: FieldPolicy<any> | FieldReadFunction<any>
	reference?: FieldPolicy<any> | FieldReadFunction<any>
	revocation_reason?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MandatePaymentMethodDetailsSepaDebitKeySpecifier = (
	| 'reference'
	| 'url'
	| MandatePaymentMethodDetailsSepaDebitKeySpecifier
)[]
export type MandatePaymentMethodDetailsSepaDebitFieldPolicy = {
	reference?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MandateSingleUseKeySpecifier = ('amount' | 'currency' | MandateSingleUseKeySpecifier)[]
export type MandateSingleUseFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MarkAreaDataKeySpecifier = (
	| 'type'
	| 'valueDim'
	| 'valueIndex'
	| 'xAxis'
	| 'yAxis'
	| MarkAreaDataKeySpecifier
)[]
export type MarkAreaDataFieldPolicy = {
	type?: FieldPolicy<any> | FieldReadFunction<any>
	valueDim?: FieldPolicy<any> | FieldReadFunction<any>
	valueIndex?: FieldPolicy<any> | FieldReadFunction<any>
	xAxis?: FieldPolicy<any> | FieldReadFunction<any>
	yAxis?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MarkLineDataKeySpecifier = (
	| 'type'
	| 'valueDim'
	| 'valueIndex'
	| 'xAxis'
	| 'yAxis'
	| MarkLineDataKeySpecifier
)[]
export type MarkLineDataFieldPolicy = {
	type?: FieldPolicy<any> | FieldReadFunction<any>
	valueDim?: FieldPolicy<any> | FieldReadFunction<any>
	valueIndex?: FieldPolicy<any> | FieldReadFunction<any>
	xAxis?: FieldPolicy<any> | FieldReadFunction<any>
	yAxis?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MarkerPositionKeySpecifier = (
	| 'angleAxis'
	| 'coord'
	| 'radiusAxis'
	| 'type'
	| 'value'
	| 'valueDim'
	| 'valueIndex'
	| 'x'
	| 'xAxis'
	| 'y'
	| 'yAxis'
	| MarkerPositionKeySpecifier
)[]
export type MarkerPositionFieldPolicy = {
	angleAxis?: FieldPolicy<any> | FieldReadFunction<any>
	coord?: FieldPolicy<any> | FieldReadFunction<any>
	radiusAxis?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	value?: FieldPolicy<any> | FieldReadFunction<any>
	valueDim?: FieldPolicy<any> | FieldReadFunction<any>
	valueIndex?: FieldPolicy<any> | FieldReadFunction<any>
	x?: FieldPolicy<any> | FieldReadFunction<any>
	xAxis?: FieldPolicy<any> | FieldReadFunction<any>
	y?: FieldPolicy<any> | FieldReadFunction<any>
	yAxis?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MarketingFeatureKeySpecifier = ('name' | MarketingFeatureKeySpecifier)[]
export type MarketingFeatureFieldPolicy = {
	name?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MultibancoKeySpecifier = (
	| 'entity'
	| 'reference'
	| 'refund_account_holder_address_city'
	| 'refund_account_holder_address_country'
	| 'refund_account_holder_address_line1'
	| 'refund_account_holder_address_line2'
	| 'refund_account_holder_address_postal_code'
	| 'refund_account_holder_address_state'
	| 'refund_account_holder_name'
	| 'refund_iban'
	| MultibancoKeySpecifier
)[]
export type MultibancoFieldPolicy = {
	entity?: FieldPolicy<any> | FieldReadFunction<any>
	reference?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_city?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_country?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_line1?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_line2?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_postal_code?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_state?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_name?: FieldPolicy<any> | FieldReadFunction<any>
	refund_iban?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MutationKeySpecifier = (
	| 'answerInvitation'
	| 'applyCustomerBalance'
	| 'attachProductToTags'
	| 'attachProgramToTags'
	| 'block'
	| 'cancel'
	| 'capture'
	| 'claimTag'
	| 'confirm'
	| 'createActivation'
	| 'createBalanceTransaction'
	| 'createComment'
	| 'createComments'
	| 'createFederatedUser'
	| 'createFundingInstructions'
	| 'createInstallation'
	| 'createInvitation'
	| 'createInvitations'
	| 'createNotification'
	| 'createNotifications'
	| 'createOidcClient'
	| 'createOidcClients'
	| 'createOrganization'
	| 'createOrganizations'
	| 'createProduct'
	| 'createProducts'
	| 'createProgram'
	| 'createPrograms'
	| 'createRate'
	| 'createRates'
	| 'createReaction'
	| 'createReactions'
	| 'createRole'
	| 'createRoles'
	| 'createSource'
	| 'createTag'
	| 'createTags'
	| 'createTaxId'
	| 'createUser'
	| 'createUsers'
	| 'createWebhook'
	| 'createWebhooks'
	| 'customerCreate'
	| 'customerDelete'
	| 'customerUpdate'
	| 'deleteActivation'
	| 'deleteComment'
	| 'deleteDiscount'
	| 'deleteFederatedUser'
	| 'deleteInstallation'
	| 'deleteInvitation'
	| 'deleteOidcClient'
	| 'deleteOrganization'
	| 'deleteProduct'
	| 'deleteProgram'
	| 'deleteRate'
	| 'deleteReaction'
	| 'deleteRole'
	| 'deleteSource'
	| 'deleteTag'
	| 'deleteTaxId'
	| 'deleteUser'
	| 'deleteWebhook'
	| 'detachProductFromTags'
	| 'detachProgramFromTags'
	| 'follow'
	| 'incrementAuthorization'
	| 'invoiceCreate'
	| 'invoiceDelete'
	| 'invoiceFinalize'
	| 'invoiceMarkUncollectible'
	| 'invoicePay'
	| 'invoiceSend'
	| 'invoiceUpdate'
	| 'invoiceVoid'
	| 'markAllReadNotifications'
	| 'meChangePassword'
	| 'meSendVerification'
	| 'organizationSendVerification'
	| 'participateInPerk'
	| 'paymentIntentCreate'
	| 'paymentIntentUpdate'
	| 'paymentMethodAttach'
	| 'paymentMethodCreate'
	| 'paymentMethodDetach'
	| 'paymentMethodUpdate'
	| 'releaseTag'
	| 'stripeSubscriptionCreate'
	| 'stripeSubscriptionDelete'
	| 'stripeSubscriptionDeleteDiscount'
	| 'stripeSubscriptionUpdate'
	| 'transferTag'
	| 'unFollow'
	| 'unblock'
	| 'updateActivation'
	| 'updateBalanceTransaction'
	| 'updateCashBalance'
	| 'updateComment'
	| 'updateCustomer'
	| 'updateInvitation'
	| 'updateNotification'
	| 'updateOidcClient'
	| 'updateOrganization'
	| 'updateProduct'
	| 'updateProgram'
	| 'updateRate'
	| 'updateReaction'
	| 'updateRole'
	| 'updateSource'
	| 'updateTag'
	| 'updateUser'
	| 'updateWebhook'
	| 'verifyMicrodeposits'
	| 'verifySource'
	| 'withdrawFromPerk'
	| MutationKeySpecifier
)[]
export type MutationFieldPolicy = {
	answerInvitation?: FieldPolicy<any> | FieldReadFunction<any>
	applyCustomerBalance?: FieldPolicy<any> | FieldReadFunction<any>
	attachProductToTags?: FieldPolicy<any> | FieldReadFunction<any>
	attachProgramToTags?: FieldPolicy<any> | FieldReadFunction<any>
	block?: FieldPolicy<any> | FieldReadFunction<any>
	cancel?: FieldPolicy<any> | FieldReadFunction<any>
	capture?: FieldPolicy<any> | FieldReadFunction<any>
	claimTag?: FieldPolicy<any> | FieldReadFunction<any>
	confirm?: FieldPolicy<any> | FieldReadFunction<any>
	createActivation?: FieldPolicy<any> | FieldReadFunction<any>
	createBalanceTransaction?: FieldPolicy<any> | FieldReadFunction<any>
	createComment?: FieldPolicy<any> | FieldReadFunction<any>
	createComments?: FieldPolicy<any> | FieldReadFunction<any>
	createFederatedUser?: FieldPolicy<any> | FieldReadFunction<any>
	createFundingInstructions?: FieldPolicy<any> | FieldReadFunction<any>
	createInstallation?: FieldPolicy<any> | FieldReadFunction<any>
	createInvitation?: FieldPolicy<any> | FieldReadFunction<any>
	createInvitations?: FieldPolicy<any> | FieldReadFunction<any>
	createNotification?: FieldPolicy<any> | FieldReadFunction<any>
	createNotifications?: FieldPolicy<any> | FieldReadFunction<any>
	createOidcClient?: FieldPolicy<any> | FieldReadFunction<any>
	createOidcClients?: FieldPolicy<any> | FieldReadFunction<any>
	createOrganization?: FieldPolicy<any> | FieldReadFunction<any>
	createOrganizations?: FieldPolicy<any> | FieldReadFunction<any>
	createProduct?: FieldPolicy<any> | FieldReadFunction<any>
	createProducts?: FieldPolicy<any> | FieldReadFunction<any>
	createProgram?: FieldPolicy<any> | FieldReadFunction<any>
	createPrograms?: FieldPolicy<any> | FieldReadFunction<any>
	createRate?: FieldPolicy<any> | FieldReadFunction<any>
	createRates?: FieldPolicy<any> | FieldReadFunction<any>
	createReaction?: FieldPolicy<any> | FieldReadFunction<any>
	createReactions?: FieldPolicy<any> | FieldReadFunction<any>
	createRole?: FieldPolicy<any> | FieldReadFunction<any>
	createRoles?: FieldPolicy<any> | FieldReadFunction<any>
	createSource?: FieldPolicy<any> | FieldReadFunction<any>
	createTag?: FieldPolicy<any> | FieldReadFunction<any>
	createTags?: FieldPolicy<any> | FieldReadFunction<any>
	createTaxId?: FieldPolicy<any> | FieldReadFunction<any>
	createUser?: FieldPolicy<any> | FieldReadFunction<any>
	createUsers?: FieldPolicy<any> | FieldReadFunction<any>
	createWebhook?: FieldPolicy<any> | FieldReadFunction<any>
	createWebhooks?: FieldPolicy<any> | FieldReadFunction<any>
	customerCreate?: FieldPolicy<any> | FieldReadFunction<any>
	customerDelete?: FieldPolicy<any> | FieldReadFunction<any>
	customerUpdate?: FieldPolicy<any> | FieldReadFunction<any>
	deleteActivation?: FieldPolicy<any> | FieldReadFunction<any>
	deleteComment?: FieldPolicy<any> | FieldReadFunction<any>
	deleteDiscount?: FieldPolicy<any> | FieldReadFunction<any>
	deleteFederatedUser?: FieldPolicy<any> | FieldReadFunction<any>
	deleteInstallation?: FieldPolicy<any> | FieldReadFunction<any>
	deleteInvitation?: FieldPolicy<any> | FieldReadFunction<any>
	deleteOidcClient?: FieldPolicy<any> | FieldReadFunction<any>
	deleteOrganization?: FieldPolicy<any> | FieldReadFunction<any>
	deleteProduct?: FieldPolicy<any> | FieldReadFunction<any>
	deleteProgram?: FieldPolicy<any> | FieldReadFunction<any>
	deleteRate?: FieldPolicy<any> | FieldReadFunction<any>
	deleteReaction?: FieldPolicy<any> | FieldReadFunction<any>
	deleteRole?: FieldPolicy<any> | FieldReadFunction<any>
	deleteSource?: FieldPolicy<any> | FieldReadFunction<any>
	deleteTag?: FieldPolicy<any> | FieldReadFunction<any>
	deleteTaxId?: FieldPolicy<any> | FieldReadFunction<any>
	deleteUser?: FieldPolicy<any> | FieldReadFunction<any>
	deleteWebhook?: FieldPolicy<any> | FieldReadFunction<any>
	detachProductFromTags?: FieldPolicy<any> | FieldReadFunction<any>
	detachProgramFromTags?: FieldPolicy<any> | FieldReadFunction<any>
	follow?: FieldPolicy<any> | FieldReadFunction<any>
	incrementAuthorization?: FieldPolicy<any> | FieldReadFunction<any>
	invoiceCreate?: FieldPolicy<any> | FieldReadFunction<any>
	invoiceDelete?: FieldPolicy<any> | FieldReadFunction<any>
	invoiceFinalize?: FieldPolicy<any> | FieldReadFunction<any>
	invoiceMarkUncollectible?: FieldPolicy<any> | FieldReadFunction<any>
	invoicePay?: FieldPolicy<any> | FieldReadFunction<any>
	invoiceSend?: FieldPolicy<any> | FieldReadFunction<any>
	invoiceUpdate?: FieldPolicy<any> | FieldReadFunction<any>
	invoiceVoid?: FieldPolicy<any> | FieldReadFunction<any>
	markAllReadNotifications?: FieldPolicy<any> | FieldReadFunction<any>
	meChangePassword?: FieldPolicy<any> | FieldReadFunction<any>
	meSendVerification?: FieldPolicy<any> | FieldReadFunction<any>
	organizationSendVerification?: FieldPolicy<any> | FieldReadFunction<any>
	participateInPerk?: FieldPolicy<any> | FieldReadFunction<any>
	paymentIntentCreate?: FieldPolicy<any> | FieldReadFunction<any>
	paymentIntentUpdate?: FieldPolicy<any> | FieldReadFunction<any>
	paymentMethodAttach?: FieldPolicy<any> | FieldReadFunction<any>
	paymentMethodCreate?: FieldPolicy<any> | FieldReadFunction<any>
	paymentMethodDetach?: FieldPolicy<any> | FieldReadFunction<any>
	paymentMethodUpdate?: FieldPolicy<any> | FieldReadFunction<any>
	releaseTag?: FieldPolicy<any> | FieldReadFunction<any>
	stripeSubscriptionCreate?: FieldPolicy<any> | FieldReadFunction<any>
	stripeSubscriptionDelete?: FieldPolicy<any> | FieldReadFunction<any>
	stripeSubscriptionDeleteDiscount?: FieldPolicy<any> | FieldReadFunction<any>
	stripeSubscriptionUpdate?: FieldPolicy<any> | FieldReadFunction<any>
	transferTag?: FieldPolicy<any> | FieldReadFunction<any>
	unFollow?: FieldPolicy<any> | FieldReadFunction<any>
	unblock?: FieldPolicy<any> | FieldReadFunction<any>
	updateActivation?: FieldPolicy<any> | FieldReadFunction<any>
	updateBalanceTransaction?: FieldPolicy<any> | FieldReadFunction<any>
	updateCashBalance?: FieldPolicy<any> | FieldReadFunction<any>
	updateComment?: FieldPolicy<any> | FieldReadFunction<any>
	updateCustomer?: FieldPolicy<any> | FieldReadFunction<any>
	updateInvitation?: FieldPolicy<any> | FieldReadFunction<any>
	updateNotification?: FieldPolicy<any> | FieldReadFunction<any>
	updateOidcClient?: FieldPolicy<any> | FieldReadFunction<any>
	updateOrganization?: FieldPolicy<any> | FieldReadFunction<any>
	updateProduct?: FieldPolicy<any> | FieldReadFunction<any>
	updateProgram?: FieldPolicy<any> | FieldReadFunction<any>
	updateRate?: FieldPolicy<any> | FieldReadFunction<any>
	updateReaction?: FieldPolicy<any> | FieldReadFunction<any>
	updateRole?: FieldPolicy<any> | FieldReadFunction<any>
	updateSource?: FieldPolicy<any> | FieldReadFunction<any>
	updateTag?: FieldPolicy<any> | FieldReadFunction<any>
	updateUser?: FieldPolicy<any> | FieldReadFunction<any>
	updateWebhook?: FieldPolicy<any> | FieldReadFunction<any>
	verifyMicrodeposits?: FieldPolicy<any> | FieldReadFunction<any>
	verifySource?: FieldPolicy<any> | FieldReadFunction<any>
	withdrawFromPerk?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MyReactionKeySpecifier = ('_id' | 'type' | MyReactionKeySpecifier)[]
export type MyReactionFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NetworksKeySpecifier = ('available' | 'preferred' | NetworksKeySpecifier)[]
export type NetworksFieldPolicy = {
	available?: FieldPolicy<any> | FieldReadFunction<any>
	preferred?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionAlipayHandleRedirectKeySpecifier = (
	| 'native_data'
	| 'native_url'
	| 'return_url'
	| 'url'
	| NextActionAlipayHandleRedirectKeySpecifier
)[]
export type NextActionAlipayHandleRedirectFieldPolicy = {
	native_data?: FieldPolicy<any> | FieldReadFunction<any>
	native_url?: FieldPolicy<any> | FieldReadFunction<any>
	return_url?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsKeySpecifier = (
	| 'expires_at'
	| 'hosted_voucher_url'
	| 'stores'
	| NextActionAlipayHandleRedirectKonbiniDisplayDetailsKeySpecifier
)[]
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsFieldPolicy = {
	expires_at?: FieldPolicy<any> | FieldReadFunction<any>
	hosted_voucher_url?: FieldPolicy<any> | FieldReadFunction<any>
	stores?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresKeySpecifier = (
	| 'familymart'
	| 'lawson'
	| 'ministop'
	| 'seicomart'
	| NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresKeySpecifier
)[]
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresFieldPolicy = {
	familymart?: FieldPolicy<any> | FieldReadFunction<any>
	lawson?: FieldPolicy<any> | FieldReadFunction<any>
	ministop?: FieldPolicy<any> | FieldReadFunction<any>
	seicomart?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresFamilymartKeySpecifier = (
	| 'confirmation_number'
	| 'payment_code'
	| NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresFamilymartKeySpecifier
)[]
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresFamilymartFieldPolicy = {
	confirmation_number?: FieldPolicy<any> | FieldReadFunction<any>
	payment_code?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresLawsonKeySpecifier = (
	| 'confirmation_number'
	| 'payment_code'
	| NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresLawsonKeySpecifier
)[]
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresLawsonFieldPolicy = {
	confirmation_number?: FieldPolicy<any> | FieldReadFunction<any>
	payment_code?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresMinistopKeySpecifier = (
	| 'confirmation_number'
	| 'payment_code'
	| NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresMinistopKeySpecifier
)[]
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresMinistopFieldPolicy = {
	confirmation_number?: FieldPolicy<any> | FieldReadFunction<any>
	payment_code?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresSeicomartKeySpecifier = (
	| 'confirmation_number'
	| 'payment_code'
	| NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresSeicomartKeySpecifier
)[]
export type NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresSeicomartFieldPolicy = {
	confirmation_number?: FieldPolicy<any> | FieldReadFunction<any>
	payment_code?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionAlipayHandleRedirectOxxoDisplayDetailsKeySpecifier = (
	| 'expires_after'
	| 'hosted_voucher_url'
	| 'number'
	| NextActionAlipayHandleRedirectOxxoDisplayDetailsKeySpecifier
)[]
export type NextActionAlipayHandleRedirectOxxoDisplayDetailsFieldPolicy = {
	expires_after?: FieldPolicy<any> | FieldReadFunction<any>
	hosted_voucher_url?: FieldPolicy<any> | FieldReadFunction<any>
	number?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionAlipayHandleRedirectPaynowDisplayQrCodeKeySpecifier = (
	| 'data'
	| 'hosted_instructions_url'
	| 'image_url_png'
	| 'image_url_svg'
	| NextActionAlipayHandleRedirectPaynowDisplayQrCodeKeySpecifier
)[]
export type NextActionAlipayHandleRedirectPaynowDisplayQrCodeFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	hosted_instructions_url?: FieldPolicy<any> | FieldReadFunction<any>
	image_url_png?: FieldPolicy<any> | FieldReadFunction<any>
	image_url_svg?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionAlipayHandleRedirectRedirectToUrlKeySpecifier = (
	| 'return_url'
	| 'url'
	| NextActionAlipayHandleRedirectRedirectToUrlKeySpecifier
)[]
export type NextActionAlipayHandleRedirectRedirectToUrlFieldPolicy = {
	return_url?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionBoletoDisplayDetailsKeySpecifier = (
	| 'expires_at'
	| 'hosted_voucher_url'
	| 'number'
	| 'pdf'
	| NextActionBoletoDisplayDetailsKeySpecifier
)[]
export type NextActionBoletoDisplayDetailsFieldPolicy = {
	expires_at?: FieldPolicy<any> | FieldReadFunction<any>
	hosted_voucher_url?: FieldPolicy<any> | FieldReadFunction<any>
	number?: FieldPolicy<any> | FieldReadFunction<any>
	pdf?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionCardAwaitNotificationKeySpecifier = (
	| 'charge_attempt_at'
	| 'customer_approval_required'
	| NextActionCardAwaitNotificationKeySpecifier
)[]
export type NextActionCardAwaitNotificationFieldPolicy = {
	charge_attempt_at?: FieldPolicy<any> | FieldReadFunction<any>
	customer_approval_required?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionDisplayBankTransferInstructionsKeySpecifier = (
	| 'amount_remaining'
	| 'currency'
	| 'financial_addresses'
	| 'hosted_instructions_url'
	| 'reference'
	| 'type'
	| NextActionDisplayBankTransferInstructionsKeySpecifier
)[]
export type NextActionDisplayBankTransferInstructionsFieldPolicy = {
	amount_remaining?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	financial_addresses?: FieldPolicy<any> | FieldReadFunction<any>
	hosted_instructions_url?: FieldPolicy<any> | FieldReadFunction<any>
	reference?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionDisplayBankTransferInstructionsFinancialAddressKeySpecifier = (
	| 'iban'
	| 'sort_code'
	| 'spei'
	| 'supported_networks'
	| 'type'
	| 'zengin'
	| NextActionDisplayBankTransferInstructionsFinancialAddressKeySpecifier
)[]
export type NextActionDisplayBankTransferInstructionsFinancialAddressFieldPolicy = {
	iban?: FieldPolicy<any> | FieldReadFunction<any>
	sort_code?: FieldPolicy<any> | FieldReadFunction<any>
	spei?: FieldPolicy<any> | FieldReadFunction<any>
	supported_networks?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	zengin?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionDisplayBankTransferInstructionsFinancialAddressIbanKeySpecifier = (
	| 'account_holder_address'
	| 'account_holder_name'
	| 'bank_address'
	| 'bic'
	| 'country'
	| 'iban'
	| NextActionDisplayBankTransferInstructionsFinancialAddressIbanKeySpecifier
)[]
export type NextActionDisplayBankTransferInstructionsFinancialAddressIbanFieldPolicy = {
	account_holder_address?: FieldPolicy<any> | FieldReadFunction<any>
	account_holder_name?: FieldPolicy<any> | FieldReadFunction<any>
	bank_address?: FieldPolicy<any> | FieldReadFunction<any>
	bic?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	iban?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionDisplayBankTransferInstructionsFinancialAddressSortCodeKeySpecifier = (
	| 'account_holder_address'
	| 'account_holder_name'
	| 'account_number'
	| 'bank_address'
	| 'sort_code'
	| NextActionDisplayBankTransferInstructionsFinancialAddressSortCodeKeySpecifier
)[]
export type NextActionDisplayBankTransferInstructionsFinancialAddressSortCodeFieldPolicy = {
	account_holder_address?: FieldPolicy<any> | FieldReadFunction<any>
	account_holder_name?: FieldPolicy<any> | FieldReadFunction<any>
	account_number?: FieldPolicy<any> | FieldReadFunction<any>
	bank_address?: FieldPolicy<any> | FieldReadFunction<any>
	sort_code?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionDisplayBankTransferInstructionsFinancialAddressSpeiKeySpecifier = (
	| 'account_holder_address'
	| 'account_holder_name'
	| 'bank_address'
	| 'bank_code'
	| 'bank_name'
	| 'clabe'
	| NextActionDisplayBankTransferInstructionsFinancialAddressSpeiKeySpecifier
)[]
export type NextActionDisplayBankTransferInstructionsFinancialAddressSpeiFieldPolicy = {
	account_holder_address?: FieldPolicy<any> | FieldReadFunction<any>
	account_holder_name?: FieldPolicy<any> | FieldReadFunction<any>
	bank_address?: FieldPolicy<any> | FieldReadFunction<any>
	bank_code?: FieldPolicy<any> | FieldReadFunction<any>
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	clabe?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionDisplayBankTransferInstructionsFinancialAddressZenginKeySpecifier = (
	| 'account_holder_address'
	| 'account_holder_name'
	| 'account_number'
	| 'account_type'
	| 'bank_address'
	| 'bank_code'
	| 'bank_name'
	| 'branch_code'
	| 'branch_name'
	| NextActionDisplayBankTransferInstructionsFinancialAddressZenginKeySpecifier
)[]
export type NextActionDisplayBankTransferInstructionsFinancialAddressZenginFieldPolicy = {
	account_holder_address?: FieldPolicy<any> | FieldReadFunction<any>
	account_holder_name?: FieldPolicy<any> | FieldReadFunction<any>
	account_number?: FieldPolicy<any> | FieldReadFunction<any>
	account_type?: FieldPolicy<any> | FieldReadFunction<any>
	bank_address?: FieldPolicy<any> | FieldReadFunction<any>
	bank_code?: FieldPolicy<any> | FieldReadFunction<any>
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	branch_code?: FieldPolicy<any> | FieldReadFunction<any>
	branch_name?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionDisplayDetailsKeySpecifier = (
	| 'email_sent'
	| 'expires_at'
	| NextActionDisplayDetailsKeySpecifier
)[]
export type NextActionDisplayDetailsFieldPolicy = {
	email_sent?: FieldPolicy<any> | FieldReadFunction<any>
	expires_at?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionDisplayDetailsEmailSentKeySpecifier = (
	| 'email_sent_at'
	| 'email_sent_to'
	| NextActionDisplayDetailsEmailSentKeySpecifier
)[]
export type NextActionDisplayDetailsEmailSentFieldPolicy = {
	email_sent_at?: FieldPolicy<any> | FieldReadFunction<any>
	email_sent_to?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionRedirectToUrlKeySpecifier = ('return_url' | 'url' | NextActionRedirectToUrlKeySpecifier)[]
export type NextActionRedirectToUrlFieldPolicy = {
	return_url?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionVerifyWithMicrodepositsKeySpecifier = (
	| 'arrival_date'
	| 'hosted_verification_url'
	| 'microdeposit_type'
	| NextActionVerifyWithMicrodepositsKeySpecifier
)[]
export type NextActionVerifyWithMicrodepositsFieldPolicy = {
	arrival_date?: FieldPolicy<any> | FieldReadFunction<any>
	hosted_verification_url?: FieldPolicy<any> | FieldReadFunction<any>
	microdeposit_type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionWechatPayDisplayQrCodeKeySpecifier = (
	| 'data'
	| 'hosted_instructions_url'
	| 'image_data_url'
	| 'image_url_png'
	| 'image_url_svg'
	| NextActionWechatPayDisplayQrCodeKeySpecifier
)[]
export type NextActionWechatPayDisplayQrCodeFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	hosted_instructions_url?: FieldPolicy<any> | FieldReadFunction<any>
	image_data_url?: FieldPolicy<any> | FieldReadFunction<any>
	image_url_png?: FieldPolicy<any> | FieldReadFunction<any>
	image_url_svg?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionWechatPayRedirectToAndroidAppKeySpecifier = (
	| 'app_id'
	| 'nonce_str'
	| 'package'
	| 'partner_id'
	| 'prepay_id'
	| 'sign'
	| 'timestamp'
	| NextActionWechatPayRedirectToAndroidAppKeySpecifier
)[]
export type NextActionWechatPayRedirectToAndroidAppFieldPolicy = {
	app_id?: FieldPolicy<any> | FieldReadFunction<any>
	nonce_str?: FieldPolicy<any> | FieldReadFunction<any>
	package?: FieldPolicy<any> | FieldReadFunction<any>
	partner_id?: FieldPolicy<any> | FieldReadFunction<any>
	prepay_id?: FieldPolicy<any> | FieldReadFunction<any>
	sign?: FieldPolicy<any> | FieldReadFunction<any>
	timestamp?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NextActionWechatPayRedirectToIosAppKeySpecifier = (
	| 'native_url'
	| NextActionWechatPayRedirectToIosAppKeySpecifier
)[]
export type NextActionWechatPayRedirectToIosAppFieldPolicy = {
	native_url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NotificationKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'data'
	| 'deletedAt'
	| 'message'
	| 'parent'
	| 'parentId'
	| 'title'
	| 'type'
	| 'updatedAt'
	| 'userId'
	| 'viewed'
	| NotificationKeySpecifier
)[]
export type NotificationFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	data?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	message?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
	parentId?: FieldPolicy<any> | FieldReadFunction<any>
	title?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
	viewed?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NotificationCMarkAllReadResultKeySpecifier = ('_id' | NotificationCMarkAllReadResultKeySpecifier)[]
export type NotificationCMarkAllReadResultFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
}
export type OfflineKeySpecifier = ('stored_at' | 'type' | OfflineKeySpecifier)[]
export type OfflineFieldPolicy = {
	stored_at?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type OidcClientKeySpecifier = (
	| '_id'
	| 'application_type'
	| 'client_id'
	| 'client_name'
	| 'client_uri'
	| 'contacts'
	| 'createdAt'
	| 'deletedAt'
	| 'grant_types'
	| 'logo_uri'
	| 'organization'
	| 'organizationId'
	| 'policy_uri'
	| 'redirect_uris'
	| 'response_types'
	| 'tos_uri'
	| 'updatedAt'
	| OidcClientKeySpecifier
)[]
export type OidcClientFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	application_type?: FieldPolicy<any> | FieldReadFunction<any>
	client_id?: FieldPolicy<any> | FieldReadFunction<any>
	client_name?: FieldPolicy<any> | FieldReadFunction<any>
	client_uri?: FieldPolicy<any> | FieldReadFunction<any>
	contacts?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	grant_types?: FieldPolicy<any> | FieldReadFunction<any>
	logo_uri?: FieldPolicy<any> | FieldReadFunction<any>
	organization?: FieldPolicy<any> | FieldReadFunction<any>
	organizationId?: FieldPolicy<any> | FieldReadFunction<any>
	policy_uri?: FieldPolicy<any> | FieldReadFunction<any>
	redirect_uris?: FieldPolicy<any> | FieldReadFunction<any>
	response_types?: FieldPolicy<any> | FieldReadFunction<any>
	tos_uri?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type OrganizationKeySpecifier = (
	| '_id'
	| 'activations'
	| 'billing'
	| 'billingVerified'
	| 'contactEmail'
	| 'contactEmailVerified'
	| 'contactPhoneNumber'
	| 'contactPhoneNumberVerified'
	| 'createdAt'
	| 'customDomains'
	| 'customerId'
	| 'deletedAt'
	| 'description'
	| 'emailTemplates'
	| 'logo'
	| 'name'
	| 'oidcClients'
	| 'programs'
	| 'tag'
	| 'tags'
	| 'template'
	| 'updatedAt'
	| 'users'
	| 'webhooks'
	| 'website'
	| OrganizationKeySpecifier
)[]
export type OrganizationFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	activations?: FieldPolicy<any> | FieldReadFunction<any>
	billing?: FieldPolicy<any> | FieldReadFunction<any>
	billingVerified?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmail?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	contactPhoneNumber?: FieldPolicy<any> | FieldReadFunction<any>
	contactPhoneNumberVerified?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	customDomains?: FieldPolicy<any> | FieldReadFunction<any>
	customerId?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	emailTemplates?: FieldPolicy<any> | FieldReadFunction<any>
	logo?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	oidcClients?: FieldPolicy<any> | FieldReadFunction<any>
	programs?: FieldPolicy<any> | FieldReadFunction<any>
	tag?: FieldPolicy<any> | FieldReadFunction<any>
	tags?: FieldPolicy<any> | FieldReadFunction<any>
	template?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	users?: FieldPolicy<any> | FieldReadFunction<any>
	webhooks?: FieldPolicy<any> | FieldReadFunction<any>
	website?: FieldPolicy<any> | FieldReadFunction<any>
}
export type OrganizationTemplateKeySpecifier = (
	| 'backgroundColor'
	| 'backgroundImageUrl'
	| 'footerMessage'
	| 'primaryColor'
	| 'primaryTextColor'
	| 'secondaryColor'
	| 'secondaryTextColor'
	| 'tertiaryColor'
	| 'tertiaryTextColor'
	| 'textColor'
	| 'zendeskKey'
	| OrganizationTemplateKeySpecifier
)[]
export type OrganizationTemplateFieldPolicy = {
	backgroundColor?: FieldPolicy<any> | FieldReadFunction<any>
	backgroundImageUrl?: FieldPolicy<any> | FieldReadFunction<any>
	footerMessage?: FieldPolicy<any> | FieldReadFunction<any>
	primaryColor?: FieldPolicy<any> | FieldReadFunction<any>
	primaryTextColor?: FieldPolicy<any> | FieldReadFunction<any>
	secondaryColor?: FieldPolicy<any> | FieldReadFunction<any>
	secondaryTextColor?: FieldPolicy<any> | FieldReadFunction<any>
	tertiaryColor?: FieldPolicy<any> | FieldReadFunction<any>
	tertiaryTextColor?: FieldPolicy<any> | FieldReadFunction<any>
	textColor?: FieldPolicy<any> | FieldReadFunction<any>
	zendeskKey?: FieldPolicy<any> | FieldReadFunction<any>
}
export type OrganizationWithRoleKeySpecifier = (
	| '_id'
	| 'billing'
	| 'billingVerified'
	| 'contactEmail'
	| 'contactEmailVerified'
	| 'contactPhoneNumber'
	| 'contactPhoneNumberVerified'
	| 'createdAt'
	| 'customDomains'
	| 'deletedAt'
	| 'description'
	| 'emailTemplates'
	| 'logo'
	| 'name'
	| 'oidcClients'
	| 'role'
	| 'tag'
	| 'template'
	| 'updatedAt'
	| 'users'
	| 'webhooks'
	| 'website'
	| OrganizationWithRoleKeySpecifier
)[]
export type OrganizationWithRoleFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	billing?: FieldPolicy<any> | FieldReadFunction<any>
	billingVerified?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmail?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	contactPhoneNumber?: FieldPolicy<any> | FieldReadFunction<any>
	contactPhoneNumberVerified?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	customDomains?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	emailTemplates?: FieldPolicy<any> | FieldReadFunction<any>
	logo?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	oidcClients?: FieldPolicy<any> | FieldReadFunction<any>
	role?: FieldPolicy<any> | FieldReadFunction<any>
	tag?: FieldPolicy<any> | FieldReadFunction<any>
	template?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	users?: FieldPolicy<any> | FieldReadFunction<any>
	webhooks?: FieldPolicy<any> | FieldReadFunction<any>
	website?: FieldPolicy<any> | FieldReadFunction<any>
}
export type OutcomeKeySpecifier = (
	| 'advice_code'
	| 'network_advice_code'
	| 'network_decline_code'
	| 'network_status'
	| 'reason'
	| 'risk_level'
	| 'risk_score'
	| 'rule'
	| 'seller_message'
	| 'type'
	| OutcomeKeySpecifier
)[]
export type OutcomeFieldPolicy = {
	advice_code?: FieldPolicy<any> | FieldReadFunction<any>
	network_advice_code?: FieldPolicy<any> | FieldReadFunction<any>
	network_decline_code?: FieldPolicy<any> | FieldReadFunction<any>
	network_status?: FieldPolicy<any> | FieldReadFunction<any>
	reason?: FieldPolicy<any> | FieldReadFunction<any>
	risk_level?: FieldPolicy<any> | FieldReadFunction<any>
	risk_score?: FieldPolicy<any> | FieldReadFunction<any>
	rule?: FieldPolicy<any> | FieldReadFunction<any>
	seller_message?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type OutcomeRuleKeySpecifier = ('action' | 'id' | 'predicate' | OutcomeRuleKeySpecifier)[]
export type OutcomeRuleFieldPolicy = {
	action?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	predicate?: FieldPolicy<any> | FieldReadFunction<any>
}
export type OwnerKeySpecifier = (
	| 'address'
	| 'email'
	| 'name'
	| 'phone'
	| 'verified_address'
	| 'verified_email'
	| 'verified_name'
	| 'verified_phone'
	| OwnerKeySpecifier
)[]
export type OwnerFieldPolicy = {
	address?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	phone?: FieldPolicy<any> | FieldReadFunction<any>
	verified_address?: FieldPolicy<any> | FieldReadFunction<any>
	verified_email?: FieldPolicy<any> | FieldReadFunction<any>
	verified_name?: FieldPolicy<any> | FieldReadFunction<any>
	verified_phone?: FieldPolicy<any> | FieldReadFunction<any>
}
export type OwnershipDeclarationKeySpecifier = ('date' | 'ip' | 'user_agent' | OwnershipDeclarationKeySpecifier)[]
export type OwnershipDeclarationFieldPolicy = {
	date?: FieldPolicy<any> | FieldReadFunction<any>
	ip?: FieldPolicy<any> | FieldReadFunction<any>
	user_agent?: FieldPolicy<any> | FieldReadFunction<any>
}
export type P24KeySpecifier = ('bank' | P24KeySpecifier)[]
export type P24FieldPolicy = {
	bank?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PackageDimensionsKeySpecifier = ('height' | 'length' | 'weight' | 'width' | PackageDimensionsKeySpecifier)[]
export type PackageDimensionsFieldPolicy = {
	height?: FieldPolicy<any> | FieldReadFunction<any>
	length?: FieldPolicy<any> | FieldReadFunction<any>
	weight?: FieldPolicy<any> | FieldReadFunction<any>
	width?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedActivationsKeySpecifier = ('nodes' | 'totalCount' | PaginatedActivationsKeySpecifier)[]
export type PaginatedActivationsFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedAdminsKeySpecifier = ('nodes' | 'totalCount' | PaginatedAdminsKeySpecifier)[]
export type PaginatedAdminsFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedAuditEventKeySpecifier = ('nodes' | 'totalCount' | PaginatedAuditEventKeySpecifier)[]
export type PaginatedAuditEventFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedCommentsKeySpecifier = ('nodes' | 'totalCount' | PaginatedCommentsKeySpecifier)[]
export type PaginatedCommentsFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedFederatedUserKeySpecifier = ('nodes' | 'totalCount' | PaginatedFederatedUserKeySpecifier)[]
export type PaginatedFederatedUserFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedInstallationKeySpecifier = ('nodes' | 'totalCount' | PaginatedInstallationKeySpecifier)[]
export type PaginatedInstallationFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedInvitationKeySpecifier = ('nodes' | 'totalCount' | PaginatedInvitationKeySpecifier)[]
export type PaginatedInvitationFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedNotificationKeySpecifier = ('nodes' | 'totalCount' | PaginatedNotificationKeySpecifier)[]
export type PaginatedNotificationFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedOidcClientKeySpecifier = ('nodes' | 'totalCount' | PaginatedOidcClientKeySpecifier)[]
export type PaginatedOidcClientFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedOrganizationKeySpecifier = ('nodes' | 'totalCount' | PaginatedOrganizationKeySpecifier)[]
export type PaginatedOrganizationFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedPerksKeySpecifier = ('nodes' | 'totalCount' | PaginatedPerksKeySpecifier)[]
export type PaginatedPerksFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedProductsKeySpecifier = ('nodes' | 'totalCount' | PaginatedProductsKeySpecifier)[]
export type PaginatedProductsFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedProgramsKeySpecifier = ('nodes' | 'totalCount' | PaginatedProgramsKeySpecifier)[]
export type PaginatedProgramsFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedRateKeySpecifier = ('nodes' | 'totalCount' | PaginatedRateKeySpecifier)[]
export type PaginatedRateFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedReactionKeySpecifier = ('nodes' | 'totalCount' | PaginatedReactionKeySpecifier)[]
export type PaginatedReactionFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedRoleKeySpecifier = ('nodes' | 'totalCount' | PaginatedRoleKeySpecifier)[]
export type PaginatedRoleFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedTagsKeySpecifier = ('nodes' | 'totalCount' | PaginatedTagsKeySpecifier)[]
export type PaginatedTagsFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedUserKeySpecifier = ('nodes' | 'totalCount' | PaginatedUserKeySpecifier)[]
export type PaginatedUserFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaginatedWebhooksKeySpecifier = ('nodes' | 'totalCount' | PaginatedWebhooksKeySpecifier)[]
export type PaginatedWebhooksFieldPolicy = {
	nodes?: FieldPolicy<any> | FieldReadFunction<any>
	totalCount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PauseCollectionKeySpecifier = ('behavior' | 'resumes_at' | PauseCollectionKeySpecifier)[]
export type PauseCollectionFieldPolicy = {
	behavior?: FieldPolicy<any> | FieldReadFunction<any>
	resumes_at?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentIntentKeySpecifier = (
	| 'amount'
	| 'amount_capturable'
	| 'amount_received'
	| 'application'
	| 'application_fee_amount'
	| 'automatic_payment_methods'
	| 'canceled_at'
	| 'cancellation_reason'
	| 'capture_method'
	| 'client_secret'
	| 'confirmation_method'
	| 'created'
	| 'currency'
	| 'customer'
	| 'description'
	| 'id'
	| 'invoice'
	| 'last_payment_error'
	| 'latest_charge'
	| 'livemode'
	| 'metadata'
	| 'next_action'
	| 'object'
	| 'on_behalf_of'
	| 'payment_method'
	| 'payment_method_configuration_details'
	| 'payment_method_options'
	| 'payment_method_types'
	| 'processing'
	| 'receipt_email'
	| 'review'
	| 'setup_future_usage'
	| 'shipping'
	| 'source'
	| 'statement_descriptor'
	| 'statement_descriptor_suffix'
	| 'status'
	| 'transfer_data'
	| 'transfer_group'
	| PaymentIntentKeySpecifier
)[]
export type PaymentIntentFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_capturable?: FieldPolicy<any> | FieldReadFunction<any>
	amount_received?: FieldPolicy<any> | FieldReadFunction<any>
	application?: FieldPolicy<any> | FieldReadFunction<any>
	application_fee_amount?: FieldPolicy<any> | FieldReadFunction<any>
	automatic_payment_methods?: FieldPolicy<any> | FieldReadFunction<any>
	canceled_at?: FieldPolicy<any> | FieldReadFunction<any>
	cancellation_reason?: FieldPolicy<any> | FieldReadFunction<any>
	capture_method?: FieldPolicy<any> | FieldReadFunction<any>
	client_secret?: FieldPolicy<any> | FieldReadFunction<any>
	confirmation_method?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	last_payment_error?: FieldPolicy<any> | FieldReadFunction<any>
	latest_charge?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	next_action?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	on_behalf_of?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_configuration_details?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_options?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_types?: FieldPolicy<any> | FieldReadFunction<any>
	processing?: FieldPolicy<any> | FieldReadFunction<any>
	receipt_email?: FieldPolicy<any> | FieldReadFunction<any>
	review?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
	shipping?: FieldPolicy<any> | FieldReadFunction<any>
	source?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor_suffix?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_data?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_group?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentIntentListKeySpecifier = ('data' | 'has_more' | 'object' | 'url' | PaymentIntentListKeySpecifier)[]
export type PaymentIntentListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentIntentNextActionKeySpecifier = (
	| 'alipay_handle_redirect'
	| 'boleto_display_details'
	| 'card_await_notification'
	| 'display_bank_transfer_instructions'
	| 'konbini_display_details'
	| 'oxxo_display_details'
	| 'paynow_display_qr_code'
	| 'redirect_to_url'
	| 'type'
	| 'use_stripe_sdk'
	| 'verify_with_microdeposits'
	| 'wechat_pay_display_qr_code'
	| 'wechat_pay_redirect_to_android_app'
	| 'wechat_pay_redirect_to_ios_app'
	| PaymentIntentNextActionKeySpecifier
)[]
export type PaymentIntentNextActionFieldPolicy = {
	alipay_handle_redirect?: FieldPolicy<any> | FieldReadFunction<any>
	boleto_display_details?: FieldPolicy<any> | FieldReadFunction<any>
	card_await_notification?: FieldPolicy<any> | FieldReadFunction<any>
	display_bank_transfer_instructions?: FieldPolicy<any> | FieldReadFunction<any>
	konbini_display_details?: FieldPolicy<any> | FieldReadFunction<any>
	oxxo_display_details?: FieldPolicy<any> | FieldReadFunction<any>
	paynow_display_qr_code?: FieldPolicy<any> | FieldReadFunction<any>
	redirect_to_url?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	use_stripe_sdk?: FieldPolicy<any> | FieldReadFunction<any>
	verify_with_microdeposits?: FieldPolicy<any> | FieldReadFunction<any>
	wechat_pay_display_qr_code?: FieldPolicy<any> | FieldReadFunction<any>
	wechat_pay_redirect_to_android_app?: FieldPolicy<any> | FieldReadFunction<any>
	wechat_pay_redirect_to_ios_app?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentIntentSearchResultsKeySpecifier = (
	| 'data'
	| 'has_more'
	| 'next_page'
	| 'object'
	| 'total_count'
	| 'url'
	| PaymentIntentSearchResultsKeySpecifier
)[]
export type PaymentIntentSearchResultsFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	next_page?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	total_count?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodKeySpecifier = (
	| 'acss_debit'
	| 'affirm'
	| 'afterpay_clearpay'
	| 'alipay'
	| 'au_becs_debit'
	| 'bacs_debit'
	| 'bancontact'
	| 'billing_details'
	| 'boleto'
	| 'card'
	| 'card_present'
	| 'created'
	| 'customer'
	| 'customer_balance'
	| 'eps'
	| 'fpx'
	| 'giropay'
	| 'grabpay'
	| 'id'
	| 'ideal'
	| 'interac_present'
	| 'klarna'
	| 'konbini'
	| 'link'
	| 'livemode'
	| 'metadata'
	| 'object'
	| 'oxxo'
	| 'p24'
	| 'paynow'
	| 'radar_options'
	| 'sepa_debit'
	| 'sofort'
	| 'type'
	| 'us_bank_account'
	| 'wechat_pay'
	| PaymentMethodKeySpecifier
)[]
export type PaymentMethodFieldPolicy = {
	acss_debit?: FieldPolicy<any> | FieldReadFunction<any>
	affirm?: FieldPolicy<any> | FieldReadFunction<any>
	afterpay_clearpay?: FieldPolicy<any> | FieldReadFunction<any>
	alipay?: FieldPolicy<any> | FieldReadFunction<any>
	au_becs_debit?: FieldPolicy<any> | FieldReadFunction<any>
	bacs_debit?: FieldPolicy<any> | FieldReadFunction<any>
	bancontact?: FieldPolicy<any> | FieldReadFunction<any>
	billing_details?: FieldPolicy<any> | FieldReadFunction<any>
	boleto?: FieldPolicy<any> | FieldReadFunction<any>
	card?: FieldPolicy<any> | FieldReadFunction<any>
	card_present?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	customer_balance?: FieldPolicy<any> | FieldReadFunction<any>
	eps?: FieldPolicy<any> | FieldReadFunction<any>
	fpx?: FieldPolicy<any> | FieldReadFunction<any>
	giropay?: FieldPolicy<any> | FieldReadFunction<any>
	grabpay?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	ideal?: FieldPolicy<any> | FieldReadFunction<any>
	interac_present?: FieldPolicy<any> | FieldReadFunction<any>
	klarna?: FieldPolicy<any> | FieldReadFunction<any>
	konbini?: FieldPolicy<any> | FieldReadFunction<any>
	link?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	oxxo?: FieldPolicy<any> | FieldReadFunction<any>
	p24?: FieldPolicy<any> | FieldReadFunction<any>
	paynow?: FieldPolicy<any> | FieldReadFunction<any>
	radar_options?: FieldPolicy<any> | FieldReadFunction<any>
	sepa_debit?: FieldPolicy<any> | FieldReadFunction<any>
	sofort?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	us_bank_account?: FieldPolicy<any> | FieldReadFunction<any>
	wechat_pay?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodCardKeySpecifier = (
	| 'brand'
	| 'checks'
	| 'country'
	| 'description'
	| 'display_brand'
	| 'exp_month'
	| 'exp_year'
	| 'fingerprint'
	| 'funding'
	| 'generated_from'
	| 'iin'
	| 'issuer'
	| 'last4'
	| 'networks'
	| 'regulated_status'
	| 'three_d_secure_usage'
	| 'wallet'
	| PaymentMethodCardKeySpecifier
)[]
export type PaymentMethodCardFieldPolicy = {
	brand?: FieldPolicy<any> | FieldReadFunction<any>
	checks?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	display_brand?: FieldPolicy<any> | FieldReadFunction<any>
	exp_month?: FieldPolicy<any> | FieldReadFunction<any>
	exp_year?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	funding?: FieldPolicy<any> | FieldReadFunction<any>
	generated_from?: FieldPolicy<any> | FieldReadFunction<any>
	iin?: FieldPolicy<any> | FieldReadFunction<any>
	issuer?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	networks?: FieldPolicy<any> | FieldReadFunction<any>
	regulated_status?: FieldPolicy<any> | FieldReadFunction<any>
	three_d_secure_usage?: FieldPolicy<any> | FieldReadFunction<any>
	wallet?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodConfigurationDetailsKeySpecifier = (
	| 'id'
	| 'parent'
	| PaymentMethodConfigurationDetailsKeySpecifier
)[]
export type PaymentMethodConfigurationDetailsFieldPolicy = {
	id?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodDetailsKeySpecifier = ('card_present' | 'type' | PaymentMethodDetailsKeySpecifier)[]
export type PaymentMethodDetailsFieldPolicy = {
	card_present?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodListKeySpecifier = ('data' | 'has_more' | 'object' | 'url' | PaymentMethodListKeySpecifier)[]
export type PaymentMethodListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsKeySpecifier = (
	| 'acss_debit'
	| 'affirm'
	| 'afterpay_clearpay'
	| 'alipay'
	| 'au_becs_debit'
	| 'bacs_debit'
	| 'bancontact'
	| 'boleto'
	| 'card'
	| 'card_present'
	| 'customer_balance'
	| 'eps'
	| 'fpx'
	| 'giropay'
	| 'grabpay'
	| 'ideal'
	| 'interac_present'
	| 'klarna'
	| 'konbini'
	| 'link'
	| 'oxxo'
	| 'p24'
	| 'paynow'
	| 'sepa_debit'
	| 'sofort'
	| 'us_bank_account'
	| 'wechat_pay'
	| PaymentMethodOptionsKeySpecifier
)[]
export type PaymentMethodOptionsFieldPolicy = {
	acss_debit?: FieldPolicy<any> | FieldReadFunction<any>
	affirm?: FieldPolicy<any> | FieldReadFunction<any>
	afterpay_clearpay?: FieldPolicy<any> | FieldReadFunction<any>
	alipay?: FieldPolicy<any> | FieldReadFunction<any>
	au_becs_debit?: FieldPolicy<any> | FieldReadFunction<any>
	bacs_debit?: FieldPolicy<any> | FieldReadFunction<any>
	bancontact?: FieldPolicy<any> | FieldReadFunction<any>
	boleto?: FieldPolicy<any> | FieldReadFunction<any>
	card?: FieldPolicy<any> | FieldReadFunction<any>
	card_present?: FieldPolicy<any> | FieldReadFunction<any>
	customer_balance?: FieldPolicy<any> | FieldReadFunction<any>
	eps?: FieldPolicy<any> | FieldReadFunction<any>
	fpx?: FieldPolicy<any> | FieldReadFunction<any>
	giropay?: FieldPolicy<any> | FieldReadFunction<any>
	grabpay?: FieldPolicy<any> | FieldReadFunction<any>
	ideal?: FieldPolicy<any> | FieldReadFunction<any>
	interac_present?: FieldPolicy<any> | FieldReadFunction<any>
	klarna?: FieldPolicy<any> | FieldReadFunction<any>
	konbini?: FieldPolicy<any> | FieldReadFunction<any>
	link?: FieldPolicy<any> | FieldReadFunction<any>
	oxxo?: FieldPolicy<any> | FieldReadFunction<any>
	p24?: FieldPolicy<any> | FieldReadFunction<any>
	paynow?: FieldPolicy<any> | FieldReadFunction<any>
	sepa_debit?: FieldPolicy<any> | FieldReadFunction<any>
	sofort?: FieldPolicy<any> | FieldReadFunction<any>
	us_bank_account?: FieldPolicy<any> | FieldReadFunction<any>
	wechat_pay?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsAcssDebitKeySpecifier = (
	| 'mandate_options'
	| 'setup_future_usage'
	| 'verification_method'
	| PaymentMethodOptionsAcssDebitKeySpecifier
)[]
export type PaymentMethodOptionsAcssDebitFieldPolicy = {
	mandate_options?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
	verification_method?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsAcssDebitMandateOptionsKeySpecifier = (
	| 'custom_mandate_url'
	| 'interval_description'
	| 'payment_schedule'
	| 'transaction_type'
	| PaymentMethodOptionsAcssDebitMandateOptionsKeySpecifier
)[]
export type PaymentMethodOptionsAcssDebitMandateOptionsFieldPolicy = {
	custom_mandate_url?: FieldPolicy<any> | FieldReadFunction<any>
	interval_description?: FieldPolicy<any> | FieldReadFunction<any>
	payment_schedule?: FieldPolicy<any> | FieldReadFunction<any>
	transaction_type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsAffirmKeySpecifier = (
	| 'capture_method'
	| 'setup_future_usage'
	| PaymentMethodOptionsAffirmKeySpecifier
)[]
export type PaymentMethodOptionsAffirmFieldPolicy = {
	capture_method?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsAfterpayClearpayKeySpecifier = (
	| 'capture_method'
	| 'reference'
	| 'setup_future_usage'
	| PaymentMethodOptionsAfterpayClearpayKeySpecifier
)[]
export type PaymentMethodOptionsAfterpayClearpayFieldPolicy = {
	capture_method?: FieldPolicy<any> | FieldReadFunction<any>
	reference?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsAlipayKeySpecifier = ('setup_future_usage' | PaymentMethodOptionsAlipayKeySpecifier)[]
export type PaymentMethodOptionsAlipayFieldPolicy = {
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsAuBecsDebitKeySpecifier = (
	| 'setup_future_usage'
	| PaymentMethodOptionsAuBecsDebitKeySpecifier
)[]
export type PaymentMethodOptionsAuBecsDebitFieldPolicy = {
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsBacsDebitKeySpecifier = (
	| 'setup_future_usage'
	| PaymentMethodOptionsBacsDebitKeySpecifier
)[]
export type PaymentMethodOptionsBacsDebitFieldPolicy = {
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsBancontactKeySpecifier = (
	| 'preferred_language'
	| 'setup_future_usage'
	| PaymentMethodOptionsBancontactKeySpecifier
)[]
export type PaymentMethodOptionsBancontactFieldPolicy = {
	preferred_language?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsBoletoKeySpecifier = (
	| 'expires_after_days'
	| 'setup_future_usage'
	| PaymentMethodOptionsBoletoKeySpecifier
)[]
export type PaymentMethodOptionsBoletoFieldPolicy = {
	expires_after_days?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsCardKeySpecifier = (
	| 'capture_method'
	| 'cvc_token'
	| 'installments'
	| 'mandate_options'
	| 'moto'
	| 'network'
	| 'request_three_d_secure'
	| 'setup_future_usage'
	| 'statement_descriptor_suffix_kana'
	| 'statement_descriptor_suffix_kanji'
	| PaymentMethodOptionsCardKeySpecifier
)[]
export type PaymentMethodOptionsCardFieldPolicy = {
	capture_method?: FieldPolicy<any> | FieldReadFunction<any>
	cvc_token?: FieldPolicy<any> | FieldReadFunction<any>
	installments?: FieldPolicy<any> | FieldReadFunction<any>
	mandate_options?: FieldPolicy<any> | FieldReadFunction<any>
	moto?: FieldPolicy<any> | FieldReadFunction<any>
	network?: FieldPolicy<any> | FieldReadFunction<any>
	request_three_d_secure?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor_suffix_kana?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor_suffix_kanji?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsCardInstallmentsKeySpecifier = (
	| 'enabled'
	| 'plan'
	| PaymentMethodOptionsCardInstallmentsKeySpecifier
)[]
export type PaymentMethodOptionsCardInstallmentsFieldPolicy = {
	enabled?: FieldPolicy<any> | FieldReadFunction<any>
	plan?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsCardInstallmentsPlanKeySpecifier = (
	| 'count'
	| 'interval'
	| 'type'
	| PaymentMethodOptionsCardInstallmentsPlanKeySpecifier
)[]
export type PaymentMethodOptionsCardInstallmentsPlanFieldPolicy = {
	count?: FieldPolicy<any> | FieldReadFunction<any>
	interval?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsCardMandateOptionsKeySpecifier = (
	| 'amount'
	| 'amount_type'
	| 'description'
	| 'end_date'
	| 'interval'
	| 'interval_count'
	| 'reference'
	| 'start_date'
	| 'supported_types'
	| PaymentMethodOptionsCardMandateOptionsKeySpecifier
)[]
export type PaymentMethodOptionsCardMandateOptionsFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_type?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	end_date?: FieldPolicy<any> | FieldReadFunction<any>
	interval?: FieldPolicy<any> | FieldReadFunction<any>
	interval_count?: FieldPolicy<any> | FieldReadFunction<any>
	reference?: FieldPolicy<any> | FieldReadFunction<any>
	start_date?: FieldPolicy<any> | FieldReadFunction<any>
	supported_types?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsCardPresentKeySpecifier = (
	| 'request_extended_authorization'
	| 'request_incremental_authorization_support'
	| PaymentMethodOptionsCardPresentKeySpecifier
)[]
export type PaymentMethodOptionsCardPresentFieldPolicy = {
	request_extended_authorization?: FieldPolicy<any> | FieldReadFunction<any>
	request_incremental_authorization_support?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsCustomerBalanceKeySpecifier = (
	| 'bank_transfer'
	| 'funding_type'
	| 'setup_future_usage'
	| PaymentMethodOptionsCustomerBalanceKeySpecifier
)[]
export type PaymentMethodOptionsCustomerBalanceFieldPolicy = {
	bank_transfer?: FieldPolicy<any> | FieldReadFunction<any>
	funding_type?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsCustomerBalanceBankTransferKeySpecifier = (
	| 'eu_bank_transfer'
	| 'requested_address_types'
	| 'type'
	| PaymentMethodOptionsCustomerBalanceBankTransferKeySpecifier
)[]
export type PaymentMethodOptionsCustomerBalanceBankTransferFieldPolicy = {
	eu_bank_transfer?: FieldPolicy<any> | FieldReadFunction<any>
	requested_address_types?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsCustomerBalanceBankTransferEuBankTransferKeySpecifier = (
	| 'country'
	| PaymentMethodOptionsCustomerBalanceBankTransferEuBankTransferKeySpecifier
)[]
export type PaymentMethodOptionsCustomerBalanceBankTransferEuBankTransferFieldPolicy = {
	country?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsEpsKeySpecifier = ('setup_future_usage' | PaymentMethodOptionsEpsKeySpecifier)[]
export type PaymentMethodOptionsEpsFieldPolicy = {
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsFpxKeySpecifier = ('setup_future_usage' | PaymentMethodOptionsFpxKeySpecifier)[]
export type PaymentMethodOptionsFpxFieldPolicy = {
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsGiropayKeySpecifier = ('setup_future_usage' | PaymentMethodOptionsGiropayKeySpecifier)[]
export type PaymentMethodOptionsGiropayFieldPolicy = {
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsGrabpayKeySpecifier = ('setup_future_usage' | PaymentMethodOptionsGrabpayKeySpecifier)[]
export type PaymentMethodOptionsGrabpayFieldPolicy = {
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsIdealKeySpecifier = ('setup_future_usage' | PaymentMethodOptionsIdealKeySpecifier)[]
export type PaymentMethodOptionsIdealFieldPolicy = {
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsKlarnaKeySpecifier = (
	| 'capture_method'
	| 'preferred_locale'
	| 'setup_future_usage'
	| PaymentMethodOptionsKlarnaKeySpecifier
)[]
export type PaymentMethodOptionsKlarnaFieldPolicy = {
	capture_method?: FieldPolicy<any> | FieldReadFunction<any>
	preferred_locale?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsKonbiniKeySpecifier = (
	| 'confirmation_number'
	| 'expires_after_days'
	| 'expires_at'
	| 'product_description'
	| 'setup_future_usage'
	| PaymentMethodOptionsKonbiniKeySpecifier
)[]
export type PaymentMethodOptionsKonbiniFieldPolicy = {
	confirmation_number?: FieldPolicy<any> | FieldReadFunction<any>
	expires_after_days?: FieldPolicy<any> | FieldReadFunction<any>
	expires_at?: FieldPolicy<any> | FieldReadFunction<any>
	product_description?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsLinkKeySpecifier = (
	| 'capture_method'
	| 'persistent_token'
	| 'setup_future_usage'
	| PaymentMethodOptionsLinkKeySpecifier
)[]
export type PaymentMethodOptionsLinkFieldPolicy = {
	capture_method?: FieldPolicy<any> | FieldReadFunction<any>
	persistent_token?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsOxxoKeySpecifier = (
	| 'expires_after_days'
	| 'setup_future_usage'
	| PaymentMethodOptionsOxxoKeySpecifier
)[]
export type PaymentMethodOptionsOxxoFieldPolicy = {
	expires_after_days?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsP24KeySpecifier = (
	| 'setup_future_usage'
	| 'tos_shown_and_accepted'
	| PaymentMethodOptionsP24KeySpecifier
)[]
export type PaymentMethodOptionsP24FieldPolicy = {
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
	tos_shown_and_accepted?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsPaynowKeySpecifier = ('setup_future_usage' | PaymentMethodOptionsPaynowKeySpecifier)[]
export type PaymentMethodOptionsPaynowFieldPolicy = {
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsSepaDebitKeySpecifier = (
	| 'mandate_options'
	| 'setup_future_usage'
	| PaymentMethodOptionsSepaDebitKeySpecifier
)[]
export type PaymentMethodOptionsSepaDebitFieldPolicy = {
	mandate_options?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsSofortKeySpecifier = (
	| 'preferred_language'
	| 'setup_future_usage'
	| PaymentMethodOptionsSofortKeySpecifier
)[]
export type PaymentMethodOptionsSofortFieldPolicy = {
	preferred_language?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsUsBankAccountKeySpecifier = (
	| 'financial_connections'
	| 'networks'
	| 'setup_future_usage'
	| 'verification_method'
	| PaymentMethodOptionsUsBankAccountKeySpecifier
)[]
export type PaymentMethodOptionsUsBankAccountFieldPolicy = {
	financial_connections?: FieldPolicy<any> | FieldReadFunction<any>
	networks?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
	verification_method?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsUsBankAccountFinancialConnectionsKeySpecifier = (
	| 'permissions'
	| 'return_url'
	| PaymentMethodOptionsUsBankAccountFinancialConnectionsKeySpecifier
)[]
export type PaymentMethodOptionsUsBankAccountFinancialConnectionsFieldPolicy = {
	permissions?: FieldPolicy<any> | FieldReadFunction<any>
	return_url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsUsBankAccountNetworksKeySpecifier = (
	| 'requested'
	| PaymentMethodOptionsUsBankAccountNetworksKeySpecifier
)[]
export type PaymentMethodOptionsUsBankAccountNetworksFieldPolicy = {
	requested?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentMethodOptionsWechatPayKeySpecifier = (
	| 'app_id'
	| 'client'
	| 'setup_future_usage'
	| PaymentMethodOptionsWechatPayKeySpecifier
)[]
export type PaymentMethodOptionsWechatPayFieldPolicy = {
	app_id?: FieldPolicy<any> | FieldReadFunction<any>
	client?: FieldPolicy<any> | FieldReadFunction<any>
	setup_future_usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentSettingsKeySpecifier = (
	| 'payment_method_options'
	| 'payment_method_types'
	| 'save_default_payment_method'
	| PaymentSettingsKeySpecifier
)[]
export type PaymentSettingsFieldPolicy = {
	payment_method_options?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_types?: FieldPolicy<any> | FieldReadFunction<any>
	save_default_payment_method?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentSettingsPaymentMethodOptionsKeySpecifier = (
	| 'acss_debit'
	| PaymentSettingsPaymentMethodOptionsKeySpecifier
)[]
export type PaymentSettingsPaymentMethodOptionsFieldPolicy = {
	acss_debit?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentSettingsPaymentMethodOptionsAcssDebitKeySpecifier = (
	| 'mandate_options'
	| 'verification_method'
	| PaymentSettingsPaymentMethodOptionsAcssDebitKeySpecifier
)[]
export type PaymentSettingsPaymentMethodOptionsAcssDebitFieldPolicy = {
	mandate_options?: FieldPolicy<any> | FieldReadFunction<any>
	verification_method?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PaymentSettingsPaymentMethodOptionsAcssDebitMandateOptionsKeySpecifier = (
	| 'transaction_type'
	| PaymentSettingsPaymentMethodOptionsAcssDebitMandateOptionsKeySpecifier
)[]
export type PaymentSettingsPaymentMethodOptionsAcssDebitMandateOptionsFieldPolicy = {
	transaction_type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PayoutKeySpecifier = (
	| 'amount'
	| 'application_fee'
	| 'application_fee_amount'
	| 'arrival_date'
	| 'automatic'
	| 'balance_transaction'
	| 'created'
	| 'currency'
	| 'description'
	| 'destination'
	| 'failure_balance_transaction'
	| 'failure_code'
	| 'failure_message'
	| 'id'
	| 'livemode'
	| 'metadata'
	| 'method'
	| 'object'
	| 'original_payout'
	| 'reconciliation_status'
	| 'reversed_by'
	| 'source_type'
	| 'statement_descriptor'
	| 'status'
	| 'trace_id'
	| 'type'
	| PayoutKeySpecifier
)[]
export type PayoutFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	application_fee?: FieldPolicy<any> | FieldReadFunction<any>
	application_fee_amount?: FieldPolicy<any> | FieldReadFunction<any>
	arrival_date?: FieldPolicy<any> | FieldReadFunction<any>
	automatic?: FieldPolicy<any> | FieldReadFunction<any>
	balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	destination?: FieldPolicy<any> | FieldReadFunction<any>
	failure_balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	failure_code?: FieldPolicy<any> | FieldReadFunction<any>
	failure_message?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	method?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	original_payout?: FieldPolicy<any> | FieldReadFunction<any>
	reconciliation_status?: FieldPolicy<any> | FieldReadFunction<any>
	reversed_by?: FieldPolicy<any> | FieldReadFunction<any>
	source_type?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	trace_id?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PendingInvoiceItemIntervalKeySpecifier = (
	| 'interval'
	| 'interval_count'
	| PendingInvoiceItemIntervalKeySpecifier
)[]
export type PendingInvoiceItemIntervalFieldPolicy = {
	interval?: FieldPolicy<any> | FieldReadFunction<any>
	interval_count?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PendingUpdateKeySpecifier = (
	| 'billing_cycle_anchor'
	| 'expires_at'
	| 'subscription_items'
	| 'trial_end'
	| 'trial_from_plan'
	| PendingUpdateKeySpecifier
)[]
export type PendingUpdateFieldPolicy = {
	billing_cycle_anchor?: FieldPolicy<any> | FieldReadFunction<any>
	expires_at?: FieldPolicy<any> | FieldReadFunction<any>
	subscription_items?: FieldPolicy<any> | FieldReadFunction<any>
	trial_end?: FieldPolicy<any> | FieldReadFunction<any>
	trial_from_plan?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PercentilesKeySpecifier = ('p25' | 'p50' | 'p75' | PercentilesKeySpecifier)[]
export type PercentilesFieldPolicy = {
	p25?: FieldPolicy<any> | FieldReadFunction<any>
	p50?: FieldPolicy<any> | FieldReadFunction<any>
	p75?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PeriodKeySpecifier = ('end' | 'start' | PeriodKeySpecifier)[]
export type PeriodFieldPolicy = {
	end?: FieldPolicy<any> | FieldReadFunction<any>
	start?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PerkKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'date'
	| 'deletedAt'
	| 'description'
	| 'isFull'
	| 'isParticipant'
	| 'location'
	| 'name'
	| 'organization'
	| 'organizationId'
	| 'picture'
	| 'runFrom'
	| 'runTo'
	| 'type'
	| 'updatedAt'
	| 'url'
	| PerkKeySpecifier
)[]
export type PerkFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	date?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	isFull?: FieldPolicy<any> | FieldReadFunction<any>
	isParticipant?: FieldPolicy<any> | FieldReadFunction<any>
	location?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	organization?: FieldPolicy<any> | FieldReadFunction<any>
	organizationId?: FieldPolicy<any> | FieldReadFunction<any>
	picture?: FieldPolicy<any> | FieldReadFunction<any>
	runFrom?: FieldPolicy<any> | FieldReadFunction<any>
	runTo?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PersonKeySpecifier = (
	| 'account'
	| 'address'
	| 'address_kana'
	| 'address_kanji'
	| 'created'
	| 'deleted'
	| 'dob'
	| 'email'
	| 'first_name'
	| 'first_name_kana'
	| 'first_name_kanji'
	| 'full_name_aliases'
	| 'future_requirements'
	| 'gender'
	| 'id'
	| 'id_number_provided'
	| 'id_number_secondary_provided'
	| 'last_name'
	| 'last_name_kana'
	| 'last_name_kanji'
	| 'maiden_name'
	| 'metadata'
	| 'nationality'
	| 'object'
	| 'phone'
	| 'political_exposure'
	| 'registered_address'
	| 'relationship'
	| 'requirements'
	| 'ssn_last_4_provided'
	| 'verification'
	| PersonKeySpecifier
)[]
export type PersonFieldPolicy = {
	account?: FieldPolicy<any> | FieldReadFunction<any>
	address?: FieldPolicy<any> | FieldReadFunction<any>
	address_kana?: FieldPolicy<any> | FieldReadFunction<any>
	address_kanji?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	dob?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	first_name?: FieldPolicy<any> | FieldReadFunction<any>
	first_name_kana?: FieldPolicy<any> | FieldReadFunction<any>
	first_name_kanji?: FieldPolicy<any> | FieldReadFunction<any>
	full_name_aliases?: FieldPolicy<any> | FieldReadFunction<any>
	future_requirements?: FieldPolicy<any> | FieldReadFunction<any>
	gender?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	id_number_provided?: FieldPolicy<any> | FieldReadFunction<any>
	id_number_secondary_provided?: FieldPolicy<any> | FieldReadFunction<any>
	last_name?: FieldPolicy<any> | FieldReadFunction<any>
	last_name_kana?: FieldPolicy<any> | FieldReadFunction<any>
	last_name_kanji?: FieldPolicy<any> | FieldReadFunction<any>
	maiden_name?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	nationality?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	phone?: FieldPolicy<any> | FieldReadFunction<any>
	political_exposure?: FieldPolicy<any> | FieldReadFunction<any>
	registered_address?: FieldPolicy<any> | FieldReadFunction<any>
	relationship?: FieldPolicy<any> | FieldReadFunction<any>
	requirements?: FieldPolicy<any> | FieldReadFunction<any>
	ssn_last_4_provided?: FieldPolicy<any> | FieldReadFunction<any>
	verification?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PersonVerificationKeySpecifier = (
	| 'additional_document'
	| 'details'
	| 'details_code'
	| 'document'
	| 'status'
	| PersonVerificationKeySpecifier
)[]
export type PersonVerificationFieldPolicy = {
	additional_document?: FieldPolicy<any> | FieldReadFunction<any>
	details?: FieldPolicy<any> | FieldReadFunction<any>
	details_code?: FieldPolicy<any> | FieldReadFunction<any>
	document?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PhaseKeySpecifier = (
	| 'add_invoice_items'
	| 'application_fee_percent'
	| 'automatic_tax'
	| 'billing_cycle_anchor'
	| 'billing_thresholds'
	| 'collection_method'
	| 'coupon'
	| 'currency'
	| 'default_payment_method'
	| 'default_tax_rates'
	| 'description'
	| 'discounts'
	| 'end_date'
	| 'invoice_settings'
	| 'items'
	| 'metadata'
	| 'on_behalf_of'
	| 'proration_behavior'
	| 'start_date'
	| 'transfer_data'
	| 'trial_end'
	| PhaseKeySpecifier
)[]
export type PhaseFieldPolicy = {
	add_invoice_items?: FieldPolicy<any> | FieldReadFunction<any>
	application_fee_percent?: FieldPolicy<any> | FieldReadFunction<any>
	automatic_tax?: FieldPolicy<any> | FieldReadFunction<any>
	billing_cycle_anchor?: FieldPolicy<any> | FieldReadFunction<any>
	billing_thresholds?: FieldPolicy<any> | FieldReadFunction<any>
	collection_method?: FieldPolicy<any> | FieldReadFunction<any>
	coupon?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	default_payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	default_tax_rates?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	end_date?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_settings?: FieldPolicy<any> | FieldReadFunction<any>
	items?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	on_behalf_of?: FieldPolicy<any> | FieldReadFunction<any>
	proration_behavior?: FieldPolicy<any> | FieldReadFunction<any>
	start_date?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_data?: FieldPolicy<any> | FieldReadFunction<any>
	trial_end?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PhaseAddInvoiceItemKeySpecifier = (
	| 'discounts'
	| 'price'
	| 'quantity'
	| 'tax_rates'
	| PhaseAddInvoiceItemKeySpecifier
)[]
export type PhaseAddInvoiceItemFieldPolicy = {
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	price?: FieldPolicy<any> | FieldReadFunction<any>
	quantity?: FieldPolicy<any> | FieldReadFunction<any>
	tax_rates?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PhaseAutomaticTaxKeySpecifier = (
	| 'disabled_reason'
	| 'enabled'
	| 'liability'
	| PhaseAutomaticTaxKeySpecifier
)[]
export type PhaseAutomaticTaxFieldPolicy = {
	disabled_reason?: FieldPolicy<any> | FieldReadFunction<any>
	enabled?: FieldPolicy<any> | FieldReadFunction<any>
	liability?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PhaseBillingThresholdsKeySpecifier = (
	| 'amount_gte'
	| 'reset_billing_cycle_anchor'
	| PhaseBillingThresholdsKeySpecifier
)[]
export type PhaseBillingThresholdsFieldPolicy = {
	amount_gte?: FieldPolicy<any> | FieldReadFunction<any>
	reset_billing_cycle_anchor?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PhaseDiscountKeySpecifier = ('coupon' | 'discount' | 'promotion_code' | PhaseDiscountKeySpecifier)[]
export type PhaseDiscountFieldPolicy = {
	coupon?: FieldPolicy<any> | FieldReadFunction<any>
	discount?: FieldPolicy<any> | FieldReadFunction<any>
	promotion_code?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PhaseInvoiceSettingsKeySpecifier = (
	| 'account_tax_ids'
	| 'days_until_due'
	| 'issuer'
	| PhaseInvoiceSettingsKeySpecifier
)[]
export type PhaseInvoiceSettingsFieldPolicy = {
	account_tax_ids?: FieldPolicy<any> | FieldReadFunction<any>
	days_until_due?: FieldPolicy<any> | FieldReadFunction<any>
	issuer?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PhaseItemKeySpecifier = (
	| 'billing_thresholds'
	| 'discounts'
	| 'metadata'
	| 'plan'
	| 'price'
	| 'quantity'
	| 'tax_rates'
	| PhaseItemKeySpecifier
)[]
export type PhaseItemFieldPolicy = {
	billing_thresholds?: FieldPolicy<any> | FieldReadFunction<any>
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	plan?: FieldPolicy<any> | FieldReadFunction<any>
	price?: FieldPolicy<any> | FieldReadFunction<any>
	quantity?: FieldPolicy<any> | FieldReadFunction<any>
	tax_rates?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PhaseItemBillingThresholdsKeySpecifier = ('usage_gte' | PhaseItemBillingThresholdsKeySpecifier)[]
export type PhaseItemBillingThresholdsFieldPolicy = {
	usage_gte?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PhaseTransferDataKeySpecifier = ('amount_percent' | 'destination' | PhaseTransferDataKeySpecifier)[]
export type PhaseTransferDataFieldPolicy = {
	amount_percent?: FieldPolicy<any> | FieldReadFunction<any>
	destination?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PlanKeySpecifier = (
	| 'active'
	| 'aggregate_usage'
	| 'amount'
	| 'amount_decimal'
	| 'billing_scheme'
	| 'created'
	| 'currency'
	| 'deleted'
	| 'id'
	| 'interval'
	| 'interval_count'
	| 'livemode'
	| 'metadata'
	| 'meter'
	| 'nickname'
	| 'object'
	| 'product'
	| 'tiers'
	| 'tiers_mode'
	| 'transform_usage'
	| 'trial_period_days'
	| 'usage_type'
	| PlanKeySpecifier
)[]
export type PlanFieldPolicy = {
	active?: FieldPolicy<any> | FieldReadFunction<any>
	aggregate_usage?: FieldPolicy<any> | FieldReadFunction<any>
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_decimal?: FieldPolicy<any> | FieldReadFunction<any>
	billing_scheme?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	interval?: FieldPolicy<any> | FieldReadFunction<any>
	interval_count?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	meter?: FieldPolicy<any> | FieldReadFunction<any>
	nickname?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	product?: FieldPolicy<any> | FieldReadFunction<any>
	tiers?: FieldPolicy<any> | FieldReadFunction<any>
	tiers_mode?: FieldPolicy<any> | FieldReadFunction<any>
	transform_usage?: FieldPolicy<any> | FieldReadFunction<any>
	trial_period_days?: FieldPolicy<any> | FieldReadFunction<any>
	usage_type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PretaxCreditAmountKeySpecifier = (
	| 'amount'
	| 'credit_balance_transaction'
	| 'discount'
	| 'type'
	| PretaxCreditAmountKeySpecifier
)[]
export type PretaxCreditAmountFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	credit_balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	discount?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PriceKeySpecifier = (
	| 'active'
	| 'billing_scheme'
	| 'created'
	| 'currency'
	| 'custom_unit_amount'
	| 'deleted'
	| 'id'
	| 'livemode'
	| 'lookup_key'
	| 'metadata'
	| 'nickname'
	| 'object'
	| 'product'
	| 'recurring'
	| 'tax_behavior'
	| 'tiers'
	| 'tiers_mode'
	| 'transform_quantity'
	| 'type'
	| 'unit_amount'
	| 'unit_amount_decimal'
	| PriceKeySpecifier
)[]
export type PriceFieldPolicy = {
	active?: FieldPolicy<any> | FieldReadFunction<any>
	billing_scheme?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	custom_unit_amount?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	lookup_key?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	nickname?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	product?: FieldPolicy<any> | FieldReadFunction<any>
	recurring?: FieldPolicy<any> | FieldReadFunction<any>
	tax_behavior?: FieldPolicy<any> | FieldReadFunction<any>
	tiers?: FieldPolicy<any> | FieldReadFunction<any>
	tiers_mode?: FieldPolicy<any> | FieldReadFunction<any>
	transform_quantity?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	unit_amount?: FieldPolicy<any> | FieldReadFunction<any>
	unit_amount_decimal?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PriceRecurringKeySpecifier = (
	| 'aggregate_usage'
	| 'interval'
	| 'interval_count'
	| 'meter'
	| 'trial_period_days'
	| 'usage_type'
	| PriceRecurringKeySpecifier
)[]
export type PriceRecurringFieldPolicy = {
	aggregate_usage?: FieldPolicy<any> | FieldReadFunction<any>
	interval?: FieldPolicy<any> | FieldReadFunction<any>
	interval_count?: FieldPolicy<any> | FieldReadFunction<any>
	meter?: FieldPolicy<any> | FieldReadFunction<any>
	trial_period_days?: FieldPolicy<any> | FieldReadFunction<any>
	usage_type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PriorUndisputedTransactionKeySpecifier = (
	| 'charge'
	| 'customer_account_id'
	| 'customer_device_fingerprint'
	| 'customer_device_id'
	| 'customer_email_address'
	| 'customer_purchase_ip'
	| 'product_description'
	| 'shipping_address'
	| PriorUndisputedTransactionKeySpecifier
)[]
export type PriorUndisputedTransactionFieldPolicy = {
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	customer_account_id?: FieldPolicy<any> | FieldReadFunction<any>
	customer_device_fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	customer_device_id?: FieldPolicy<any> | FieldReadFunction<any>
	customer_email_address?: FieldPolicy<any> | FieldReadFunction<any>
	customer_purchase_ip?: FieldPolicy<any> | FieldReadFunction<any>
	product_description?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_address?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ProcessingKeySpecifier = ('card' | 'type' | ProcessingKeySpecifier)[]
export type ProcessingFieldPolicy = {
	card?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ProcessingCardKeySpecifier = ('customer_notification' | ProcessingCardKeySpecifier)[]
export type ProcessingCardFieldPolicy = {
	customer_notification?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ProcessingCardCustomerNotificationKeySpecifier = (
	| 'approval_requested'
	| 'completes_at'
	| ProcessingCardCustomerNotificationKeySpecifier
)[]
export type ProcessingCardCustomerNotificationFieldPolicy = {
	approval_requested?: FieldPolicy<any> | FieldReadFunction<any>
	completes_at?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ProductKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'description'
	| 'name'
	| 'organization'
	| 'organizationId'
	| 'picture'
	| 'updatedAt'
	| 'url'
	| ProductKeySpecifier
)[]
export type ProductFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	organization?: FieldPolicy<any> | FieldReadFunction<any>
	organizationId?: FieldPolicy<any> | FieldReadFunction<any>
	picture?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ProductDetailsKeySpecifier = (
	| 'batch'
	| 'color'
	| 'picture'
	| 'serialNumber'
	| 'size'
	| 'sku'
	| 'stamp'
	| ProductDetailsKeySpecifier
)[]
export type ProductDetailsFieldPolicy = {
	batch?: FieldPolicy<any> | FieldReadFunction<any>
	color?: FieldPolicy<any> | FieldReadFunction<any>
	picture?: FieldPolicy<any> | FieldReadFunction<any>
	serialNumber?: FieldPolicy<any> | FieldReadFunction<any>
	size?: FieldPolicy<any> | FieldReadFunction<any>
	sku?: FieldPolicy<any> | FieldReadFunction<any>
	stamp?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ProgramKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'customRedirect'
	| 'deletedAt'
	| 'name'
	| 'organization'
	| 'organizationId'
	| 'updatedAt'
	| ProgramKeySpecifier
)[]
export type ProgramFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	customRedirect?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	organization?: FieldPolicy<any> | FieldReadFunction<any>
	organizationId?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PromotionCodeKeySpecifier = (
	| 'active'
	| 'code'
	| 'coupon'
	| 'created'
	| 'customer'
	| 'expires_at'
	| 'id'
	| 'livemode'
	| 'max_redemptions'
	| 'metadata'
	| 'object'
	| 'restrictions'
	| 'times_redeemed'
	| PromotionCodeKeySpecifier
)[]
export type PromotionCodeFieldPolicy = {
	active?: FieldPolicy<any> | FieldReadFunction<any>
	code?: FieldPolicy<any> | FieldReadFunction<any>
	coupon?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	expires_at?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	max_redemptions?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	restrictions?: FieldPolicy<any> | FieldReadFunction<any>
	times_redeemed?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PromotionCodeRestrictionsKeySpecifier = (
	| 'first_time_transaction'
	| 'minimum_amount'
	| 'minimum_amount_currency'
	| PromotionCodeRestrictionsKeySpecifier
)[]
export type PromotionCodeRestrictionsFieldPolicy = {
	first_time_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	minimum_amount?: FieldPolicy<any> | FieldReadFunction<any>
	minimum_amount_currency?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ProrationDetailsKeySpecifier = ('credited_items' | ProrationDetailsKeySpecifier)[]
export type ProrationDetailsFieldPolicy = {
	credited_items?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ProrationDetailsCreditedItemsKeySpecifier = (
	| 'invoice'
	| 'invoice_line_items'
	| ProrationDetailsCreditedItemsKeySpecifier
)[]
export type ProrationDetailsCreditedItemsFieldPolicy = {
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_line_items?: FieldPolicy<any> | FieldReadFunction<any>
}
export type QueryKeySpecifier = (
	| 'activation'
	| 'activationStats'
	| 'activations'
	| 'admins'
	| 'auditEvent'
	| 'auditEvents'
	| 'authenticate'
	| 'comment'
	| 'comments'
	| 'customer'
	| 'customerList'
	| 'customerSearch'
	| 'federatedUser'
	| 'federatedUsers'
	| 'installation'
	| 'installations'
	| 'invitation'
	| 'invitationStats'
	| 'invitations'
	| 'invoice'
	| 'invoiceList'
	| 'invoiceRetrieveUpcoming'
	| 'invoiceSearch'
	| 'listBalanceTransactions'
	| 'listLineItems'
	| 'listPaymentMethods'
	| 'listSources'
	| 'listTaxIds'
	| 'listUpcomingLines'
	| 'me'
	| 'notification'
	| 'notifications'
	| 'oidcClient'
	| 'oidcClients'
	| 'organization'
	| 'organizationStats'
	| 'organizations'
	| 'paymentIntent'
	| 'paymentIntentList'
	| 'paymentIntentSearch'
	| 'paymentMethod'
	| 'paymentMethodList'
	| 'peopleStats'
	| 'perk'
	| 'perks'
	| 'person'
	| 'product'
	| 'productStats'
	| 'products'
	| 'program'
	| 'programStats'
	| 'programs'
	| 'rate'
	| 'rates'
	| 'rating'
	| 'ratingOverTime'
	| 'reaction'
	| 'reactions'
	| 'retrieveBalanceTransaction'
	| 'retrieveCashBalance'
	| 'retrievePaymentMethod'
	| 'retrieveSource'
	| 'retrieveTaxId'
	| 'role'
	| 'roles'
	| 'stripeSubscription'
	| 'stripeSubscriptionList'
	| 'stripeSubscriptionSearch'
	| 'tag'
	| 'tagStats'
	| 'tags'
	| 'tap'
	| 'user'
	| 'userStats'
	| 'users'
	| 'webhook'
	| 'webhooks'
	| QueryKeySpecifier
)[]
export type QueryFieldPolicy = {
	activation?: FieldPolicy<any> | FieldReadFunction<any>
	activationStats?: FieldPolicy<any> | FieldReadFunction<any>
	activations?: FieldPolicy<any> | FieldReadFunction<any>
	admins?: FieldPolicy<any> | FieldReadFunction<any>
	auditEvent?: FieldPolicy<any> | FieldReadFunction<any>
	auditEvents?: FieldPolicy<any> | FieldReadFunction<any>
	authenticate?: FieldPolicy<any> | FieldReadFunction<any>
	comment?: FieldPolicy<any> | FieldReadFunction<any>
	comments?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	customerList?: FieldPolicy<any> | FieldReadFunction<any>
	customerSearch?: FieldPolicy<any> | FieldReadFunction<any>
	federatedUser?: FieldPolicy<any> | FieldReadFunction<any>
	federatedUsers?: FieldPolicy<any> | FieldReadFunction<any>
	installation?: FieldPolicy<any> | FieldReadFunction<any>
	installations?: FieldPolicy<any> | FieldReadFunction<any>
	invitation?: FieldPolicy<any> | FieldReadFunction<any>
	invitationStats?: FieldPolicy<any> | FieldReadFunction<any>
	invitations?: FieldPolicy<any> | FieldReadFunction<any>
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	invoiceList?: FieldPolicy<any> | FieldReadFunction<any>
	invoiceRetrieveUpcoming?: FieldPolicy<any> | FieldReadFunction<any>
	invoiceSearch?: FieldPolicy<any> | FieldReadFunction<any>
	listBalanceTransactions?: FieldPolicy<any> | FieldReadFunction<any>
	listLineItems?: FieldPolicy<any> | FieldReadFunction<any>
	listPaymentMethods?: FieldPolicy<any> | FieldReadFunction<any>
	listSources?: FieldPolicy<any> | FieldReadFunction<any>
	listTaxIds?: FieldPolicy<any> | FieldReadFunction<any>
	listUpcomingLines?: FieldPolicy<any> | FieldReadFunction<any>
	me?: FieldPolicy<any> | FieldReadFunction<any>
	notification?: FieldPolicy<any> | FieldReadFunction<any>
	notifications?: FieldPolicy<any> | FieldReadFunction<any>
	oidcClient?: FieldPolicy<any> | FieldReadFunction<any>
	oidcClients?: FieldPolicy<any> | FieldReadFunction<any>
	organization?: FieldPolicy<any> | FieldReadFunction<any>
	organizationStats?: FieldPolicy<any> | FieldReadFunction<any>
	organizations?: FieldPolicy<any> | FieldReadFunction<any>
	paymentIntent?: FieldPolicy<any> | FieldReadFunction<any>
	paymentIntentList?: FieldPolicy<any> | FieldReadFunction<any>
	paymentIntentSearch?: FieldPolicy<any> | FieldReadFunction<any>
	paymentMethod?: FieldPolicy<any> | FieldReadFunction<any>
	paymentMethodList?: FieldPolicy<any> | FieldReadFunction<any>
	peopleStats?: FieldPolicy<any> | FieldReadFunction<any>
	perk?: FieldPolicy<any> | FieldReadFunction<any>
	perks?: FieldPolicy<any> | FieldReadFunction<any>
	person?: FieldPolicy<any> | FieldReadFunction<any>
	product?: FieldPolicy<any> | FieldReadFunction<any>
	productStats?: FieldPolicy<any> | FieldReadFunction<any>
	products?: FieldPolicy<any> | FieldReadFunction<any>
	program?: FieldPolicy<any> | FieldReadFunction<any>
	programStats?: FieldPolicy<any> | FieldReadFunction<any>
	programs?: FieldPolicy<any> | FieldReadFunction<any>
	rate?: FieldPolicy<any> | FieldReadFunction<any>
	rates?: FieldPolicy<any> | FieldReadFunction<any>
	rating?: FieldPolicy<any> | FieldReadFunction<any>
	ratingOverTime?: FieldPolicy<any> | FieldReadFunction<any>
	reaction?: FieldPolicy<any> | FieldReadFunction<any>
	reactions?: FieldPolicy<any> | FieldReadFunction<any>
	retrieveBalanceTransaction?: FieldPolicy<any> | FieldReadFunction<any>
	retrieveCashBalance?: FieldPolicy<any> | FieldReadFunction<any>
	retrievePaymentMethod?: FieldPolicy<any> | FieldReadFunction<any>
	retrieveSource?: FieldPolicy<any> | FieldReadFunction<any>
	retrieveTaxId?: FieldPolicy<any> | FieldReadFunction<any>
	role?: FieldPolicy<any> | FieldReadFunction<any>
	roles?: FieldPolicy<any> | FieldReadFunction<any>
	stripeSubscription?: FieldPolicy<any> | FieldReadFunction<any>
	stripeSubscriptionList?: FieldPolicy<any> | FieldReadFunction<any>
	stripeSubscriptionSearch?: FieldPolicy<any> | FieldReadFunction<any>
	tag?: FieldPolicy<any> | FieldReadFunction<any>
	tagStats?: FieldPolicy<any> | FieldReadFunction<any>
	tags?: FieldPolicy<any> | FieldReadFunction<any>
	tap?: FieldPolicy<any> | FieldReadFunction<any>
	user?: FieldPolicy<any> | FieldReadFunction<any>
	userStats?: FieldPolicy<any> | FieldReadFunction<any>
	users?: FieldPolicy<any> | FieldReadFunction<any>
	webhook?: FieldPolicy<any> | FieldReadFunction<any>
	webhooks?: FieldPolicy<any> | FieldReadFunction<any>
}
export type QuoteKeySpecifier = (
	| 'amount_subtotal'
	| 'amount_total'
	| 'application'
	| 'application_fee_amount'
	| 'application_fee_percent'
	| 'automatic_tax'
	| 'collection_method'
	| 'computed'
	| 'created'
	| 'currency'
	| 'customer'
	| 'default_tax_rates'
	| 'description'
	| 'discounts'
	| 'expires_at'
	| 'footer'
	| 'from_quote'
	| 'header'
	| 'id'
	| 'invoice'
	| 'invoice_settings'
	| 'line_items'
	| 'livemode'
	| 'metadata'
	| 'number'
	| 'object'
	| 'on_behalf_of'
	| 'status'
	| 'status_transitions'
	| 'subscription'
	| 'subscription_data'
	| 'subscription_schedule'
	| 'test_clock'
	| 'total_details'
	| 'transfer_data'
	| QuoteKeySpecifier
)[]
export type QuoteFieldPolicy = {
	amount_subtotal?: FieldPolicy<any> | FieldReadFunction<any>
	amount_total?: FieldPolicy<any> | FieldReadFunction<any>
	application?: FieldPolicy<any> | FieldReadFunction<any>
	application_fee_amount?: FieldPolicy<any> | FieldReadFunction<any>
	application_fee_percent?: FieldPolicy<any> | FieldReadFunction<any>
	automatic_tax?: FieldPolicy<any> | FieldReadFunction<any>
	collection_method?: FieldPolicy<any> | FieldReadFunction<any>
	computed?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	default_tax_rates?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	expires_at?: FieldPolicy<any> | FieldReadFunction<any>
	footer?: FieldPolicy<any> | FieldReadFunction<any>
	from_quote?: FieldPolicy<any> | FieldReadFunction<any>
	header?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_settings?: FieldPolicy<any> | FieldReadFunction<any>
	line_items?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	number?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	on_behalf_of?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	status_transitions?: FieldPolicy<any> | FieldReadFunction<any>
	subscription?: FieldPolicy<any> | FieldReadFunction<any>
	subscription_data?: FieldPolicy<any> | FieldReadFunction<any>
	subscription_schedule?: FieldPolicy<any> | FieldReadFunction<any>
	test_clock?: FieldPolicy<any> | FieldReadFunction<any>
	total_details?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_data?: FieldPolicy<any> | FieldReadFunction<any>
}
export type QuoteInvoiceSettingsKeySpecifier = ('days_until_due' | 'issuer' | QuoteInvoiceSettingsKeySpecifier)[]
export type QuoteInvoiceSettingsFieldPolicy = {
	days_until_due?: FieldPolicy<any> | FieldReadFunction<any>
	issuer?: FieldPolicy<any> | FieldReadFunction<any>
}
export type QuoteStatusTransitionsKeySpecifier = (
	| 'accepted_at'
	| 'canceled_at'
	| 'finalized_at'
	| QuoteStatusTransitionsKeySpecifier
)[]
export type QuoteStatusTransitionsFieldPolicy = {
	accepted_at?: FieldPolicy<any> | FieldReadFunction<any>
	canceled_at?: FieldPolicy<any> | FieldReadFunction<any>
	finalized_at?: FieldPolicy<any> | FieldReadFunction<any>
}
export type QuoteTransferDataKeySpecifier = (
	| 'amount'
	| 'amount_percent'
	| 'destination'
	| QuoteTransferDataKeySpecifier
)[]
export type QuoteTransferDataFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_percent?: FieldPolicy<any> | FieldReadFunction<any>
	destination?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RadarOptionsKeySpecifier = ('session' | RadarOptionsKeySpecifier)[]
export type RadarOptionsFieldPolicy = {
	session?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RateKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'parent'
	| 'parentId'
	| 'rate'
	| 'updatedAt'
	| 'userId'
	| RateKeySpecifier
)[]
export type RateFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
	parentId?: FieldPolicy<any> | FieldReadFunction<any>
	rate?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ReactionKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'parent'
	| 'parentId'
	| 'type'
	| 'updatedAt'
	| 'userId'
	| ReactionKeySpecifier
)[]
export type ReactionFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
	parentId?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ReactionCountKeySpecifier = ('count' | 'type' | ReactionCountKeySpecifier)[]
export type ReactionCountFieldPolicy = {
	count?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ReactionsKeySpecifier = ('mine' | 'resume' | ReactionsKeySpecifier)[]
export type ReactionsFieldPolicy = {
	mine?: FieldPolicy<any> | FieldReadFunction<any>
	resume?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ReceiverKeySpecifier = (
	| 'address'
	| 'amount_charged'
	| 'amount_received'
	| 'amount_returned'
	| 'refund_attributes_method'
	| 'refund_attributes_status'
	| ReceiverKeySpecifier
)[]
export type ReceiverFieldPolicy = {
	address?: FieldPolicy<any> | FieldReadFunction<any>
	amount_charged?: FieldPolicy<any> | FieldReadFunction<any>
	amount_received?: FieldPolicy<any> | FieldReadFunction<any>
	amount_returned?: FieldPolicy<any> | FieldReadFunction<any>
	refund_attributes_method?: FieldPolicy<any> | FieldReadFunction<any>
	refund_attributes_status?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RedirectKeySpecifier = ('failure_reason' | 'return_url' | 'status' | 'url' | RedirectKeySpecifier)[]
export type RedirectFieldPolicy = {
	failure_reason?: FieldPolicy<any> | FieldReadFunction<any>
	return_url?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RefundKeySpecifier = (
	| 'amount'
	| 'balance_transaction'
	| 'charge'
	| 'created'
	| 'currency'
	| 'description'
	| 'failure_balance_transaction'
	| 'failure_reason'
	| 'id'
	| 'instructions_email'
	| 'metadata'
	| 'next_action'
	| 'object'
	| 'payment_intent'
	| 'reason'
	| 'receipt_number'
	| 'source_transfer_reversal'
	| 'status'
	| 'transfer_reversal'
	| RefundKeySpecifier
)[]
export type RefundFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	failure_balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	failure_reason?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	instructions_email?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	next_action?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	payment_intent?: FieldPolicy<any> | FieldReadFunction<any>
	reason?: FieldPolicy<any> | FieldReadFunction<any>
	receipt_number?: FieldPolicy<any> | FieldReadFunction<any>
	source_transfer_reversal?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_reversal?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RefundListKeySpecifier = ('data' | 'has_more' | 'object' | 'url' | RefundListKeySpecifier)[]
export type RefundListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RefundNextActionKeySpecifier = ('display_details' | 'type' | RefundNextActionKeySpecifier)[]
export type RefundNextActionFieldPolicy = {
	display_details?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RelationshipKeySpecifier = (
	| 'authorizer'
	| 'director'
	| 'executive'
	| 'legal_guardian'
	| 'owner'
	| 'percent_ownership'
	| 'representative'
	| 'title'
	| RelationshipKeySpecifier
)[]
export type RelationshipFieldPolicy = {
	authorizer?: FieldPolicy<any> | FieldReadFunction<any>
	director?: FieldPolicy<any> | FieldReadFunction<any>
	executive?: FieldPolicy<any> | FieldReadFunction<any>
	legal_guardian?: FieldPolicy<any> | FieldReadFunction<any>
	owner?: FieldPolicy<any> | FieldReadFunction<any>
	percent_ownership?: FieldPolicy<any> | FieldReadFunction<any>
	representative?: FieldPolicy<any> | FieldReadFunction<any>
	title?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RenderingKeySpecifier = (
	| 'amount_tax_display'
	| 'pdf'
	| 'template'
	| 'template_version'
	| RenderingKeySpecifier
)[]
export type RenderingFieldPolicy = {
	amount_tax_display?: FieldPolicy<any> | FieldReadFunction<any>
	pdf?: FieldPolicy<any> | FieldReadFunction<any>
	template?: FieldPolicy<any> | FieldReadFunction<any>
	template_version?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RenderingPdfKeySpecifier = ('page_size' | RenderingPdfKeySpecifier)[]
export type RenderingPdfFieldPolicy = {
	page_size?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RequirementsKeySpecifier = (
	| 'alternatives'
	| 'current_deadline'
	| 'currently_due'
	| 'disabled_reason'
	| 'errors'
	| 'eventually_due'
	| 'past_due'
	| 'pending_verification'
	| RequirementsKeySpecifier
)[]
export type RequirementsFieldPolicy = {
	alternatives?: FieldPolicy<any> | FieldReadFunction<any>
	current_deadline?: FieldPolicy<any> | FieldReadFunction<any>
	currently_due?: FieldPolicy<any> | FieldReadFunction<any>
	disabled_reason?: FieldPolicy<any> | FieldReadFunction<any>
	errors?: FieldPolicy<any> | FieldReadFunction<any>
	eventually_due?: FieldPolicy<any> | FieldReadFunction<any>
	past_due?: FieldPolicy<any> | FieldReadFunction<any>
	pending_verification?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RequirementsAlternativeKeySpecifier = (
	| 'alternative_fields_due'
	| 'original_fields_due'
	| RequirementsAlternativeKeySpecifier
)[]
export type RequirementsAlternativeFieldPolicy = {
	alternative_fields_due?: FieldPolicy<any> | FieldReadFunction<any>
	original_fields_due?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RequirementsErrorKeySpecifier = ('code' | 'reason' | 'requirement' | RequirementsErrorKeySpecifier)[]
export type RequirementsErrorFieldPolicy = {
	code?: FieldPolicy<any> | FieldReadFunction<any>
	reason?: FieldPolicy<any> | FieldReadFunction<any>
	requirement?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ReserveTransactionKeySpecifier = (
	| 'amount'
	| 'currency'
	| 'description'
	| 'id'
	| 'object'
	| ReserveTransactionKeySpecifier
)[]
export type ReserveTransactionFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ReviewKeySpecifier = (
	| '_id'
	| 'body'
	| 'createdAt'
	| 'deletedAt'
	| 'parent'
	| 'parentId'
	| 'review'
	| 'updatedAt'
	| 'userId'
	| ReviewKeySpecifier
)[]
export type ReviewFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	body?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
	parentId?: FieldPolicy<any> | FieldReadFunction<any>
	review?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
}
export type RoleKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'organization'
	| 'organizationId'
	| 'role'
	| 'updatedAt'
	| 'user'
	| 'userId'
	| RoleKeySpecifier
)[]
export type RoleFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	organization?: FieldPolicy<any> | FieldReadFunction<any>
	organizationId?: FieldPolicy<any> | FieldReadFunction<any>
	role?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	user?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SGLN96ComponentsKeySpecifier = (
	| 'companyPrefix'
	| 'extension'
	| 'filter'
	| 'locationReference'
	| 'scheme'
	| SGLN96ComponentsKeySpecifier
)[]
export type SGLN96ComponentsFieldPolicy = {
	companyPrefix?: FieldPolicy<any> | FieldReadFunction<any>
	extension?: FieldPolicy<any> | FieldReadFunction<any>
	filter?: FieldPolicy<any> | FieldReadFunction<any>
	locationReference?: FieldPolicy<any> | FieldReadFunction<any>
	scheme?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SGTIN96ComponentsKeySpecifier = (
	| 'companyPrefix'
	| 'filter'
	| 'itemReference'
	| 'scheme'
	| 'serialNumber'
	| SGTIN96ComponentsKeySpecifier
)[]
export type SGTIN96ComponentsFieldPolicy = {
	companyPrefix?: FieldPolicy<any> | FieldReadFunction<any>
	filter?: FieldPolicy<any> | FieldReadFunction<any>
	itemReference?: FieldPolicy<any> | FieldReadFunction<any>
	scheme?: FieldPolicy<any> | FieldReadFunction<any>
	serialNumber?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SSCC96ComponentsKeySpecifier = (
	| 'companyPrefix'
	| 'extensionDigit'
	| 'filter'
	| 'scheme'
	| 'serialReference'
	| SSCC96ComponentsKeySpecifier
)[]
export type SSCC96ComponentsFieldPolicy = {
	companyPrefix?: FieldPolicy<any> | FieldReadFunction<any>
	extensionDigit?: FieldPolicy<any> | FieldReadFunction<any>
	filter?: FieldPolicy<any> | FieldReadFunction<any>
	scheme?: FieldPolicy<any> | FieldReadFunction<any>
	serialReference?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SepaCreditTransferKeySpecifier = (
	| 'bank_name'
	| 'bic'
	| 'iban'
	| 'refund_account_holder_address_city'
	| 'refund_account_holder_address_country'
	| 'refund_account_holder_address_line1'
	| 'refund_account_holder_address_line2'
	| 'refund_account_holder_address_postal_code'
	| 'refund_account_holder_address_state'
	| 'refund_account_holder_name'
	| 'refund_iban'
	| SepaCreditTransferKeySpecifier
)[]
export type SepaCreditTransferFieldPolicy = {
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	bic?: FieldPolicy<any> | FieldReadFunction<any>
	iban?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_city?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_country?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_line1?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_line2?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_postal_code?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_address_state?: FieldPolicy<any> | FieldReadFunction<any>
	refund_account_holder_name?: FieldPolicy<any> | FieldReadFunction<any>
	refund_iban?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SepaDebitKeySpecifier = (
	| 'bank_code'
	| 'branch_code'
	| 'country'
	| 'fingerprint'
	| 'generated_from'
	| 'last4'
	| SepaDebitKeySpecifier
)[]
export type SepaDebitFieldPolicy = {
	bank_code?: FieldPolicy<any> | FieldReadFunction<any>
	branch_code?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	generated_from?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SepaDebitGeneratedFromKeySpecifier = ('charge' | 'setup_attempt' | SepaDebitGeneratedFromKeySpecifier)[]
export type SepaDebitGeneratedFromFieldPolicy = {
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	setup_attempt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SessionKeySpecifier = ('browser' | 'device' | 'platform' | 'version' | SessionKeySpecifier)[]
export type SessionFieldPolicy = {
	browser?: FieldPolicy<any> | FieldReadFunction<any>
	device?: FieldPolicy<any> | FieldReadFunction<any>
	platform?: FieldPolicy<any> | FieldReadFunction<any>
	version?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SetupAttemptKeySpecifier = (
	| 'application'
	| 'attach_to_self'
	| 'created'
	| 'customer'
	| 'flow_directions'
	| 'id'
	| 'livemode'
	| 'object'
	| 'on_behalf_of'
	| 'payment_method'
	| 'payment_method_details'
	| 'setup_error'
	| 'setup_intent'
	| 'status'
	| 'usage'
	| SetupAttemptKeySpecifier
)[]
export type SetupAttemptFieldPolicy = {
	application?: FieldPolicy<any> | FieldReadFunction<any>
	attach_to_self?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	flow_directions?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	on_behalf_of?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_details?: FieldPolicy<any> | FieldReadFunction<any>
	setup_error?: FieldPolicy<any> | FieldReadFunction<any>
	setup_intent?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SetupErrorKeySpecifier = (
	| 'charge'
	| 'code'
	| 'decline_code'
	| 'doc_url'
	| 'message'
	| 'param'
	| 'payment_intent'
	| 'payment_method'
	| 'payment_method_type'
	| 'setup_intent'
	| 'source'
	| 'type'
	| SetupErrorKeySpecifier
)[]
export type SetupErrorFieldPolicy = {
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	code?: FieldPolicy<any> | FieldReadFunction<any>
	decline_code?: FieldPolicy<any> | FieldReadFunction<any>
	doc_url?: FieldPolicy<any> | FieldReadFunction<any>
	message?: FieldPolicy<any> | FieldReadFunction<any>
	param?: FieldPolicy<any> | FieldReadFunction<any>
	payment_intent?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_type?: FieldPolicy<any> | FieldReadFunction<any>
	setup_intent?: FieldPolicy<any> | FieldReadFunction<any>
	source?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SetupIntentKeySpecifier = (
	| 'application'
	| 'attach_to_self'
	| 'automatic_payment_methods'
	| 'cancellation_reason'
	| 'client_secret'
	| 'created'
	| 'customer'
	| 'description'
	| 'flow_directions'
	| 'id'
	| 'last_setup_error'
	| 'latest_attempt'
	| 'livemode'
	| 'mandate'
	| 'metadata'
	| 'next_action'
	| 'object'
	| 'on_behalf_of'
	| 'payment_method'
	| 'payment_method_configuration_details'
	| 'payment_method_options'
	| 'payment_method_types'
	| 'single_use_mandate'
	| 'status'
	| 'usage'
	| SetupIntentKeySpecifier
)[]
export type SetupIntentFieldPolicy = {
	application?: FieldPolicy<any> | FieldReadFunction<any>
	attach_to_self?: FieldPolicy<any> | FieldReadFunction<any>
	automatic_payment_methods?: FieldPolicy<any> | FieldReadFunction<any>
	cancellation_reason?: FieldPolicy<any> | FieldReadFunction<any>
	client_secret?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	flow_directions?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	last_setup_error?: FieldPolicy<any> | FieldReadFunction<any>
	latest_attempt?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	mandate?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	next_action?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	on_behalf_of?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_configuration_details?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_options?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_types?: FieldPolicy<any> | FieldReadFunction<any>
	single_use_mandate?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	usage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SetupIntentNextActionKeySpecifier = (
	| 'redirect_to_url'
	| 'type'
	| 'use_stripe_sdk'
	| 'verify_with_microdeposits'
	| SetupIntentNextActionKeySpecifier
)[]
export type SetupIntentNextActionFieldPolicy = {
	redirect_to_url?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	use_stripe_sdk?: FieldPolicy<any> | FieldReadFunction<any>
	verify_with_microdeposits?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SetupIntentPaymentMethodOptionsKeySpecifier = (
	| 'acss_debit'
	| 'card'
	| 'link'
	| 'sepa_debit'
	| 'us_bank_account'
	| SetupIntentPaymentMethodOptionsKeySpecifier
)[]
export type SetupIntentPaymentMethodOptionsFieldPolicy = {
	acss_debit?: FieldPolicy<any> | FieldReadFunction<any>
	card?: FieldPolicy<any> | FieldReadFunction<any>
	link?: FieldPolicy<any> | FieldReadFunction<any>
	sepa_debit?: FieldPolicy<any> | FieldReadFunction<any>
	us_bank_account?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SetupIntentPaymentMethodOptionsAcssDebitKeySpecifier = (
	| 'currency'
	| 'mandate_options'
	| 'verification_method'
	| SetupIntentPaymentMethodOptionsAcssDebitKeySpecifier
)[]
export type SetupIntentPaymentMethodOptionsAcssDebitFieldPolicy = {
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	mandate_options?: FieldPolicy<any> | FieldReadFunction<any>
	verification_method?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SetupIntentPaymentMethodOptionsCardKeySpecifier = (
	| 'mandate_options'
	| 'network'
	| 'request_three_d_secure'
	| SetupIntentPaymentMethodOptionsCardKeySpecifier
)[]
export type SetupIntentPaymentMethodOptionsCardFieldPolicy = {
	mandate_options?: FieldPolicy<any> | FieldReadFunction<any>
	network?: FieldPolicy<any> | FieldReadFunction<any>
	request_three_d_secure?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SetupIntentPaymentMethodOptionsLinkKeySpecifier = (
	| 'persistent_token'
	| SetupIntentPaymentMethodOptionsLinkKeySpecifier
)[]
export type SetupIntentPaymentMethodOptionsLinkFieldPolicy = {
	persistent_token?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SetupIntentPaymentMethodOptionsSepaDebitKeySpecifier = (
	| 'mandate_options'
	| SetupIntentPaymentMethodOptionsSepaDebitKeySpecifier
)[]
export type SetupIntentPaymentMethodOptionsSepaDebitFieldPolicy = {
	mandate_options?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SetupIntentPaymentMethodOptionsUsBankAccountKeySpecifier = (
	| 'financial_connections'
	| 'verification_method'
	| SetupIntentPaymentMethodOptionsUsBankAccountKeySpecifier
)[]
export type SetupIntentPaymentMethodOptionsUsBankAccountFieldPolicy = {
	financial_connections?: FieldPolicy<any> | FieldReadFunction<any>
	verification_method?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SetupIntentPaymentMethodOptionsUsBankAccountFinancialConnectionsKeySpecifier = (
	| 'permissions'
	| 'prefetch'
	| 'return_url'
	| SetupIntentPaymentMethodOptionsUsBankAccountFinancialConnectionsKeySpecifier
)[]
export type SetupIntentPaymentMethodOptionsUsBankAccountFinancialConnectionsFieldPolicy = {
	permissions?: FieldPolicy<any> | FieldReadFunction<any>
	prefetch?: FieldPolicy<any> | FieldReadFunction<any>
	return_url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ShippingKeySpecifier = (
	| 'address'
	| 'carrier'
	| 'name'
	| 'phone'
	| 'tracking_number'
	| ShippingKeySpecifier
)[]
export type ShippingFieldPolicy = {
	address?: FieldPolicy<any> | FieldReadFunction<any>
	carrier?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	phone?: FieldPolicy<any> | FieldReadFunction<any>
	tracking_number?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ShippingCostKeySpecifier = (
	| 'amount_subtotal'
	| 'amount_tax'
	| 'amount_total'
	| 'shipping_rate'
	| 'taxes'
	| ShippingCostKeySpecifier
)[]
export type ShippingCostFieldPolicy = {
	amount_subtotal?: FieldPolicy<any> | FieldReadFunction<any>
	amount_tax?: FieldPolicy<any> | FieldReadFunction<any>
	amount_total?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_rate?: FieldPolicy<any> | FieldReadFunction<any>
	taxes?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ShippingCostTaxKeySpecifier = (
	| 'amount'
	| 'rate'
	| 'taxability_reason'
	| 'taxable_amount'
	| ShippingCostTaxKeySpecifier
)[]
export type ShippingCostTaxFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	rate?: FieldPolicy<any> | FieldReadFunction<any>
	taxability_reason?: FieldPolicy<any> | FieldReadFunction<any>
	taxable_amount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ShippingDetailsKeySpecifier = (
	| 'address'
	| 'carrier'
	| 'name'
	| 'phone'
	| 'tracking_number'
	| ShippingDetailsKeySpecifier
)[]
export type ShippingDetailsFieldPolicy = {
	address?: FieldPolicy<any> | FieldReadFunction<any>
	carrier?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	phone?: FieldPolicy<any> | FieldReadFunction<any>
	tracking_number?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ShippingRateKeySpecifier = (
	| 'active'
	| 'created'
	| 'delivery_estimate'
	| 'display_name'
	| 'fixed_amount'
	| 'id'
	| 'livemode'
	| 'metadata'
	| 'object'
	| 'tax_behavior'
	| 'tax_code'
	| 'type'
	| ShippingRateKeySpecifier
)[]
export type ShippingRateFieldPolicy = {
	active?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	delivery_estimate?: FieldPolicy<any> | FieldReadFunction<any>
	display_name?: FieldPolicy<any> | FieldReadFunction<any>
	fixed_amount?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	tax_behavior?: FieldPolicy<any> | FieldReadFunction<any>
	tax_code?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ShippingRateDeliveryEstimateKeySpecifier = (
	| 'maximum'
	| 'minimum'
	| ShippingRateDeliveryEstimateKeySpecifier
)[]
export type ShippingRateDeliveryEstimateFieldPolicy = {
	maximum?: FieldPolicy<any> | FieldReadFunction<any>
	minimum?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ShippingRateDeliveryEstimateMaximumKeySpecifier = (
	| 'unit'
	| 'value'
	| ShippingRateDeliveryEstimateMaximumKeySpecifier
)[]
export type ShippingRateDeliveryEstimateMaximumFieldPolicy = {
	unit?: FieldPolicy<any> | FieldReadFunction<any>
	value?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ShippingRateDeliveryEstimateMinimumKeySpecifier = (
	| 'unit'
	| 'value'
	| ShippingRateDeliveryEstimateMinimumKeySpecifier
)[]
export type ShippingRateDeliveryEstimateMinimumFieldPolicy = {
	unit?: FieldPolicy<any> | FieldReadFunction<any>
	value?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ShippingRateFixedAmountKeySpecifier = (
	| 'amount'
	| 'currency'
	| 'currency_options'
	| ShippingRateFixedAmountKeySpecifier
)[]
export type ShippingRateFixedAmountFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	currency_options?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ShippingRateFixedAmountCurrencyOptionsKeySpecifier = (
	| 'AED'
	| 'AFN'
	| 'ALL'
	| 'AMD'
	| 'ANG'
	| 'AOA'
	| 'ARS'
	| 'AUD'
	| 'AWG'
	| 'AZN'
	| 'BAM'
	| 'BBD'
	| 'BDT'
	| 'BGN'
	| 'BIF'
	| 'BMD'
	| 'BND'
	| 'BOB'
	| 'BRL'
	| 'BSD'
	| 'BWP'
	| 'BYN'
	| 'BZD'
	| 'CAD'
	| 'CDF'
	| 'CHF'
	| 'CLP'
	| 'CNY'
	| 'COP'
	| 'CRC'
	| 'CVE'
	| 'CZK'
	| 'DJF'
	| 'DKK'
	| 'DOP'
	| 'DZD'
	| 'EGP'
	| 'ETB'
	| 'EUR'
	| 'FJD'
	| 'FKP'
	| 'GBP'
	| 'GEL'
	| 'GIP'
	| 'GMD'
	| 'GNF'
	| 'GTQ'
	| 'GYD'
	| 'HKD'
	| 'HNL'
	| 'HRK'
	| 'HTG'
	| 'HUF'
	| 'IDR'
	| 'ILS'
	| 'INR'
	| 'ISK'
	| 'JMD'
	| 'JPY'
	| 'KES'
	| 'KGS'
	| 'KHR'
	| 'KMF'
	| 'KRW'
	| 'KYD'
	| 'KZT'
	| 'LAK'
	| 'LBP'
	| 'LKR'
	| 'LRD'
	| 'LSL'
	| 'MAD'
	| 'MDL'
	| 'MGA'
	| 'MKD'
	| 'MMK'
	| 'MNT'
	| 'MOP'
	| 'MRO'
	| 'MUR'
	| 'MVR'
	| 'MWK'
	| 'MXN'
	| 'MYR'
	| 'MZN'
	| 'NAD'
	| 'NGN'
	| 'NIO'
	| 'NOK'
	| 'NPR'
	| 'NZD'
	| 'PAB'
	| 'PEN'
	| 'PGK'
	| 'PHP'
	| 'PKR'
	| 'PLN'
	| 'PYG'
	| 'QAR'
	| 'RON'
	| 'RSD'
	| 'RUB'
	| 'RWF'
	| 'SAR'
	| 'SBD'
	| 'SCR'
	| 'SEK'
	| 'SGD'
	| 'SHP'
	| 'SLE'
	| 'SLL'
	| 'SOS'
	| 'SRD'
	| 'STD'
	| 'SZL'
	| 'THB'
	| 'TJS'
	| 'TOP'
	| 'TRY'
	| 'TTD'
	| 'TWD'
	| 'TZS'
	| 'UAH'
	| 'UGX'
	| 'USD'
	| 'UYU'
	| 'UZS'
	| 'VND'
	| 'VUV'
	| 'WST'
	| 'XAF'
	| 'XCD'
	| 'XOF'
	| 'XPF'
	| 'YER'
	| 'ZAR'
	| 'ZMW'
	| ShippingRateFixedAmountCurrencyOptionsKeySpecifier
)[]
export type ShippingRateFixedAmountCurrencyOptionsFieldPolicy = {
	AED?: FieldPolicy<any> | FieldReadFunction<any>
	AFN?: FieldPolicy<any> | FieldReadFunction<any>
	ALL?: FieldPolicy<any> | FieldReadFunction<any>
	AMD?: FieldPolicy<any> | FieldReadFunction<any>
	ANG?: FieldPolicy<any> | FieldReadFunction<any>
	AOA?: FieldPolicy<any> | FieldReadFunction<any>
	ARS?: FieldPolicy<any> | FieldReadFunction<any>
	AUD?: FieldPolicy<any> | FieldReadFunction<any>
	AWG?: FieldPolicy<any> | FieldReadFunction<any>
	AZN?: FieldPolicy<any> | FieldReadFunction<any>
	BAM?: FieldPolicy<any> | FieldReadFunction<any>
	BBD?: FieldPolicy<any> | FieldReadFunction<any>
	BDT?: FieldPolicy<any> | FieldReadFunction<any>
	BGN?: FieldPolicy<any> | FieldReadFunction<any>
	BIF?: FieldPolicy<any> | FieldReadFunction<any>
	BMD?: FieldPolicy<any> | FieldReadFunction<any>
	BND?: FieldPolicy<any> | FieldReadFunction<any>
	BOB?: FieldPolicy<any> | FieldReadFunction<any>
	BRL?: FieldPolicy<any> | FieldReadFunction<any>
	BSD?: FieldPolicy<any> | FieldReadFunction<any>
	BWP?: FieldPolicy<any> | FieldReadFunction<any>
	BYN?: FieldPolicy<any> | FieldReadFunction<any>
	BZD?: FieldPolicy<any> | FieldReadFunction<any>
	CAD?: FieldPolicy<any> | FieldReadFunction<any>
	CDF?: FieldPolicy<any> | FieldReadFunction<any>
	CHF?: FieldPolicy<any> | FieldReadFunction<any>
	CLP?: FieldPolicy<any> | FieldReadFunction<any>
	CNY?: FieldPolicy<any> | FieldReadFunction<any>
	COP?: FieldPolicy<any> | FieldReadFunction<any>
	CRC?: FieldPolicy<any> | FieldReadFunction<any>
	CVE?: FieldPolicy<any> | FieldReadFunction<any>
	CZK?: FieldPolicy<any> | FieldReadFunction<any>
	DJF?: FieldPolicy<any> | FieldReadFunction<any>
	DKK?: FieldPolicy<any> | FieldReadFunction<any>
	DOP?: FieldPolicy<any> | FieldReadFunction<any>
	DZD?: FieldPolicy<any> | FieldReadFunction<any>
	EGP?: FieldPolicy<any> | FieldReadFunction<any>
	ETB?: FieldPolicy<any> | FieldReadFunction<any>
	EUR?: FieldPolicy<any> | FieldReadFunction<any>
	FJD?: FieldPolicy<any> | FieldReadFunction<any>
	FKP?: FieldPolicy<any> | FieldReadFunction<any>
	GBP?: FieldPolicy<any> | FieldReadFunction<any>
	GEL?: FieldPolicy<any> | FieldReadFunction<any>
	GIP?: FieldPolicy<any> | FieldReadFunction<any>
	GMD?: FieldPolicy<any> | FieldReadFunction<any>
	GNF?: FieldPolicy<any> | FieldReadFunction<any>
	GTQ?: FieldPolicy<any> | FieldReadFunction<any>
	GYD?: FieldPolicy<any> | FieldReadFunction<any>
	HKD?: FieldPolicy<any> | FieldReadFunction<any>
	HNL?: FieldPolicy<any> | FieldReadFunction<any>
	HRK?: FieldPolicy<any> | FieldReadFunction<any>
	HTG?: FieldPolicy<any> | FieldReadFunction<any>
	HUF?: FieldPolicy<any> | FieldReadFunction<any>
	IDR?: FieldPolicy<any> | FieldReadFunction<any>
	ILS?: FieldPolicy<any> | FieldReadFunction<any>
	INR?: FieldPolicy<any> | FieldReadFunction<any>
	ISK?: FieldPolicy<any> | FieldReadFunction<any>
	JMD?: FieldPolicy<any> | FieldReadFunction<any>
	JPY?: FieldPolicy<any> | FieldReadFunction<any>
	KES?: FieldPolicy<any> | FieldReadFunction<any>
	KGS?: FieldPolicy<any> | FieldReadFunction<any>
	KHR?: FieldPolicy<any> | FieldReadFunction<any>
	KMF?: FieldPolicy<any> | FieldReadFunction<any>
	KRW?: FieldPolicy<any> | FieldReadFunction<any>
	KYD?: FieldPolicy<any> | FieldReadFunction<any>
	KZT?: FieldPolicy<any> | FieldReadFunction<any>
	LAK?: FieldPolicy<any> | FieldReadFunction<any>
	LBP?: FieldPolicy<any> | FieldReadFunction<any>
	LKR?: FieldPolicy<any> | FieldReadFunction<any>
	LRD?: FieldPolicy<any> | FieldReadFunction<any>
	LSL?: FieldPolicy<any> | FieldReadFunction<any>
	MAD?: FieldPolicy<any> | FieldReadFunction<any>
	MDL?: FieldPolicy<any> | FieldReadFunction<any>
	MGA?: FieldPolicy<any> | FieldReadFunction<any>
	MKD?: FieldPolicy<any> | FieldReadFunction<any>
	MMK?: FieldPolicy<any> | FieldReadFunction<any>
	MNT?: FieldPolicy<any> | FieldReadFunction<any>
	MOP?: FieldPolicy<any> | FieldReadFunction<any>
	MRO?: FieldPolicy<any> | FieldReadFunction<any>
	MUR?: FieldPolicy<any> | FieldReadFunction<any>
	MVR?: FieldPolicy<any> | FieldReadFunction<any>
	MWK?: FieldPolicy<any> | FieldReadFunction<any>
	MXN?: FieldPolicy<any> | FieldReadFunction<any>
	MYR?: FieldPolicy<any> | FieldReadFunction<any>
	MZN?: FieldPolicy<any> | FieldReadFunction<any>
	NAD?: FieldPolicy<any> | FieldReadFunction<any>
	NGN?: FieldPolicy<any> | FieldReadFunction<any>
	NIO?: FieldPolicy<any> | FieldReadFunction<any>
	NOK?: FieldPolicy<any> | FieldReadFunction<any>
	NPR?: FieldPolicy<any> | FieldReadFunction<any>
	NZD?: FieldPolicy<any> | FieldReadFunction<any>
	PAB?: FieldPolicy<any> | FieldReadFunction<any>
	PEN?: FieldPolicy<any> | FieldReadFunction<any>
	PGK?: FieldPolicy<any> | FieldReadFunction<any>
	PHP?: FieldPolicy<any> | FieldReadFunction<any>
	PKR?: FieldPolicy<any> | FieldReadFunction<any>
	PLN?: FieldPolicy<any> | FieldReadFunction<any>
	PYG?: FieldPolicy<any> | FieldReadFunction<any>
	QAR?: FieldPolicy<any> | FieldReadFunction<any>
	RON?: FieldPolicy<any> | FieldReadFunction<any>
	RSD?: FieldPolicy<any> | FieldReadFunction<any>
	RUB?: FieldPolicy<any> | FieldReadFunction<any>
	RWF?: FieldPolicy<any> | FieldReadFunction<any>
	SAR?: FieldPolicy<any> | FieldReadFunction<any>
	SBD?: FieldPolicy<any> | FieldReadFunction<any>
	SCR?: FieldPolicy<any> | FieldReadFunction<any>
	SEK?: FieldPolicy<any> | FieldReadFunction<any>
	SGD?: FieldPolicy<any> | FieldReadFunction<any>
	SHP?: FieldPolicy<any> | FieldReadFunction<any>
	SLE?: FieldPolicy<any> | FieldReadFunction<any>
	SLL?: FieldPolicy<any> | FieldReadFunction<any>
	SOS?: FieldPolicy<any> | FieldReadFunction<any>
	SRD?: FieldPolicy<any> | FieldReadFunction<any>
	STD?: FieldPolicy<any> | FieldReadFunction<any>
	SZL?: FieldPolicy<any> | FieldReadFunction<any>
	THB?: FieldPolicy<any> | FieldReadFunction<any>
	TJS?: FieldPolicy<any> | FieldReadFunction<any>
	TOP?: FieldPolicy<any> | FieldReadFunction<any>
	TRY?: FieldPolicy<any> | FieldReadFunction<any>
	TTD?: FieldPolicy<any> | FieldReadFunction<any>
	TWD?: FieldPolicy<any> | FieldReadFunction<any>
	TZS?: FieldPolicy<any> | FieldReadFunction<any>
	UAH?: FieldPolicy<any> | FieldReadFunction<any>
	UGX?: FieldPolicy<any> | FieldReadFunction<any>
	USD?: FieldPolicy<any> | FieldReadFunction<any>
	UYU?: FieldPolicy<any> | FieldReadFunction<any>
	UZS?: FieldPolicy<any> | FieldReadFunction<any>
	VND?: FieldPolicy<any> | FieldReadFunction<any>
	VUV?: FieldPolicy<any> | FieldReadFunction<any>
	WST?: FieldPolicy<any> | FieldReadFunction<any>
	XAF?: FieldPolicy<any> | FieldReadFunction<any>
	XCD?: FieldPolicy<any> | FieldReadFunction<any>
	XOF?: FieldPolicy<any> | FieldReadFunction<any>
	XPF?: FieldPolicy<any> | FieldReadFunction<any>
	YER?: FieldPolicy<any> | FieldReadFunction<any>
	ZAR?: FieldPolicy<any> | FieldReadFunction<any>
	ZMW?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SofortKeySpecifier = ('country' | SofortKeySpecifier)[]
export type SofortFieldPolicy = {
	country?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceKeySpecifier = (
	| 'ach_credit_transfer'
	| 'ach_debit'
	| 'acss_debit'
	| 'alipay'
	| 'allow_redisplay'
	| 'amount'
	| 'au_becs_debit'
	| 'bancontact'
	| 'card'
	| 'card_present'
	| 'client_secret'
	| 'code_verification'
	| 'created'
	| 'currency'
	| 'customer'
	| 'eps'
	| 'flow'
	| 'giropay'
	| 'id'
	| 'ideal'
	| 'klarna'
	| 'livemode'
	| 'metadata'
	| 'multibanco'
	| 'object'
	| 'owner'
	| 'p24'
	| 'receiver'
	| 'redirect'
	| 'sepa_credit_transfer'
	| 'sepa_debit'
	| 'sofort'
	| 'source_order'
	| 'statement_descriptor'
	| 'status'
	| 'three_d_secure'
	| 'type'
	| 'usage'
	| 'wechat'
	| SourceKeySpecifier
)[]
export type SourceFieldPolicy = {
	ach_credit_transfer?: FieldPolicy<any> | FieldReadFunction<any>
	ach_debit?: FieldPolicy<any> | FieldReadFunction<any>
	acss_debit?: FieldPolicy<any> | FieldReadFunction<any>
	alipay?: FieldPolicy<any> | FieldReadFunction<any>
	allow_redisplay?: FieldPolicy<any> | FieldReadFunction<any>
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	au_becs_debit?: FieldPolicy<any> | FieldReadFunction<any>
	bancontact?: FieldPolicy<any> | FieldReadFunction<any>
	card?: FieldPolicy<any> | FieldReadFunction<any>
	card_present?: FieldPolicy<any> | FieldReadFunction<any>
	client_secret?: FieldPolicy<any> | FieldReadFunction<any>
	code_verification?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	eps?: FieldPolicy<any> | FieldReadFunction<any>
	flow?: FieldPolicy<any> | FieldReadFunction<any>
	giropay?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	ideal?: FieldPolicy<any> | FieldReadFunction<any>
	klarna?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	multibanco?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	owner?: FieldPolicy<any> | FieldReadFunction<any>
	p24?: FieldPolicy<any> | FieldReadFunction<any>
	receiver?: FieldPolicy<any> | FieldReadFunction<any>
	redirect?: FieldPolicy<any> | FieldReadFunction<any>
	sepa_credit_transfer?: FieldPolicy<any> | FieldReadFunction<any>
	sepa_debit?: FieldPolicy<any> | FieldReadFunction<any>
	sofort?: FieldPolicy<any> | FieldReadFunction<any>
	source_order?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	three_d_secure?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	usage?: FieldPolicy<any> | FieldReadFunction<any>
	wechat?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceAcssDebitKeySpecifier = (
	| 'bank_address_city'
	| 'bank_address_line_1'
	| 'bank_address_line_2'
	| 'bank_address_postal_code'
	| 'bank_name'
	| 'category'
	| 'country'
	| 'fingerprint'
	| 'last4'
	| 'routing_number'
	| SourceAcssDebitKeySpecifier
)[]
export type SourceAcssDebitFieldPolicy = {
	bank_address_city?: FieldPolicy<any> | FieldReadFunction<any>
	bank_address_line_1?: FieldPolicy<any> | FieldReadFunction<any>
	bank_address_line_2?: FieldPolicy<any> | FieldReadFunction<any>
	bank_address_postal_code?: FieldPolicy<any> | FieldReadFunction<any>
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	category?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	routing_number?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceAlipayKeySpecifier = (
	| 'data_string'
	| 'native_url'
	| 'statement_descriptor'
	| SourceAlipayKeySpecifier
)[]
export type SourceAlipayFieldPolicy = {
	data_string?: FieldPolicy<any> | FieldReadFunction<any>
	native_url?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceAuBecsDebitKeySpecifier = ('bsb_number' | 'fingerprint' | 'last4' | SourceAuBecsDebitKeySpecifier)[]
export type SourceAuBecsDebitFieldPolicy = {
	bsb_number?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceBancontactKeySpecifier = (
	| 'bank_code'
	| 'bank_name'
	| 'bic'
	| 'iban_last4'
	| 'preferred_language'
	| 'statement_descriptor'
	| SourceBancontactKeySpecifier
)[]
export type SourceBancontactFieldPolicy = {
	bank_code?: FieldPolicy<any> | FieldReadFunction<any>
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	bic?: FieldPolicy<any> | FieldReadFunction<any>
	iban_last4?: FieldPolicy<any> | FieldReadFunction<any>
	preferred_language?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceCardKeySpecifier = (
	| 'address_line1_check'
	| 'address_zip_check'
	| 'brand'
	| 'country'
	| 'cvc_check'
	| 'description'
	| 'dynamic_last4'
	| 'exp_month'
	| 'exp_year'
	| 'fingerprint'
	| 'funding'
	| 'iin'
	| 'issuer'
	| 'last4'
	| 'name'
	| 'three_d_secure'
	| 'tokenization_method'
	| SourceCardKeySpecifier
)[]
export type SourceCardFieldPolicy = {
	address_line1_check?: FieldPolicy<any> | FieldReadFunction<any>
	address_zip_check?: FieldPolicy<any> | FieldReadFunction<any>
	brand?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	cvc_check?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	dynamic_last4?: FieldPolicy<any> | FieldReadFunction<any>
	exp_month?: FieldPolicy<any> | FieldReadFunction<any>
	exp_year?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	funding?: FieldPolicy<any> | FieldReadFunction<any>
	iin?: FieldPolicy<any> | FieldReadFunction<any>
	issuer?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	three_d_secure?: FieldPolicy<any> | FieldReadFunction<any>
	tokenization_method?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceCardPresentKeySpecifier = (
	| 'application_cryptogram'
	| 'application_preferred_name'
	| 'authorization_code'
	| 'authorization_response_code'
	| 'brand'
	| 'country'
	| 'cvm_type'
	| 'data_type'
	| 'dedicated_file_name'
	| 'description'
	| 'emv_auth_data'
	| 'evidence_customer_signature'
	| 'evidence_transaction_certificate'
	| 'exp_month'
	| 'exp_year'
	| 'fingerprint'
	| 'funding'
	| 'iin'
	| 'issuer'
	| 'last4'
	| 'pos_device_id'
	| 'pos_entry_mode'
	| 'read_method'
	| 'reader'
	| 'terminal_verification_results'
	| 'transaction_status_information'
	| SourceCardPresentKeySpecifier
)[]
export type SourceCardPresentFieldPolicy = {
	application_cryptogram?: FieldPolicy<any> | FieldReadFunction<any>
	application_preferred_name?: FieldPolicy<any> | FieldReadFunction<any>
	authorization_code?: FieldPolicy<any> | FieldReadFunction<any>
	authorization_response_code?: FieldPolicy<any> | FieldReadFunction<any>
	brand?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	cvm_type?: FieldPolicy<any> | FieldReadFunction<any>
	data_type?: FieldPolicy<any> | FieldReadFunction<any>
	dedicated_file_name?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	emv_auth_data?: FieldPolicy<any> | FieldReadFunction<any>
	evidence_customer_signature?: FieldPolicy<any> | FieldReadFunction<any>
	evidence_transaction_certificate?: FieldPolicy<any> | FieldReadFunction<any>
	exp_month?: FieldPolicy<any> | FieldReadFunction<any>
	exp_year?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	funding?: FieldPolicy<any> | FieldReadFunction<any>
	iin?: FieldPolicy<any> | FieldReadFunction<any>
	issuer?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	pos_device_id?: FieldPolicy<any> | FieldReadFunction<any>
	pos_entry_mode?: FieldPolicy<any> | FieldReadFunction<any>
	read_method?: FieldPolicy<any> | FieldReadFunction<any>
	reader?: FieldPolicy<any> | FieldReadFunction<any>
	terminal_verification_results?: FieldPolicy<any> | FieldReadFunction<any>
	transaction_status_information?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceEpsKeySpecifier = ('reference' | 'statement_descriptor' | SourceEpsKeySpecifier)[]
export type SourceEpsFieldPolicy = {
	reference?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceGiropayKeySpecifier = (
	| 'bank_code'
	| 'bank_name'
	| 'bic'
	| 'statement_descriptor'
	| SourceGiropayKeySpecifier
)[]
export type SourceGiropayFieldPolicy = {
	bank_code?: FieldPolicy<any> | FieldReadFunction<any>
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	bic?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceIdealKeySpecifier = (
	| 'bank'
	| 'bic'
	| 'iban_last4'
	| 'statement_descriptor'
	| SourceIdealKeySpecifier
)[]
export type SourceIdealFieldPolicy = {
	bank?: FieldPolicy<any> | FieldReadFunction<any>
	bic?: FieldPolicy<any> | FieldReadFunction<any>
	iban_last4?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceKlarnaKeySpecifier = (
	| 'background_image_url'
	| 'client_token'
	| 'first_name'
	| 'last_name'
	| 'locale'
	| 'logo_url'
	| 'page_title'
	| 'pay_later_asset_urls_descriptive'
	| 'pay_later_asset_urls_standard'
	| 'pay_later_name'
	| 'pay_later_redirect_url'
	| 'pay_now_asset_urls_descriptive'
	| 'pay_now_asset_urls_standard'
	| 'pay_now_name'
	| 'pay_now_redirect_url'
	| 'pay_over_time_asset_urls_descriptive'
	| 'pay_over_time_asset_urls_standard'
	| 'pay_over_time_name'
	| 'pay_over_time_redirect_url'
	| 'payment_method_categories'
	| 'purchase_country'
	| 'purchase_type'
	| 'redirect_url'
	| 'shipping_delay'
	| 'shipping_first_name'
	| 'shipping_last_name'
	| SourceKlarnaKeySpecifier
)[]
export type SourceKlarnaFieldPolicy = {
	background_image_url?: FieldPolicy<any> | FieldReadFunction<any>
	client_token?: FieldPolicy<any> | FieldReadFunction<any>
	first_name?: FieldPolicy<any> | FieldReadFunction<any>
	last_name?: FieldPolicy<any> | FieldReadFunction<any>
	locale?: FieldPolicy<any> | FieldReadFunction<any>
	logo_url?: FieldPolicy<any> | FieldReadFunction<any>
	page_title?: FieldPolicy<any> | FieldReadFunction<any>
	pay_later_asset_urls_descriptive?: FieldPolicy<any> | FieldReadFunction<any>
	pay_later_asset_urls_standard?: FieldPolicy<any> | FieldReadFunction<any>
	pay_later_name?: FieldPolicy<any> | FieldReadFunction<any>
	pay_later_redirect_url?: FieldPolicy<any> | FieldReadFunction<any>
	pay_now_asset_urls_descriptive?: FieldPolicy<any> | FieldReadFunction<any>
	pay_now_asset_urls_standard?: FieldPolicy<any> | FieldReadFunction<any>
	pay_now_name?: FieldPolicy<any> | FieldReadFunction<any>
	pay_now_redirect_url?: FieldPolicy<any> | FieldReadFunction<any>
	pay_over_time_asset_urls_descriptive?: FieldPolicy<any> | FieldReadFunction<any>
	pay_over_time_asset_urls_standard?: FieldPolicy<any> | FieldReadFunction<any>
	pay_over_time_name?: FieldPolicy<any> | FieldReadFunction<any>
	pay_over_time_redirect_url?: FieldPolicy<any> | FieldReadFunction<any>
	payment_method_categories?: FieldPolicy<any> | FieldReadFunction<any>
	purchase_country?: FieldPolicy<any> | FieldReadFunction<any>
	purchase_type?: FieldPolicy<any> | FieldReadFunction<any>
	redirect_url?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_delay?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_first_name?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_last_name?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceOrderKeySpecifier = (
	| 'amount'
	| 'currency'
	| 'email'
	| 'items'
	| 'shipping'
	| SourceOrderKeySpecifier
)[]
export type SourceOrderFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	items?: FieldPolicy<any> | FieldReadFunction<any>
	shipping?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceP24KeySpecifier = ('reference' | SourceP24KeySpecifier)[]
export type SourceP24FieldPolicy = {
	reference?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceSepaDebitKeySpecifier = (
	| 'bank_code'
	| 'branch_code'
	| 'country'
	| 'fingerprint'
	| 'last4'
	| 'mandate_reference'
	| 'mandate_url'
	| SourceSepaDebitKeySpecifier
)[]
export type SourceSepaDebitFieldPolicy = {
	bank_code?: FieldPolicy<any> | FieldReadFunction<any>
	branch_code?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	mandate_reference?: FieldPolicy<any> | FieldReadFunction<any>
	mandate_url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SourceSofortKeySpecifier = (
	| 'bank_code'
	| 'bank_name'
	| 'bic'
	| 'country'
	| 'iban_last4'
	| 'preferred_language'
	| 'statement_descriptor'
	| SourceSofortKeySpecifier
)[]
export type SourceSofortFieldPolicy = {
	bank_code?: FieldPolicy<any> | FieldReadFunction<any>
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	bic?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	iban_last4?: FieldPolicy<any> | FieldReadFunction<any>
	preferred_language?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StatisticsKeySpecifier = (
	| 'bayesianAverage'
	| 'kurtosis'
	| 'mean'
	| 'median'
	| 'mode'
	| 'percentiles'
	| 'range'
	| 'skewness'
	| 'standardDeviation'
	| 'variance'
	| 'weightedAverage'
	| StatisticsKeySpecifier
)[]
export type StatisticsFieldPolicy = {
	bayesianAverage?: FieldPolicy<any> | FieldReadFunction<any>
	kurtosis?: FieldPolicy<any> | FieldReadFunction<any>
	mean?: FieldPolicy<any> | FieldReadFunction<any>
	median?: FieldPolicy<any> | FieldReadFunction<any>
	mode?: FieldPolicy<any> | FieldReadFunction<any>
	percentiles?: FieldPolicy<any> | FieldReadFunction<any>
	range?: FieldPolicy<any> | FieldReadFunction<any>
	skewness?: FieldPolicy<any> | FieldReadFunction<any>
	standardDeviation?: FieldPolicy<any> | FieldReadFunction<any>
	variance?: FieldPolicy<any> | FieldReadFunction<any>
	weightedAverage?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StatisticsOverTimeKeySpecifier = ('averageRating' | 'count' | 'period' | StatisticsOverTimeKeySpecifier)[]
export type StatisticsOverTimeFieldPolicy = {
	averageRating?: FieldPolicy<any> | FieldReadFunction<any>
	count?: FieldPolicy<any> | FieldReadFunction<any>
	period?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StatsKeySpecifier = (
	| 'averageNewPerWeekDay'
	| 'maxNewPerWeekDay'
	| 'minNewPerWeekDay'
	| 'newPerDay'
	| 'newPerMonth'
	| 'newPerWeek'
	| 'newPerWeekDay'
	| 'newPerYear'
	| 'perDay'
	| 'perMonth'
	| 'perWeek'
	| 'perYear'
	| 'stdDevNewPerWeekDay'
	| 'total'
	| StatsKeySpecifier
)[]
export type StatsFieldPolicy = {
	averageNewPerWeekDay?: FieldPolicy<any> | FieldReadFunction<any>
	maxNewPerWeekDay?: FieldPolicy<any> | FieldReadFunction<any>
	minNewPerWeekDay?: FieldPolicy<any> | FieldReadFunction<any>
	newPerDay?: FieldPolicy<any> | FieldReadFunction<any>
	newPerMonth?: FieldPolicy<any> | FieldReadFunction<any>
	newPerWeek?: FieldPolicy<any> | FieldReadFunction<any>
	newPerWeekDay?: FieldPolicy<any> | FieldReadFunction<any>
	newPerYear?: FieldPolicy<any> | FieldReadFunction<any>
	perDay?: FieldPolicy<any> | FieldReadFunction<any>
	perMonth?: FieldPolicy<any> | FieldReadFunction<any>
	perWeek?: FieldPolicy<any> | FieldReadFunction<any>
	perYear?: FieldPolicy<any> | FieldReadFunction<any>
	stdDevNewPerWeekDay?: FieldPolicy<any> | FieldReadFunction<any>
	total?: FieldPolicy<any> | FieldReadFunction<any>
}
export type Stats2DKeySpecifier = ('x' | 'y' | Stats2DKeySpecifier)[]
export type Stats2DFieldPolicy = {
	x?: FieldPolicy<any> | FieldReadFunction<any>
	y?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StatusDetailsKeySpecifier = ('advancing' | StatusDetailsKeySpecifier)[]
export type StatusDetailsFieldPolicy = {
	advancing?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StatusTransitionsKeySpecifier = (
	| 'finalized_at'
	| 'marked_uncollectible_at'
	| 'paid_at'
	| 'voided_at'
	| StatusTransitionsKeySpecifier
)[]
export type StatusTransitionsFieldPolicy = {
	finalized_at?: FieldPolicy<any> | FieldReadFunction<any>
	marked_uncollectible_at?: FieldPolicy<any> | FieldReadFunction<any>
	paid_at?: FieldPolicy<any> | FieldReadFunction<any>
	voided_at?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StripeAccountControllerKeySpecifier = ('is_controller' | 'type' | StripeAccountControllerKeySpecifier)[]
export type StripeAccountControllerFieldPolicy = {
	is_controller?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StripeAddressKeySpecifier = (
	| 'city'
	| 'country'
	| 'line1'
	| 'line2'
	| 'postal_code'
	| 'state'
	| StripeAddressKeySpecifier
)[]
export type StripeAddressFieldPolicy = {
	city?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	line1?: FieldPolicy<any> | FieldReadFunction<any>
	line2?: FieldPolicy<any> | FieldReadFunction<any>
	postal_code?: FieldPolicy<any> | FieldReadFunction<any>
	state?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StripeEntityIdKeySpecifier = ('id' | StripeEntityIdKeySpecifier)[]
export type StripeEntityIdFieldPolicy = {
	id?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StripeItemKeySpecifier = (
	| 'amount'
	| 'currency'
	| 'description'
	| 'parent'
	| 'quantity'
	| 'type'
	| StripeItemKeySpecifier
)[]
export type StripeItemFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
	quantity?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StripeProductKeySpecifier = (
	| 'active'
	| 'created'
	| 'default_price'
	| 'deleted'
	| 'description'
	| 'id'
	| 'images'
	| 'livemode'
	| 'marketing_features'
	| 'metadata'
	| 'name'
	| 'object'
	| 'package_dimensions'
	| 'shippable'
	| 'statement_descriptor'
	| 'tax_code'
	| 'type'
	| 'unit_label'
	| 'updated'
	| 'url'
	| StripeProductKeySpecifier
)[]
export type StripeProductFieldPolicy = {
	active?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	default_price?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	images?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	marketing_features?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	package_dimensions?: FieldPolicy<any> | FieldReadFunction<any>
	shippable?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
	tax_code?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	unit_label?: FieldPolicy<any> | FieldReadFunction<any>
	updated?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StripeReviewKeySpecifier = (
	| 'billing_zip'
	| 'charge'
	| 'closed_reason'
	| 'created'
	| 'id'
	| 'ip_address'
	| 'ip_address_location'
	| 'livemode'
	| 'object'
	| 'open'
	| 'opened_reason'
	| 'payment_intent'
	| 'reason'
	| 'session'
	| StripeReviewKeySpecifier
)[]
export type StripeReviewFieldPolicy = {
	billing_zip?: FieldPolicy<any> | FieldReadFunction<any>
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	closed_reason?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	ip_address?: FieldPolicy<any> | FieldReadFunction<any>
	ip_address_location?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	open?: FieldPolicy<any> | FieldReadFunction<any>
	opened_reason?: FieldPolicy<any> | FieldReadFunction<any>
	payment_intent?: FieldPolicy<any> | FieldReadFunction<any>
	reason?: FieldPolicy<any> | FieldReadFunction<any>
	session?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StripeSubscriptionKeySpecifier = (
	| 'application'
	| 'application_fee_percent'
	| 'automatic_tax'
	| 'billing_cycle_anchor'
	| 'billing_cycle_anchor_config'
	| 'billing_thresholds'
	| 'cancel_at'
	| 'cancel_at_period_end'
	| 'canceled_at'
	| 'cancellation_details'
	| 'collection_method'
	| 'created'
	| 'currency'
	| 'current_period_end'
	| 'current_period_start'
	| 'customer'
	| 'days_until_due'
	| 'default_payment_method'
	| 'default_source'
	| 'default_tax_rates'
	| 'description'
	| 'discount'
	| 'discounts'
	| 'ended_at'
	| 'id'
	| 'invoice_settings'
	| 'items'
	| 'latest_invoice'
	| 'livemode'
	| 'metadata'
	| 'next_pending_invoice_item_invoice'
	| 'object'
	| 'on_behalf_of'
	| 'pause_collection'
	| 'payment_settings'
	| 'pending_invoice_item_interval'
	| 'pending_setup_intent'
	| 'pending_update'
	| 'schedule'
	| 'start_date'
	| 'status'
	| 'test_clock'
	| 'transfer_data'
	| 'trial_end'
	| 'trial_settings'
	| 'trial_start'
	| StripeSubscriptionKeySpecifier
)[]
export type StripeSubscriptionFieldPolicy = {
	application?: FieldPolicy<any> | FieldReadFunction<any>
	application_fee_percent?: FieldPolicy<any> | FieldReadFunction<any>
	automatic_tax?: FieldPolicy<any> | FieldReadFunction<any>
	billing_cycle_anchor?: FieldPolicy<any> | FieldReadFunction<any>
	billing_cycle_anchor_config?: FieldPolicy<any> | FieldReadFunction<any>
	billing_thresholds?: FieldPolicy<any> | FieldReadFunction<any>
	cancel_at?: FieldPolicy<any> | FieldReadFunction<any>
	cancel_at_period_end?: FieldPolicy<any> | FieldReadFunction<any>
	canceled_at?: FieldPolicy<any> | FieldReadFunction<any>
	cancellation_details?: FieldPolicy<any> | FieldReadFunction<any>
	collection_method?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	current_period_end?: FieldPolicy<any> | FieldReadFunction<any>
	current_period_start?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	days_until_due?: FieldPolicy<any> | FieldReadFunction<any>
	default_payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	default_source?: FieldPolicy<any> | FieldReadFunction<any>
	default_tax_rates?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	discount?: FieldPolicy<any> | FieldReadFunction<any>
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	ended_at?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_settings?: FieldPolicy<any> | FieldReadFunction<any>
	items?: FieldPolicy<any> | FieldReadFunction<any>
	latest_invoice?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	next_pending_invoice_item_invoice?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	on_behalf_of?: FieldPolicy<any> | FieldReadFunction<any>
	pause_collection?: FieldPolicy<any> | FieldReadFunction<any>
	payment_settings?: FieldPolicy<any> | FieldReadFunction<any>
	pending_invoice_item_interval?: FieldPolicy<any> | FieldReadFunction<any>
	pending_setup_intent?: FieldPolicy<any> | FieldReadFunction<any>
	pending_update?: FieldPolicy<any> | FieldReadFunction<any>
	schedule?: FieldPolicy<any> | FieldReadFunction<any>
	start_date?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	test_clock?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_data?: FieldPolicy<any> | FieldReadFunction<any>
	trial_end?: FieldPolicy<any> | FieldReadFunction<any>
	trial_settings?: FieldPolicy<any> | FieldReadFunction<any>
	trial_start?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionAutomaticTaxKeySpecifier = (
	| 'disabled_reason'
	| 'enabled'
	| 'liability'
	| SubscriptionAutomaticTaxKeySpecifier
)[]
export type SubscriptionAutomaticTaxFieldPolicy = {
	disabled_reason?: FieldPolicy<any> | FieldReadFunction<any>
	enabled?: FieldPolicy<any> | FieldReadFunction<any>
	liability?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionCancellationDetailsKeySpecifier = (
	| 'comment'
	| 'feedback'
	| 'reason'
	| SubscriptionCancellationDetailsKeySpecifier
)[]
export type SubscriptionCancellationDetailsFieldPolicy = {
	comment?: FieldPolicy<any> | FieldReadFunction<any>
	feedback?: FieldPolicy<any> | FieldReadFunction<any>
	reason?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionDataKeySpecifier = (
	| 'description'
	| 'effective_date'
	| 'metadata'
	| 'trial_period_days'
	| SubscriptionDataKeySpecifier
)[]
export type SubscriptionDataFieldPolicy = {
	description?: FieldPolicy<any> | FieldReadFunction<any>
	effective_date?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	trial_period_days?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionDetailsKeySpecifier = ('metadata' | SubscriptionDetailsKeySpecifier)[]
export type SubscriptionDetailsFieldPolicy = {
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionInvoiceSettingsKeySpecifier = (
	| 'account_tax_ids'
	| 'issuer'
	| SubscriptionInvoiceSettingsKeySpecifier
)[]
export type SubscriptionInvoiceSettingsFieldPolicy = {
	account_tax_ids?: FieldPolicy<any> | FieldReadFunction<any>
	issuer?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionItemKeySpecifier = (
	| 'billing_thresholds'
	| 'created'
	| 'deleted'
	| 'discounts'
	| 'id'
	| 'metadata'
	| 'object'
	| 'plan'
	| 'price'
	| 'quantity'
	| 'subscription'
	| 'tax_rates'
	| SubscriptionItemKeySpecifier
)[]
export type SubscriptionItemFieldPolicy = {
	billing_thresholds?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	plan?: FieldPolicy<any> | FieldReadFunction<any>
	price?: FieldPolicy<any> | FieldReadFunction<any>
	quantity?: FieldPolicy<any> | FieldReadFunction<any>
	subscription?: FieldPolicy<any> | FieldReadFunction<any>
	tax_rates?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionItemListKeySpecifier = (
	| 'data'
	| 'has_more'
	| 'object'
	| 'url'
	| SubscriptionItemListKeySpecifier
)[]
export type SubscriptionItemListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionListKeySpecifier = ('data' | 'has_more' | 'object' | 'url' | SubscriptionListKeySpecifier)[]
export type SubscriptionListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionScheduleKeySpecifier = (
	| 'application'
	| 'canceled_at'
	| 'completed_at'
	| 'created'
	| 'current_phase'
	| 'customer'
	| 'default_settings'
	| 'end_behavior'
	| 'id'
	| 'livemode'
	| 'metadata'
	| 'object'
	| 'phases'
	| 'released_at'
	| 'released_subscription'
	| 'status'
	| 'subscription'
	| 'test_clock'
	| SubscriptionScheduleKeySpecifier
)[]
export type SubscriptionScheduleFieldPolicy = {
	application?: FieldPolicy<any> | FieldReadFunction<any>
	canceled_at?: FieldPolicy<any> | FieldReadFunction<any>
	completed_at?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	current_phase?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	default_settings?: FieldPolicy<any> | FieldReadFunction<any>
	end_behavior?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	phases?: FieldPolicy<any> | FieldReadFunction<any>
	released_at?: FieldPolicy<any> | FieldReadFunction<any>
	released_subscription?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	subscription?: FieldPolicy<any> | FieldReadFunction<any>
	test_clock?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionSearchResultsKeySpecifier = (
	| 'data'
	| 'has_more'
	| 'next_page'
	| 'object'
	| 'total_count'
	| 'url'
	| SubscriptionSearchResultsKeySpecifier
)[]
export type SubscriptionSearchResultsFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	next_page?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	total_count?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionTransferDataKeySpecifier = (
	| 'amount_percent'
	| 'destination'
	| SubscriptionTransferDataKeySpecifier
)[]
export type SubscriptionTransferDataFieldPolicy = {
	amount_percent?: FieldPolicy<any> | FieldReadFunction<any>
	destination?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionTrialSettingsKeySpecifier = ('end_behavior' | SubscriptionTrialSettingsKeySpecifier)[]
export type SubscriptionTrialSettingsFieldPolicy = {
	end_behavior?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionTrialSettingsEndBehaviorKeySpecifier = (
	| 'missing_payment_method'
	| SubscriptionTrialSettingsEndBehaviorKeySpecifier
)[]
export type SubscriptionTrialSettingsEndBehaviorFieldPolicy = {
	missing_payment_method?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TagKeySpecifier = (
	| '_id'
	| 'chip'
	| 'createdAt'
	| 'deletedAt'
	| 'disabled'
	| 'disabledReason'
	| 'epc'
	| 'labels'
	| 'organization'
	| 'organizationId'
	| 'perks'
	| 'product'
	| 'productDetails'
	| 'productId'
	| 'program'
	| 'programId'
	| 'registeredAt'
	| 'updatedAt'
	| 'user'
	| 'userId'
	| TagKeySpecifier
)[]
export type TagFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	chip?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	disabled?: FieldPolicy<any> | FieldReadFunction<any>
	disabledReason?: FieldPolicy<any> | FieldReadFunction<any>
	epc?: FieldPolicy<any> | FieldReadFunction<any>
	labels?: FieldPolicy<any> | FieldReadFunction<any>
	organization?: FieldPolicy<any> | FieldReadFunction<any>
	organizationId?: FieldPolicy<any> | FieldReadFunction<any>
	perks?: FieldPolicy<any> | FieldReadFunction<any>
	product?: FieldPolicy<any> | FieldReadFunction<any>
	productDetails?: FieldPolicy<any> | FieldReadFunction<any>
	productId?: FieldPolicy<any> | FieldReadFunction<any>
	program?: FieldPolicy<any> | FieldReadFunction<any>
	programId?: FieldPolicy<any> | FieldReadFunction<any>
	registeredAt?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	user?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TapKeySpecifier = ('sessionPassword' | 'tag' | TapKeySpecifier)[]
export type TapFieldPolicy = {
	sessionPassword?: FieldPolicy<any> | FieldReadFunction<any>
	tag?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TaxKeySpecifier = ('automatic_tax' | 'ip_address' | 'location' | TaxKeySpecifier)[]
export type TaxFieldPolicy = {
	automatic_tax?: FieldPolicy<any> | FieldReadFunction<any>
	ip_address?: FieldPolicy<any> | FieldReadFunction<any>
	location?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TaxAmountKeySpecifier = (
	| 'amount'
	| 'inclusive'
	| 'tax_rate'
	| 'taxability_reason'
	| 'taxable_amount'
	| TaxAmountKeySpecifier
)[]
export type TaxAmountFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	inclusive?: FieldPolicy<any> | FieldReadFunction<any>
	tax_rate?: FieldPolicy<any> | FieldReadFunction<any>
	taxability_reason?: FieldPolicy<any> | FieldReadFunction<any>
	taxable_amount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TaxCodeKeySpecifier = ('description' | 'id' | 'name' | 'object' | TaxCodeKeySpecifier)[]
export type TaxCodeFieldPolicy = {
	description?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TaxDeductedAtSourceKeySpecifier = (
	| 'id'
	| 'object'
	| 'period_end'
	| 'period_start'
	| 'tax_deduction_account_number'
	| TaxDeductedAtSourceKeySpecifier
)[]
export type TaxDeductedAtSourceFieldPolicy = {
	id?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	period_end?: FieldPolicy<any> | FieldReadFunction<any>
	period_start?: FieldPolicy<any> | FieldReadFunction<any>
	tax_deduction_account_number?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TaxIdKeySpecifier = (
	| 'country'
	| 'created'
	| 'customer'
	| 'id'
	| 'livemode'
	| 'object'
	| 'type'
	| 'value'
	| 'verification'
	| TaxIdKeySpecifier
)[]
export type TaxIdFieldPolicy = {
	country?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	value?: FieldPolicy<any> | FieldReadFunction<any>
	verification?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TaxIdListKeySpecifier = ('data' | 'has_more' | 'object' | 'url' | TaxIdListKeySpecifier)[]
export type TaxIdListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TaxLocationKeySpecifier = ('country' | 'source' | 'state' | TaxLocationKeySpecifier)[]
export type TaxLocationFieldPolicy = {
	country?: FieldPolicy<any> | FieldReadFunction<any>
	source?: FieldPolicy<any> | FieldReadFunction<any>
	state?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TaxRateKeySpecifier = (
	| 'active'
	| 'country'
	| 'created'
	| 'description'
	| 'display_name'
	| 'effective_percentage'
	| 'flat_amount'
	| 'id'
	| 'inclusive'
	| 'jurisdiction'
	| 'jurisdiction_level'
	| 'livemode'
	| 'metadata'
	| 'object'
	| 'percentage'
	| 'rate_type'
	| 'state'
	| 'tax_type'
	| TaxRateKeySpecifier
)[]
export type TaxRateFieldPolicy = {
	active?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	display_name?: FieldPolicy<any> | FieldReadFunction<any>
	effective_percentage?: FieldPolicy<any> | FieldReadFunction<any>
	flat_amount?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	inclusive?: FieldPolicy<any> | FieldReadFunction<any>
	jurisdiction?: FieldPolicy<any> | FieldReadFunction<any>
	jurisdiction_level?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	percentage?: FieldPolicy<any> | FieldReadFunction<any>
	rate_type?: FieldPolicy<any> | FieldReadFunction<any>
	state?: FieldPolicy<any> | FieldReadFunction<any>
	tax_type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TestClockKeySpecifier = (
	| 'created'
	| 'deleted'
	| 'deletes_after'
	| 'frozen_time'
	| 'id'
	| 'livemode'
	| 'name'
	| 'object'
	| 'status'
	| 'status_details'
	| TestClockKeySpecifier
)[]
export type TestClockFieldPolicy = {
	created?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	deletes_after?: FieldPolicy<any> | FieldReadFunction<any>
	frozen_time?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	status_details?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ThreeDSecureKeySpecifier = (
	| 'address_line1_check'
	| 'address_zip_check'
	| 'authenticated'
	| 'brand'
	| 'card'
	| 'country'
	| 'customer'
	| 'cvc_check'
	| 'description'
	| 'dynamic_last4'
	| 'exp_month'
	| 'exp_year'
	| 'fingerprint'
	| 'funding'
	| 'iin'
	| 'issuer'
	| 'last4'
	| 'name'
	| 'three_d_secure'
	| 'tokenization_method'
	| ThreeDSecureKeySpecifier
)[]
export type ThreeDSecureFieldPolicy = {
	address_line1_check?: FieldPolicy<any> | FieldReadFunction<any>
	address_zip_check?: FieldPolicy<any> | FieldReadFunction<any>
	authenticated?: FieldPolicy<any> | FieldReadFunction<any>
	brand?: FieldPolicy<any> | FieldReadFunction<any>
	card?: FieldPolicy<any> | FieldReadFunction<any>
	country?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	cvc_check?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	dynamic_last4?: FieldPolicy<any> | FieldReadFunction<any>
	exp_month?: FieldPolicy<any> | FieldReadFunction<any>
	exp_year?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	funding?: FieldPolicy<any> | FieldReadFunction<any>
	iin?: FieldPolicy<any> | FieldReadFunction<any>
	issuer?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	three_d_secure?: FieldPolicy<any> | FieldReadFunction<any>
	tokenization_method?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ThresholdReasonKeySpecifier = ('amount_gte' | 'item_reasons' | ThresholdReasonKeySpecifier)[]
export type ThresholdReasonFieldPolicy = {
	amount_gte?: FieldPolicy<any> | FieldReadFunction<any>
	item_reasons?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ThresholdReasonItemReasonKeySpecifier = (
	| 'line_item_ids'
	| 'usage_gte'
	| ThresholdReasonItemReasonKeySpecifier
)[]
export type ThresholdReasonItemReasonFieldPolicy = {
	line_item_ids?: FieldPolicy<any> | FieldReadFunction<any>
	usage_gte?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TierKeySpecifier = (
	| 'flat_amount'
	| 'flat_amount_decimal'
	| 'unit_amount'
	| 'unit_amount_decimal'
	| 'up_to'
	| TierKeySpecifier
)[]
export type TierFieldPolicy = {
	flat_amount?: FieldPolicy<any> | FieldReadFunction<any>
	flat_amount_decimal?: FieldPolicy<any> | FieldReadFunction<any>
	unit_amount?: FieldPolicy<any> | FieldReadFunction<any>
	unit_amount_decimal?: FieldPolicy<any> | FieldReadFunction<any>
	up_to?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TopupKeySpecifier = (
	| 'amount'
	| 'balance_transaction'
	| 'created'
	| 'currency'
	| 'description'
	| 'expected_availability_date'
	| 'failure_code'
	| 'failure_message'
	| 'id'
	| 'livemode'
	| 'metadata'
	| 'object'
	| 'source'
	| 'statement_descriptor'
	| 'status'
	| 'transfer_group'
	| TopupKeySpecifier
)[]
export type TopupFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	expected_availability_date?: FieldPolicy<any> | FieldReadFunction<any>
	failure_code?: FieldPolicy<any> | FieldReadFunction<any>
	failure_message?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	source?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_group?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TosAcceptanceKeySpecifier = (
	| 'date'
	| 'ip'
	| 'service_agreement'
	| 'user_agent'
	| TosAcceptanceKeySpecifier
)[]
export type TosAcceptanceFieldPolicy = {
	date?: FieldPolicy<any> | FieldReadFunction<any>
	ip?: FieldPolicy<any> | FieldReadFunction<any>
	service_agreement?: FieldPolicy<any> | FieldReadFunction<any>
	user_agent?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TotalDetailsKeySpecifier = (
	| 'amount_discount'
	| 'amount_shipping'
	| 'amount_tax'
	| 'breakdown'
	| TotalDetailsKeySpecifier
)[]
export type TotalDetailsFieldPolicy = {
	amount_discount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_shipping?: FieldPolicy<any> | FieldReadFunction<any>
	amount_tax?: FieldPolicy<any> | FieldReadFunction<any>
	breakdown?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TotalDiscountAmountKeySpecifier = ('amount' | 'discount' | TotalDiscountAmountKeySpecifier)[]
export type TotalDiscountAmountFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	discount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TotalTaxAmountKeySpecifier = (
	| 'amount'
	| 'inclusive'
	| 'tax_rate'
	| 'taxability_reason'
	| 'taxable_amount'
	| TotalTaxAmountKeySpecifier
)[]
export type TotalTaxAmountFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	inclusive?: FieldPolicy<any> | FieldReadFunction<any>
	tax_rate?: FieldPolicy<any> | FieldReadFunction<any>
	taxability_reason?: FieldPolicy<any> | FieldReadFunction<any>
	taxable_amount?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TraceIdKeySpecifier = ('status' | 'value' | TraceIdKeySpecifier)[]
export type TraceIdFieldPolicy = {
	status?: FieldPolicy<any> | FieldReadFunction<any>
	value?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TransferKeySpecifier = (
	| 'amount'
	| 'amount_reversed'
	| 'balance_transaction'
	| 'created'
	| 'currency'
	| 'description'
	| 'destination'
	| 'destination_payment'
	| 'id'
	| 'livemode'
	| 'metadata'
	| 'object'
	| 'reversals'
	| 'reversed'
	| 'source_transaction'
	| 'source_type'
	| 'transfer_group'
	| TransferKeySpecifier
)[]
export type TransferFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	amount_reversed?: FieldPolicy<any> | FieldReadFunction<any>
	balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	destination?: FieldPolicy<any> | FieldReadFunction<any>
	destination_payment?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	reversals?: FieldPolicy<any> | FieldReadFunction<any>
	reversed?: FieldPolicy<any> | FieldReadFunction<any>
	source_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	source_type?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_group?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TransferDataKeySpecifier = ('amount' | 'destination' | TransferDataKeySpecifier)[]
export type TransferDataFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	destination?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TransferReversalKeySpecifier = (
	| 'amount'
	| 'balance_transaction'
	| 'created'
	| 'currency'
	| 'destination_payment_refund'
	| 'id'
	| 'metadata'
	| 'object'
	| 'source_refund'
	| 'transfer'
	| TransferReversalKeySpecifier
)[]
export type TransferReversalFieldPolicy = {
	amount?: FieldPolicy<any> | FieldReadFunction<any>
	balance_transaction?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	destination_payment_refund?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	source_refund?: FieldPolicy<any> | FieldReadFunction<any>
	transfer?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TransferReversalListKeySpecifier = (
	| 'data'
	| 'has_more'
	| 'object'
	| 'url'
	| TransferReversalListKeySpecifier
)[]
export type TransferReversalListFieldPolicy = {
	data?: FieldPolicy<any> | FieldReadFunction<any>
	has_more?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TransformQuantityKeySpecifier = ('divide_by' | 'round' | TransformQuantityKeySpecifier)[]
export type TransformQuantityFieldPolicy = {
	divide_by?: FieldPolicy<any> | FieldReadFunction<any>
	round?: FieldPolicy<any> | FieldReadFunction<any>
}
export type TransformUsageKeySpecifier = ('divide_by' | 'round' | TransformUsageKeySpecifier)[]
export type TransformUsageFieldPolicy = {
	divide_by?: FieldPolicy<any> | FieldReadFunction<any>
	round?: FieldPolicy<any> | FieldReadFunction<any>
}
export type UpcomingInvoiceKeySpecifier = (
	| 'account_country'
	| 'account_name'
	| 'account_tax_ids'
	| 'amount_due'
	| 'amount_paid'
	| 'amount_remaining'
	| 'amount_shipping'
	| 'application'
	| 'application_fee_amount'
	| 'attempt_count'
	| 'attempted'
	| 'auto_advance'
	| 'automatic_tax'
	| 'automatically_finalizes_at'
	| 'billing_reason'
	| 'charge'
	| 'collection_method'
	| 'created'
	| 'currency'
	| 'custom_fields'
	| 'customer'
	| 'customer_address'
	| 'customer_email'
	| 'customer_name'
	| 'customer_phone'
	| 'customer_shipping'
	| 'customer_tax_exempt'
	| 'customer_tax_ids'
	| 'default_payment_method'
	| 'default_source'
	| 'default_tax_rates'
	| 'deleted'
	| 'description'
	| 'discount'
	| 'discounts'
	| 'due_date'
	| 'effective_at'
	| 'ending_balance'
	| 'footer'
	| 'from_invoice'
	| 'hosted_invoice_url'
	| 'invoice_pdf'
	| 'issuer'
	| 'last_finalization_error'
	| 'latest_revision'
	| 'lines'
	| 'livemode'
	| 'metadata'
	| 'next_payment_attempt'
	| 'number'
	| 'object'
	| 'on_behalf_of'
	| 'paid'
	| 'paid_out_of_band'
	| 'payment_intent'
	| 'payment_settings'
	| 'period_end'
	| 'period_start'
	| 'post_payment_credit_notes_amount'
	| 'pre_payment_credit_notes_amount'
	| 'quote'
	| 'receipt_number'
	| 'rendering'
	| 'shipping_cost'
	| 'shipping_details'
	| 'starting_balance'
	| 'statement_descriptor'
	| 'status'
	| 'status_transitions'
	| 'subscription'
	| 'subscription_details'
	| 'subscription_proration_date'
	| 'subtotal'
	| 'subtotal_excluding_tax'
	| 'tax'
	| 'test_clock'
	| 'threshold_reason'
	| 'total'
	| 'total_discount_amounts'
	| 'total_excluding_tax'
	| 'total_pretax_credit_amounts'
	| 'total_tax_amounts'
	| 'transfer_data'
	| 'webhooks_delivered_at'
	| UpcomingInvoiceKeySpecifier
)[]
export type UpcomingInvoiceFieldPolicy = {
	account_country?: FieldPolicy<any> | FieldReadFunction<any>
	account_name?: FieldPolicy<any> | FieldReadFunction<any>
	account_tax_ids?: FieldPolicy<any> | FieldReadFunction<any>
	amount_due?: FieldPolicy<any> | FieldReadFunction<any>
	amount_paid?: FieldPolicy<any> | FieldReadFunction<any>
	amount_remaining?: FieldPolicy<any> | FieldReadFunction<any>
	amount_shipping?: FieldPolicy<any> | FieldReadFunction<any>
	application?: FieldPolicy<any> | FieldReadFunction<any>
	application_fee_amount?: FieldPolicy<any> | FieldReadFunction<any>
	attempt_count?: FieldPolicy<any> | FieldReadFunction<any>
	attempted?: FieldPolicy<any> | FieldReadFunction<any>
	auto_advance?: FieldPolicy<any> | FieldReadFunction<any>
	automatic_tax?: FieldPolicy<any> | FieldReadFunction<any>
	automatically_finalizes_at?: FieldPolicy<any> | FieldReadFunction<any>
	billing_reason?: FieldPolicy<any> | FieldReadFunction<any>
	charge?: FieldPolicy<any> | FieldReadFunction<any>
	collection_method?: FieldPolicy<any> | FieldReadFunction<any>
	created?: FieldPolicy<any> | FieldReadFunction<any>
	currency?: FieldPolicy<any> | FieldReadFunction<any>
	custom_fields?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	customer_address?: FieldPolicy<any> | FieldReadFunction<any>
	customer_email?: FieldPolicy<any> | FieldReadFunction<any>
	customer_name?: FieldPolicy<any> | FieldReadFunction<any>
	customer_phone?: FieldPolicy<any> | FieldReadFunction<any>
	customer_shipping?: FieldPolicy<any> | FieldReadFunction<any>
	customer_tax_exempt?: FieldPolicy<any> | FieldReadFunction<any>
	customer_tax_ids?: FieldPolicy<any> | FieldReadFunction<any>
	default_payment_method?: FieldPolicy<any> | FieldReadFunction<any>
	default_source?: FieldPolicy<any> | FieldReadFunction<any>
	default_tax_rates?: FieldPolicy<any> | FieldReadFunction<any>
	deleted?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	discount?: FieldPolicy<any> | FieldReadFunction<any>
	discounts?: FieldPolicy<any> | FieldReadFunction<any>
	due_date?: FieldPolicy<any> | FieldReadFunction<any>
	effective_at?: FieldPolicy<any> | FieldReadFunction<any>
	ending_balance?: FieldPolicy<any> | FieldReadFunction<any>
	footer?: FieldPolicy<any> | FieldReadFunction<any>
	from_invoice?: FieldPolicy<any> | FieldReadFunction<any>
	hosted_invoice_url?: FieldPolicy<any> | FieldReadFunction<any>
	invoice_pdf?: FieldPolicy<any> | FieldReadFunction<any>
	issuer?: FieldPolicy<any> | FieldReadFunction<any>
	last_finalization_error?: FieldPolicy<any> | FieldReadFunction<any>
	latest_revision?: FieldPolicy<any> | FieldReadFunction<any>
	lines?: FieldPolicy<any> | FieldReadFunction<any>
	livemode?: FieldPolicy<any> | FieldReadFunction<any>
	metadata?: FieldPolicy<any> | FieldReadFunction<any>
	next_payment_attempt?: FieldPolicy<any> | FieldReadFunction<any>
	number?: FieldPolicy<any> | FieldReadFunction<any>
	object?: FieldPolicy<any> | FieldReadFunction<any>
	on_behalf_of?: FieldPolicy<any> | FieldReadFunction<any>
	paid?: FieldPolicy<any> | FieldReadFunction<any>
	paid_out_of_band?: FieldPolicy<any> | FieldReadFunction<any>
	payment_intent?: FieldPolicy<any> | FieldReadFunction<any>
	payment_settings?: FieldPolicy<any> | FieldReadFunction<any>
	period_end?: FieldPolicy<any> | FieldReadFunction<any>
	period_start?: FieldPolicy<any> | FieldReadFunction<any>
	post_payment_credit_notes_amount?: FieldPolicy<any> | FieldReadFunction<any>
	pre_payment_credit_notes_amount?: FieldPolicy<any> | FieldReadFunction<any>
	quote?: FieldPolicy<any> | FieldReadFunction<any>
	receipt_number?: FieldPolicy<any> | FieldReadFunction<any>
	rendering?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_cost?: FieldPolicy<any> | FieldReadFunction<any>
	shipping_details?: FieldPolicy<any> | FieldReadFunction<any>
	starting_balance?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
	status?: FieldPolicy<any> | FieldReadFunction<any>
	status_transitions?: FieldPolicy<any> | FieldReadFunction<any>
	subscription?: FieldPolicy<any> | FieldReadFunction<any>
	subscription_details?: FieldPolicy<any> | FieldReadFunction<any>
	subscription_proration_date?: FieldPolicy<any> | FieldReadFunction<any>
	subtotal?: FieldPolicy<any> | FieldReadFunction<any>
	subtotal_excluding_tax?: FieldPolicy<any> | FieldReadFunction<any>
	tax?: FieldPolicy<any> | FieldReadFunction<any>
	test_clock?: FieldPolicy<any> | FieldReadFunction<any>
	threshold_reason?: FieldPolicy<any> | FieldReadFunction<any>
	total?: FieldPolicy<any> | FieldReadFunction<any>
	total_discount_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	total_excluding_tax?: FieldPolicy<any> | FieldReadFunction<any>
	total_pretax_credit_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	total_tax_amounts?: FieldPolicy<any> | FieldReadFunction<any>
	transfer_data?: FieldPolicy<any> | FieldReadFunction<any>
	webhooks_delivered_at?: FieldPolicy<any> | FieldReadFunction<any>
}
export type UsBankAccountKeySpecifier = (
	| 'account_holder_type'
	| 'account_type'
	| 'bank_name'
	| 'financial_connections_account'
	| 'fingerprint'
	| 'last4'
	| 'networks'
	| 'routing_number'
	| 'status_details'
	| UsBankAccountKeySpecifier
)[]
export type UsBankAccountFieldPolicy = {
	account_holder_type?: FieldPolicy<any> | FieldReadFunction<any>
	account_type?: FieldPolicy<any> | FieldReadFunction<any>
	bank_name?: FieldPolicy<any> | FieldReadFunction<any>
	financial_connections_account?: FieldPolicy<any> | FieldReadFunction<any>
	fingerprint?: FieldPolicy<any> | FieldReadFunction<any>
	last4?: FieldPolicy<any> | FieldReadFunction<any>
	networks?: FieldPolicy<any> | FieldReadFunction<any>
	routing_number?: FieldPolicy<any> | FieldReadFunction<any>
	status_details?: FieldPolicy<any> | FieldReadFunction<any>
}
export type UsBankAccountNetworksKeySpecifier = ('preferred' | 'supported' | UsBankAccountNetworksKeySpecifier)[]
export type UsBankAccountNetworksFieldPolicy = {
	preferred?: FieldPolicy<any> | FieldReadFunction<any>
	supported?: FieldPolicy<any> | FieldReadFunction<any>
}
export type UsBankAccountStatusDetailsKeySpecifier = ('blocked' | UsBankAccountStatusDetailsKeySpecifier)[]
export type UsBankAccountStatusDetailsFieldPolicy = {
	blocked?: FieldPolicy<any> | FieldReadFunction<any>
}
export type UsBankAccountStatusDetailsBlockedKeySpecifier = (
	| 'network_code'
	| 'reason'
	| UsBankAccountStatusDetailsBlockedKeySpecifier
)[]
export type UsBankAccountStatusDetailsBlockedFieldPolicy = {
	network_code?: FieldPolicy<any> | FieldReadFunction<any>
	reason?: FieldPolicy<any> | FieldReadFunction<any>
}
export type UserKeySpecifier = (
	| '_id'
	| 'additionalEmail'
	| 'additionalEmailVerified'
	| 'address'
	| 'birthdate'
	| 'blocked'
	| 'blockedIds'
	| 'contactEmail'
	| 'contactEmailVerified'
	| 'createdAt'
	| 'customer'
	| 'customerId'
	| 'deletedAt'
	| 'email'
	| 'emailVerified'
	| 'familyName'
	| 'followIds'
	| 'followers'
	| 'following'
	| 'friendRequests'
	| 'friends'
	| 'gender'
	| 'givenName'
	| 'identities'
	| 'installations'
	| 'isBlocked'
	| 'isFollower'
	| 'isFollowing'
	| 'isFriend'
	| 'locale'
	| 'middleName'
	| 'name'
	| 'nickname'
	| 'organizations'
	| 'phoneNumber'
	| 'phoneNumberVerified'
	| 'picture'
	| 'recoveryEmail'
	| 'recoveryEmailVerified'
	| 'roles'
	| 'specialBadge'
	| 'tags'
	| 'updatedAt'
	| 'website'
	| 'zoneinfo'
	| UserKeySpecifier
)[]
export type UserFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	additionalEmail?: FieldPolicy<any> | FieldReadFunction<any>
	additionalEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	address?: FieldPolicy<any> | FieldReadFunction<any>
	birthdate?: FieldPolicy<any> | FieldReadFunction<any>
	blocked?: FieldPolicy<any> | FieldReadFunction<any>
	blockedIds?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmail?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	customer?: FieldPolicy<any> | FieldReadFunction<any>
	customerId?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	emailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	familyName?: FieldPolicy<any> | FieldReadFunction<any>
	followIds?: FieldPolicy<any> | FieldReadFunction<any>
	followers?: FieldPolicy<any> | FieldReadFunction<any>
	following?: FieldPolicy<any> | FieldReadFunction<any>
	friendRequests?: FieldPolicy<any> | FieldReadFunction<any>
	friends?: FieldPolicy<any> | FieldReadFunction<any>
	gender?: FieldPolicy<any> | FieldReadFunction<any>
	givenName?: FieldPolicy<any> | FieldReadFunction<any>
	identities?: FieldPolicy<any> | FieldReadFunction<any>
	installations?: FieldPolicy<any> | FieldReadFunction<any>
	isBlocked?: FieldPolicy<any> | FieldReadFunction<any>
	isFollower?: FieldPolicy<any> | FieldReadFunction<any>
	isFollowing?: FieldPolicy<any> | FieldReadFunction<any>
	isFriend?: FieldPolicy<any> | FieldReadFunction<any>
	locale?: FieldPolicy<any> | FieldReadFunction<any>
	middleName?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	nickname?: FieldPolicy<any> | FieldReadFunction<any>
	organizations?: FieldPolicy<any> | FieldReadFunction<any>
	phoneNumber?: FieldPolicy<any> | FieldReadFunction<any>
	phoneNumberVerified?: FieldPolicy<any> | FieldReadFunction<any>
	picture?: FieldPolicy<any> | FieldReadFunction<any>
	recoveryEmail?: FieldPolicy<any> | FieldReadFunction<any>
	recoveryEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	roles?: FieldPolicy<any> | FieldReadFunction<any>
	specialBadge?: FieldPolicy<any> | FieldReadFunction<any>
	tags?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	website?: FieldPolicy<any> | FieldReadFunction<any>
	zoneinfo?: FieldPolicy<any> | FieldReadFunction<any>
}
export type UserWithRoleKeySpecifier = (
	| '_id'
	| 'additionalEmail'
	| 'additionalEmailVerified'
	| 'address'
	| 'birthdate'
	| 'blocked'
	| 'blockedIds'
	| 'contactEmail'
	| 'contactEmailVerified'
	| 'createdAt'
	| 'deletedAt'
	| 'email'
	| 'emailVerified'
	| 'familyName'
	| 'followIds'
	| 'followers'
	| 'following'
	| 'friendRequests'
	| 'friends'
	| 'gender'
	| 'givenName'
	| 'identities'
	| 'installations'
	| 'isBlocked'
	| 'isFollower'
	| 'isFollowing'
	| 'isFriend'
	| 'locale'
	| 'middleName'
	| 'name'
	| 'nickname'
	| 'organizations'
	| 'phoneNumber'
	| 'phoneNumberVerified'
	| 'picture'
	| 'recoveryEmail'
	| 'recoveryEmailVerified'
	| 'role'
	| 'roles'
	| 'specialBadge'
	| 'updatedAt'
	| 'website'
	| 'zoneinfo'
	| UserWithRoleKeySpecifier
)[]
export type UserWithRoleFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	additionalEmail?: FieldPolicy<any> | FieldReadFunction<any>
	additionalEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	address?: FieldPolicy<any> | FieldReadFunction<any>
	birthdate?: FieldPolicy<any> | FieldReadFunction<any>
	blocked?: FieldPolicy<any> | FieldReadFunction<any>
	blockedIds?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmail?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	emailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	familyName?: FieldPolicy<any> | FieldReadFunction<any>
	followIds?: FieldPolicy<any> | FieldReadFunction<any>
	followers?: FieldPolicy<any> | FieldReadFunction<any>
	following?: FieldPolicy<any> | FieldReadFunction<any>
	friendRequests?: FieldPolicy<any> | FieldReadFunction<any>
	friends?: FieldPolicy<any> | FieldReadFunction<any>
	gender?: FieldPolicy<any> | FieldReadFunction<any>
	givenName?: FieldPolicy<any> | FieldReadFunction<any>
	identities?: FieldPolicy<any> | FieldReadFunction<any>
	installations?: FieldPolicy<any> | FieldReadFunction<any>
	isBlocked?: FieldPolicy<any> | FieldReadFunction<any>
	isFollower?: FieldPolicy<any> | FieldReadFunction<any>
	isFollowing?: FieldPolicy<any> | FieldReadFunction<any>
	isFriend?: FieldPolicy<any> | FieldReadFunction<any>
	locale?: FieldPolicy<any> | FieldReadFunction<any>
	middleName?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	nickname?: FieldPolicy<any> | FieldReadFunction<any>
	organizations?: FieldPolicy<any> | FieldReadFunction<any>
	phoneNumber?: FieldPolicy<any> | FieldReadFunction<any>
	phoneNumberVerified?: FieldPolicy<any> | FieldReadFunction<any>
	picture?: FieldPolicy<any> | FieldReadFunction<any>
	recoveryEmail?: FieldPolicy<any> | FieldReadFunction<any>
	recoveryEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	role?: FieldPolicy<any> | FieldReadFunction<any>
	roles?: FieldPolicy<any> | FieldReadFunction<any>
	specialBadge?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	website?: FieldPolicy<any> | FieldReadFunction<any>
	zoneinfo?: FieldPolicy<any> | FieldReadFunction<any>
}
export type VerificationKeySpecifier = ('status' | 'verified_address' | 'verified_name' | VerificationKeySpecifier)[]
export type VerificationFieldPolicy = {
	status?: FieldPolicy<any> | FieldReadFunction<any>
	verified_address?: FieldPolicy<any> | FieldReadFunction<any>
	verified_name?: FieldPolicy<any> | FieldReadFunction<any>
}
export type VerificationDocumentKeySpecifier = (
	| 'back'
	| 'details'
	| 'details_code'
	| 'front'
	| VerificationDocumentKeySpecifier
)[]
export type VerificationDocumentFieldPolicy = {
	back?: FieldPolicy<any> | FieldReadFunction<any>
	details?: FieldPolicy<any> | FieldReadFunction<any>
	details_code?: FieldPolicy<any> | FieldReadFunction<any>
	front?: FieldPolicy<any> | FieldReadFunction<any>
}
export type WebhookKeySpecifier = (
	| '_id'
	| 'active'
	| 'createdAt'
	| 'deletedAt'
	| 'parent'
	| 'parentId'
	| 'secret'
	| 'subscribeTo'
	| 'updatedAt'
	| 'url'
	| WebhookKeySpecifier
)[]
export type WebhookFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	active?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
	parentId?: FieldPolicy<any> | FieldReadFunction<any>
	secret?: FieldPolicy<any> | FieldReadFunction<any>
	subscribeTo?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type WechatKeySpecifier = ('prepay_id' | 'qr_code_url' | 'statement_descriptor' | WechatKeySpecifier)[]
export type WechatFieldPolicy = {
	prepay_id?: FieldPolicy<any> | FieldReadFunction<any>
	qr_code_url?: FieldPolicy<any> | FieldReadFunction<any>
	statement_descriptor?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StrictTypedTypePolicies = {
	Account?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AccountKeySpecifier | (() => undefined | AccountKeySpecifier)
		fields?: AccountFieldPolicy
	}
	AccountSettingCardPayments?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| AccountSettingCardPaymentsKeySpecifier
			| (() => undefined | AccountSettingCardPaymentsKeySpecifier)
		fields?: AccountSettingCardPaymentsFieldPolicy
	}
	AccountSettingCardPaymentsDeclineOn?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| AccountSettingCardPaymentsDeclineOnKeySpecifier
			| (() => undefined | AccountSettingCardPaymentsDeclineOnKeySpecifier)
		fields?: AccountSettingCardPaymentsDeclineOnFieldPolicy
	}
	AccountSettingDashboard?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AccountSettingDashboardKeySpecifier | (() => undefined | AccountSettingDashboardKeySpecifier)
		fields?: AccountSettingDashboardFieldPolicy
	}
	AccountSettingPayments?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AccountSettingPaymentsKeySpecifier | (() => undefined | AccountSettingPaymentsKeySpecifier)
		fields?: AccountSettingPaymentsFieldPolicy
	}
	AccountSettingPayouts?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AccountSettingPayoutsKeySpecifier | (() => undefined | AccountSettingPayoutsKeySpecifier)
		fields?: AccountSettingPayoutsFieldPolicy
	}
	AccountSettingPayoutsSchedule?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| AccountSettingPayoutsScheduleKeySpecifier
			| (() => undefined | AccountSettingPayoutsScheduleKeySpecifier)
		fields?: AccountSettingPayoutsScheduleFieldPolicy
	}
	AccountSettingSepaDebitPayments?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| AccountSettingSepaDebitPaymentsKeySpecifier
			| (() => undefined | AccountSettingSepaDebitPaymentsKeySpecifier)
		fields?: AccountSettingSepaDebitPaymentsFieldPolicy
	}
	AccountSettingTreasury?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AccountSettingTreasuryKeySpecifier | (() => undefined | AccountSettingTreasuryKeySpecifier)
		fields?: AccountSettingTreasuryFieldPolicy
	}
	AccountSettings?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AccountSettingsKeySpecifier | (() => undefined | AccountSettingsKeySpecifier)
		fields?: AccountSettingsFieldPolicy
	}
	AccountSettingsBacsDebitPayments?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| AccountSettingsBacsDebitPaymentsKeySpecifier
			| (() => undefined | AccountSettingsBacsDebitPaymentsKeySpecifier)
		fields?: AccountSettingsBacsDebitPaymentsFieldPolicy
	}
	AccountSettingsBranding?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AccountSettingsBrandingKeySpecifier | (() => undefined | AccountSettingsBrandingKeySpecifier)
		fields?: AccountSettingsBrandingFieldPolicy
	}
	AccountSettingsCardIssuing?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| AccountSettingsCardIssuingKeySpecifier
			| (() => undefined | AccountSettingsCardIssuingKeySpecifier)
		fields?: AccountSettingsCardIssuingFieldPolicy
	}
	AccountSettingsCardIssuingTosAcceptance?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| AccountSettingsCardIssuingTosAcceptanceKeySpecifier
			| (() => undefined | AccountSettingsCardIssuingTosAcceptanceKeySpecifier)
		fields?: AccountSettingsCardIssuingTosAcceptanceFieldPolicy
	}
	AchCreditTransfer?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AchCreditTransferKeySpecifier | (() => undefined | AchCreditTransferKeySpecifier)
		fields?: AchCreditTransferFieldPolicy
	}
	AchDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AchDebitKeySpecifier | (() => undefined | AchDebitKeySpecifier)
		fields?: AchDebitFieldPolicy
	}
	AcssDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AcssDebitKeySpecifier | (() => undefined | AcssDebitKeySpecifier)
		fields?: AcssDebitFieldPolicy
	}
	Activation?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ActivationKeySpecifier | (() => undefined | ActivationKeySpecifier)
		fields?: ActivationFieldPolicy
	}
	ActivationFilter?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ActivationFilterKeySpecifier | (() => undefined | ActivationFilterKeySpecifier)
		fields?: ActivationFilterFieldPolicy
	}
	Address?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AddressKeySpecifier | (() => undefined | AddressKeySpecifier)
		fields?: AddressFieldPolicy
	}
	AddressKana?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AddressKanaKeySpecifier | (() => undefined | AddressKanaKeySpecifier)
		fields?: AddressKanaFieldPolicy
	}
	AddressKanji?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AddressKanjiKeySpecifier | (() => undefined | AddressKanjiKeySpecifier)
		fields?: AddressKanjiFieldPolicy
	}
	Admins?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AdminsKeySpecifier | (() => undefined | AdminsKeySpecifier)
		fields?: AdminsFieldPolicy
	}
	Advancing?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AdvancingKeySpecifier | (() => undefined | AdvancingKeySpecifier)
		fields?: AdvancingFieldPolicy
	}
	Amount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AmountKeySpecifier | (() => undefined | AmountKeySpecifier)
		fields?: AmountFieldPolicy
	}
	AmountMonetary?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AmountMonetaryKeySpecifier | (() => undefined | AmountMonetaryKeySpecifier)
		fields?: AmountMonetaryFieldPolicy
	}
	AnnualRevenue?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AnnualRevenueKeySpecifier | (() => undefined | AnnualRevenueKeySpecifier)
		fields?: AnnualRevenueFieldPolicy
	}
	Application?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ApplicationKeySpecifier | (() => undefined | ApplicationKeySpecifier)
		fields?: ApplicationFieldPolicy
	}
	ApplicationFee?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ApplicationFeeKeySpecifier | (() => undefined | ApplicationFeeKeySpecifier)
		fields?: ApplicationFeeFieldPolicy
	}
	AppliesTo?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AppliesToKeySpecifier | (() => undefined | AppliesToKeySpecifier)
		fields?: AppliesToFieldPolicy
	}
	AuBecsDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AuBecsDebitKeySpecifier | (() => undefined | AuBecsDebitKeySpecifier)
		fields?: AuBecsDebitFieldPolicy
	}
	AuditEvent?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AuditEventKeySpecifier | (() => undefined | AuditEventKeySpecifier)
		fields?: AuditEventFieldPolicy
	}
	AutomaticPaymentMethods?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AutomaticPaymentMethodsKeySpecifier | (() => undefined | AutomaticPaymentMethodsKeySpecifier)
		fields?: AutomaticPaymentMethodsFieldPolicy
	}
	BacsDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | BacsDebitKeySpecifier | (() => undefined | BacsDebitKeySpecifier)
		fields?: BacsDebitFieldPolicy
	}
	BalanceTransaction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | BalanceTransactionKeySpecifier | (() => undefined | BalanceTransactionKeySpecifier)
		fields?: BalanceTransactionFieldPolicy
	}
	BankAccount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | BankAccountKeySpecifier | (() => undefined | BankAccountKeySpecifier)
		fields?: BankAccountFieldPolicy
	}
	BillingCycleAnchorConfig?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | BillingCycleAnchorConfigKeySpecifier | (() => undefined | BillingCycleAnchorConfigKeySpecifier)
		fields?: BillingCycleAnchorConfigFieldPolicy
	}
	BillingDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | BillingDetailsKeySpecifier | (() => undefined | BillingDetailsKeySpecifier)
		fields?: BillingDetailsFieldPolicy
	}
	BillingThresholds?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | BillingThresholdsKeySpecifier | (() => undefined | BillingThresholdsKeySpecifier)
		fields?: BillingThresholdsFieldPolicy
	}
	Boleto?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | BoletoKeySpecifier | (() => undefined | BoletoKeySpecifier)
		fields?: BoletoFieldPolicy
	}
	BusinessProfile?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | BusinessProfileKeySpecifier | (() => undefined | BusinessProfileKeySpecifier)
		fields?: BusinessProfileFieldPolicy
	}
	Capabilities?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CapabilitiesKeySpecifier | (() => undefined | CapabilitiesKeySpecifier)
		fields?: CapabilitiesFieldPolicy
	}
	Card?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CardKeySpecifier | (() => undefined | CardKeySpecifier)
		fields?: CardFieldPolicy
	}
	CardChecks?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CardChecksKeySpecifier | (() => undefined | CardChecksKeySpecifier)
		fields?: CardChecksFieldPolicy
	}
	CardNetworks?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CardNetworksKeySpecifier | (() => undefined | CardNetworksKeySpecifier)
		fields?: CardNetworksFieldPolicy
	}
	CardPresent?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CardPresentKeySpecifier | (() => undefined | CardPresentKeySpecifier)
		fields?: CardPresentFieldPolicy
	}
	CardThreeDSecureUsage?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CardThreeDSecureUsageKeySpecifier | (() => undefined | CardThreeDSecureUsageKeySpecifier)
		fields?: CardThreeDSecureUsageFieldPolicy
	}
	CardWallet?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CardWalletKeySpecifier | (() => undefined | CardWalletKeySpecifier)
		fields?: CardWalletFieldPolicy
	}
	CardWalletMasterpass?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CardWalletMasterpassKeySpecifier | (() => undefined | CardWalletMasterpassKeySpecifier)
		fields?: CardWalletMasterpassFieldPolicy
	}
	CardWalletVisaCheckout?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CardWalletVisaCheckoutKeySpecifier | (() => undefined | CardWalletVisaCheckoutKeySpecifier)
		fields?: CardWalletVisaCheckoutFieldPolicy
	}
	CashBalance?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CashBalanceKeySpecifier | (() => undefined | CashBalanceKeySpecifier)
		fields?: CashBalanceFieldPolicy
	}
	CashBalanceSettings?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CashBalanceSettingsKeySpecifier | (() => undefined | CashBalanceSettingsKeySpecifier)
		fields?: CashBalanceSettingsFieldPolicy
	}
	Charge?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ChargeKeySpecifier | (() => undefined | ChargeKeySpecifier)
		fields?: ChargeFieldPolicy
	}
	ChargeTransferData?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ChargeTransferDataKeySpecifier | (() => undefined | ChargeTransferDataKeySpecifier)
		fields?: ChargeTransferDataFieldPolicy
	}
	Chip?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ChipKeySpecifier | (() => undefined | ChipKeySpecifier)
		fields?: ChipFieldPolicy
	}
	CodeVerification?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CodeVerificationKeySpecifier | (() => undefined | CodeVerificationKeySpecifier)
		fields?: CodeVerificationFieldPolicy
	}
	Comment?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CommentKeySpecifier | (() => undefined | CommentKeySpecifier)
		fields?: CommentFieldPolicy
	}
	Company?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CompanyKeySpecifier | (() => undefined | CompanyKeySpecifier)
		fields?: CompanyFieldPolicy
	}
	CompanyVerification?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CompanyVerificationKeySpecifier | (() => undefined | CompanyVerificationKeySpecifier)
		fields?: CompanyVerificationFieldPolicy
	}
	CompanyVerificationDocument?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| CompanyVerificationDocumentKeySpecifier
			| (() => undefined | CompanyVerificationDocumentKeySpecifier)
		fields?: CompanyVerificationDocumentFieldPolicy
	}
	Computed?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ComputedKeySpecifier | (() => undefined | ComputedKeySpecifier)
		fields?: ComputedFieldPolicy
	}
	ComputedBreakdown?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ComputedBreakdownKeySpecifier | (() => undefined | ComputedBreakdownKeySpecifier)
		fields?: ComputedBreakdownFieldPolicy
	}
	ComputedBreakdownDiscount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| ComputedBreakdownDiscountKeySpecifier
			| (() => undefined | ComputedBreakdownDiscountKeySpecifier)
		fields?: ComputedBreakdownDiscountFieldPolicy
	}
	ComputedBreakdownTax?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ComputedBreakdownTaxKeySpecifier | (() => undefined | ComputedBreakdownTaxKeySpecifier)
		fields?: ComputedBreakdownTaxFieldPolicy
	}
	ComputedRecurring?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ComputedRecurringKeySpecifier | (() => undefined | ComputedRecurringKeySpecifier)
		fields?: ComputedRecurringFieldPolicy
	}
	ComputedRecurringTotalDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| ComputedRecurringTotalDetailsKeySpecifier
			| (() => undefined | ComputedRecurringTotalDetailsKeySpecifier)
		fields?: ComputedRecurringTotalDetailsFieldPolicy
	}
	ComputedUpfront?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ComputedUpfrontKeySpecifier | (() => undefined | ComputedUpfrontKeySpecifier)
		fields?: ComputedUpfrontFieldPolicy
	}
	ComputedUpfrontTotalDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| ComputedUpfrontTotalDetailsKeySpecifier
			| (() => undefined | ComputedUpfrontTotalDetailsKeySpecifier)
		fields?: ComputedUpfrontTotalDetailsFieldPolicy
	}
	ConnectCollectionTransfer?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| ConnectCollectionTransferKeySpecifier
			| (() => undefined | ConnectCollectionTransferKeySpecifier)
		fields?: ConnectCollectionTransferFieldPolicy
	}
	Coupon?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CouponKeySpecifier | (() => undefined | CouponKeySpecifier)
		fields?: CouponFieldPolicy
	}
	CreditBalanceTransaction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CreditBalanceTransactionKeySpecifier | (() => undefined | CreditBalanceTransactionKeySpecifier)
		fields?: CreditBalanceTransactionFieldPolicy
	}
	CreditBalanceTransactionCreditCreditsApplicationInvoiceVoided?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| CreditBalanceTransactionCreditCreditsApplicationInvoiceVoidedKeySpecifier
			| (() => undefined | CreditBalanceTransactionCreditCreditsApplicationInvoiceVoidedKeySpecifier)
		fields?: CreditBalanceTransactionCreditCreditsApplicationInvoiceVoidedFieldPolicy
	}
	CreditNote?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CreditNoteKeySpecifier | (() => undefined | CreditNoteKeySpecifier)
		fields?: CreditNoteFieldPolicy
	}
	CreditNoteLineItem?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CreditNoteLineItemKeySpecifier | (() => undefined | CreditNoteLineItemKeySpecifier)
		fields?: CreditNoteLineItemFieldPolicy
	}
	CreditNoteLineItemList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CreditNoteLineItemListKeySpecifier | (() => undefined | CreditNoteLineItemListKeySpecifier)
		fields?: CreditNoteLineItemListFieldPolicy
	}
	CurrencyOptions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CurrencyOptionsKeySpecifier | (() => undefined | CurrencyOptionsKeySpecifier)
		fields?: CurrencyOptionsFieldPolicy
	}
	CurrentPhase?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CurrentPhaseKeySpecifier | (() => undefined | CurrentPhaseKeySpecifier)
		fields?: CurrentPhaseFieldPolicy
	}
	CustomField?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CustomFieldKeySpecifier | (() => undefined | CustomFieldKeySpecifier)
		fields?: CustomFieldFieldPolicy
	}
	CustomUnitAmount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CustomUnitAmountKeySpecifier | (() => undefined | CustomUnitAmountKeySpecifier)
		fields?: CustomUnitAmountFieldPolicy
	}
	Customer?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CustomerKeySpecifier | (() => undefined | CustomerKeySpecifier)
		fields?: CustomerFieldPolicy
	}
	CustomerBalanceTransaction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| CustomerBalanceTransactionKeySpecifier
			| (() => undefined | CustomerBalanceTransactionKeySpecifier)
		fields?: CustomerBalanceTransactionFieldPolicy
	}
	CustomerBalanceTransactionList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| CustomerBalanceTransactionListKeySpecifier
			| (() => undefined | CustomerBalanceTransactionListKeySpecifier)
		fields?: CustomerBalanceTransactionListFieldPolicy
	}
	CustomerList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CustomerListKeySpecifier | (() => undefined | CustomerListKeySpecifier)
		fields?: CustomerListFieldPolicy
	}
	CustomerSearchResults?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CustomerSearchResultsKeySpecifier | (() => undefined | CustomerSearchResultsKeySpecifier)
		fields?: CustomerSearchResultsFieldPolicy
	}
	CustomerShipping?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CustomerShippingKeySpecifier | (() => undefined | CustomerShippingKeySpecifier)
		fields?: CustomerShippingFieldPolicy
	}
	CustomerSourceList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CustomerSourceListKeySpecifier | (() => undefined | CustomerSourceListKeySpecifier)
		fields?: CustomerSourceListFieldPolicy
	}
	CustomerTaxId?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CustomerTaxIdKeySpecifier | (() => undefined | CustomerTaxIdKeySpecifier)
		fields?: CustomerTaxIdFieldPolicy
	}
	DebitCreditsApplied?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | DebitCreditsAppliedKeySpecifier | (() => undefined | DebitCreditsAppliedKeySpecifier)
		fields?: DebitCreditsAppliedFieldPolicy
	}
	DefaultSettings?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | DefaultSettingsKeySpecifier | (() => undefined | DefaultSettingsKeySpecifier)
		fields?: DefaultSettingsFieldPolicy
	}
	Discount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | DiscountKeySpecifier | (() => undefined | DiscountKeySpecifier)
		fields?: DiscountFieldPolicy
	}
	DiscountAmount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | DiscountAmountKeySpecifier | (() => undefined | DiscountAmountKeySpecifier)
		fields?: DiscountAmountFieldPolicy
	}
	Dispute?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | DisputeKeySpecifier | (() => undefined | DisputeKeySpecifier)
		fields?: DisputeFieldPolicy
	}
	DisputedTransaction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | DisputedTransactionKeySpecifier | (() => undefined | DisputedTransactionKeySpecifier)
		fields?: DisputedTransactionFieldPolicy
	}
	Dob?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | DobKeySpecifier | (() => undefined | DobKeySpecifier)
		fields?: DobFieldPolicy
	}
	EmailTemplate?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EmailTemplateKeySpecifier | (() => undefined | EmailTemplateKeySpecifier)
		fields?: EmailTemplateFieldPolicy
	}
	EnhancedEligibility?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EnhancedEligibilityKeySpecifier | (() => undefined | EnhancedEligibilityKeySpecifier)
		fields?: EnhancedEligibilityFieldPolicy
	}
	EnhancedEligibilityVisaCompellingEvidence3?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| EnhancedEligibilityVisaCompellingEvidence3KeySpecifier
			| (() => undefined | EnhancedEligibilityVisaCompellingEvidence3KeySpecifier)
		fields?: EnhancedEligibilityVisaCompellingEvidence3FieldPolicy
	}
	EnhancedEvidence?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EnhancedEvidenceKeySpecifier | (() => undefined | EnhancedEvidenceKeySpecifier)
		fields?: EnhancedEvidenceFieldPolicy
	}
	EnhancedEvidenceVisaCompellingEvidence3?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| EnhancedEvidenceVisaCompellingEvidence3KeySpecifier
			| (() => undefined | EnhancedEvidenceVisaCompellingEvidence3KeySpecifier)
		fields?: EnhancedEvidenceVisaCompellingEvidence3FieldPolicy
	}
	EntityCreated?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EntityCreatedKeySpecifier | (() => undefined | EntityCreatedKeySpecifier)
		fields?: EntityCreatedFieldPolicy
	}
	EntityDeleteResult?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EntityDeleteResultKeySpecifier | (() => undefined | EntityDeleteResultKeySpecifier)
		fields?: EntityDeleteResultFieldPolicy
	}
	EntityDeleted?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EntityDeletedKeySpecifier | (() => undefined | EntityDeletedKeySpecifier)
		fields?: EntityDeletedFieldPolicy
	}
	EntityDisabled?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EntityDisabledKeySpecifier | (() => undefined | EntityDisabledKeySpecifier)
		fields?: EntityDisabledFieldPolicy
	}
	EntityEnabled?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EntityEnabledKeySpecifier | (() => undefined | EntityEnabledKeySpecifier)
		fields?: EntityEnabledFieldPolicy
	}
	EntityEvent?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EntityEventKeySpecifier | (() => undefined | EntityEventKeySpecifier)
		fields?: EntityEventFieldPolicy
	}
	EntityRelations?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EntityRelationsKeySpecifier | (() => undefined | EntityRelationsKeySpecifier)
		fields?: EntityRelationsFieldPolicy
	}
	EntityRestored?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EntityRestoredKeySpecifier | (() => undefined | EntityRestoredKeySpecifier)
		fields?: EntityRestoredFieldPolicy
	}
	EntityUpdated?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EntityUpdatedKeySpecifier | (() => undefined | EntityUpdatedKeySpecifier)
		fields?: EntityUpdatedFieldPolicy
	}
	Eps?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EpsKeySpecifier | (() => undefined | EpsKeySpecifier)
		fields?: EpsFieldPolicy
	}
	Evidence?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EvidenceKeySpecifier | (() => undefined | EvidenceKeySpecifier)
		fields?: EvidenceFieldPolicy
	}
	EvidenceDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EvidenceDetailsKeySpecifier | (() => undefined | EvidenceDetailsKeySpecifier)
		fields?: EvidenceDetailsFieldPolicy
	}
	ExternalAccountsList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ExternalAccountsListKeySpecifier | (() => undefined | ExternalAccountsListKeySpecifier)
		fields?: ExternalAccountsListFieldPolicy
	}
	FederatedUser?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FederatedUserKeySpecifier | (() => undefined | FederatedUserKeySpecifier)
		fields?: FederatedUserFieldPolicy
	}
	FeeDetail?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FeeDetailKeySpecifier | (() => undefined | FeeDetailKeySpecifier)
		fields?: FeeDetailFieldPolicy
	}
	FeeRefund?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FeeRefundKeySpecifier | (() => undefined | FeeRefundKeySpecifier)
		fields?: FeeRefundFieldPolicy
	}
	FeeRefundList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FeeRefundListKeySpecifier | (() => undefined | FeeRefundListKeySpecifier)
		fields?: FeeRefundListFieldPolicy
	}
	FeeSource?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FeeSourceKeySpecifier | (() => undefined | FeeSourceKeySpecifier)
		fields?: FeeSourceFieldPolicy
	}
	File?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FileKeySpecifier | (() => undefined | FileKeySpecifier)
		fields?: FileFieldPolicy
	}
	FileLink?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FileLinkKeySpecifier | (() => undefined | FileLinkKeySpecifier)
		fields?: FileLinkFieldPolicy
	}
	FileLinkList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FileLinkListKeySpecifier | (() => undefined | FileLinkListKeySpecifier)
		fields?: FileLinkListFieldPolicy
	}
	Fingerprint?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FingerprintKeySpecifier | (() => undefined | FingerprintKeySpecifier)
		fields?: FingerprintFieldPolicy
	}
	FlatAmount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FlatAmountKeySpecifier | (() => undefined | FlatAmountKeySpecifier)
		fields?: FlatAmountFieldPolicy
	}
	Fpx?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FpxKeySpecifier | (() => undefined | FpxKeySpecifier)
		fields?: FpxFieldPolicy
	}
	FraudDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FraudDetailsKeySpecifier | (() => undefined | FraudDetailsKeySpecifier)
		fields?: FraudDetailsFieldPolicy
	}
	FromInvoice?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FromInvoiceKeySpecifier | (() => undefined | FromInvoiceKeySpecifier)
		fields?: FromInvoiceFieldPolicy
	}
	FromQuote?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FromQuoteKeySpecifier | (() => undefined | FromQuoteKeySpecifier)
		fields?: FromQuoteFieldPolicy
	}
	FundingInstructions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FundingInstructionsKeySpecifier | (() => undefined | FundingInstructionsKeySpecifier)
		fields?: FundingInstructionsFieldPolicy
	}
	FundingInstructionsBankTransfer?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| FundingInstructionsBankTransferKeySpecifier
			| (() => undefined | FundingInstructionsBankTransferKeySpecifier)
		fields?: FundingInstructionsBankTransferFieldPolicy
	}
	FutureRequirements?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FutureRequirementsKeySpecifier | (() => undefined | FutureRequirementsKeySpecifier)
		fields?: FutureRequirementsFieldPolicy
	}
	GIAI96Components?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | GIAI96ComponentsKeySpecifier | (() => undefined | GIAI96ComponentsKeySpecifier)
		fields?: GIAI96ComponentsFieldPolicy
	}
	GID96Components?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | GID96ComponentsKeySpecifier | (() => undefined | GID96ComponentsKeySpecifier)
		fields?: GID96ComponentsFieldPolicy
	}
	GRAI96Components?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | GRAI96ComponentsKeySpecifier | (() => undefined | GRAI96ComponentsKeySpecifier)
		fields?: GRAI96ComponentsFieldPolicy
	}
	GeneratedFrom?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | GeneratedFromKeySpecifier | (() => undefined | GeneratedFromKeySpecifier)
		fields?: GeneratedFromFieldPolicy
	}
	GenericAuditEvent?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | GenericAuditEventKeySpecifier | (() => undefined | GenericAuditEventKeySpecifier)
		fields?: GenericAuditEventFieldPolicy
	}
	ISO15963Components?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ISO15963ComponentsKeySpecifier | (() => undefined | ISO15963ComponentsKeySpecifier)
		fields?: ISO15963ComponentsFieldPolicy
	}
	Ideal?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | IdealKeySpecifier | (() => undefined | IdealKeySpecifier)
		fields?: IdealFieldPolicy
	}
	Installation?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InstallationKeySpecifier | (() => undefined | InstallationKeySpecifier)
		fields?: InstallationFieldPolicy
	}
	Invitation?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InvitationKeySpecifier | (() => undefined | InvitationKeySpecifier)
		fields?: InvitationFieldPolicy
	}
	Invoice?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InvoiceKeySpecifier | (() => undefined | InvoiceKeySpecifier)
		fields?: InvoiceFieldPolicy
	}
	InvoiceAutomaticTax?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InvoiceAutomaticTaxKeySpecifier | (() => undefined | InvoiceAutomaticTaxKeySpecifier)
		fields?: InvoiceAutomaticTaxFieldPolicy
	}
	InvoiceItem?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InvoiceItemKeySpecifier | (() => undefined | InvoiceItemKeySpecifier)
		fields?: InvoiceItemFieldPolicy
	}
	InvoiceLineItem?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InvoiceLineItemKeySpecifier | (() => undefined | InvoiceLineItemKeySpecifier)
		fields?: InvoiceLineItemFieldPolicy
	}
	InvoiceLineItemList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InvoiceLineItemListKeySpecifier | (() => undefined | InvoiceLineItemListKeySpecifier)
		fields?: InvoiceLineItemListFieldPolicy
	}
	InvoiceList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InvoiceListKeySpecifier | (() => undefined | InvoiceListKeySpecifier)
		fields?: InvoiceListFieldPolicy
	}
	InvoiceSearchResults?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InvoiceSearchResultsKeySpecifier | (() => undefined | InvoiceSearchResultsKeySpecifier)
		fields?: InvoiceSearchResultsFieldPolicy
	}
	InvoiceSettings?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InvoiceSettingsKeySpecifier | (() => undefined | InvoiceSettingsKeySpecifier)
		fields?: InvoiceSettingsFieldPolicy
	}
	InvoiceSettingsCustomField?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| InvoiceSettingsCustomFieldKeySpecifier
			| (() => undefined | InvoiceSettingsCustomFieldKeySpecifier)
		fields?: InvoiceSettingsCustomFieldFieldPolicy
	}
	InvoiceSettingsIssuer?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InvoiceSettingsIssuerKeySpecifier | (() => undefined | InvoiceSettingsIssuerKeySpecifier)
		fields?: InvoiceSettingsIssuerFieldPolicy
	}
	InvoiceSettingsRenderingOptions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| InvoiceSettingsRenderingOptionsKeySpecifier
			| (() => undefined | InvoiceSettingsRenderingOptionsKeySpecifier)
		fields?: InvoiceSettingsRenderingOptionsFieldPolicy
	}
	InvoiceTransferData?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | InvoiceTransferDataKeySpecifier | (() => undefined | InvoiceTransferDataKeySpecifier)
		fields?: InvoiceTransferDataFieldPolicy
	}
	IpAddressLocation?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | IpAddressLocationKeySpecifier | (() => undefined | IpAddressLocationKeySpecifier)
		fields?: IpAddressLocationFieldPolicy
	}
	Issuer?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | IssuerKeySpecifier | (() => undefined | IssuerKeySpecifier)
		fields?: IssuerFieldPolicy
	}
	IssuingAuthorization?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | IssuingAuthorizationKeySpecifier | (() => undefined | IssuingAuthorizationKeySpecifier)
		fields?: IssuingAuthorizationFieldPolicy
	}
	IssuingDispute?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | IssuingDisputeKeySpecifier | (() => undefined | IssuingDisputeKeySpecifier)
		fields?: IssuingDisputeFieldPolicy
	}
	IssuingTransaction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | IssuingTransactionKeySpecifier | (() => undefined | IssuingTransactionKeySpecifier)
		fields?: IssuingTransactionFieldPolicy
	}
	Klarna?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | KlarnaKeySpecifier | (() => undefined | KlarnaKeySpecifier)
		fields?: KlarnaFieldPolicy
	}
	KlarnaDob?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | KlarnaDobKeySpecifier | (() => undefined | KlarnaDobKeySpecifier)
		fields?: KlarnaDobFieldPolicy
	}
	Label?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | LabelKeySpecifier | (() => undefined | LabelKeySpecifier)
		fields?: LabelFieldPolicy
	}
	LastFinalizationError?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | LastFinalizationErrorKeySpecifier | (() => undefined | LastFinalizationErrorKeySpecifier)
		fields?: LastFinalizationErrorFieldPolicy
	}
	LastPaymentError?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | LastPaymentErrorKeySpecifier | (() => undefined | LastPaymentErrorKeySpecifier)
		fields?: LastPaymentErrorFieldPolicy
	}
	LastSetupError?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | LastSetupErrorKeySpecifier | (() => undefined | LastSetupErrorKeySpecifier)
		fields?: LastSetupErrorFieldPolicy
	}
	Level3?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | Level3KeySpecifier | (() => undefined | Level3KeySpecifier)
		fields?: Level3FieldPolicy
	}
	Level3LineItem?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | Level3LineItemKeySpecifier | (() => undefined | Level3LineItemKeySpecifier)
		fields?: Level3LineItemFieldPolicy
	}
	Liability?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | LiabilityKeySpecifier | (() => undefined | LiabilityKeySpecifier)
		fields?: LiabilityFieldPolicy
	}
	LineItem?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | LineItemKeySpecifier | (() => undefined | LineItemKeySpecifier)
		fields?: LineItemFieldPolicy
	}
	LineItemDiscount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | LineItemDiscountKeySpecifier | (() => undefined | LineItemDiscountKeySpecifier)
		fields?: LineItemDiscountFieldPolicy
	}
	LineItemList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | LineItemListKeySpecifier | (() => undefined | LineItemListKeySpecifier)
		fields?: LineItemListFieldPolicy
	}
	LineItemTax?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | LineItemTaxKeySpecifier | (() => undefined | LineItemTaxKeySpecifier)
		fields?: LineItemTaxFieldPolicy
	}
	Link?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | LinkKeySpecifier | (() => undefined | LinkKeySpecifier)
		fields?: LinkFieldPolicy
	}
	Mandate?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MandateKeySpecifier | (() => undefined | MandateKeySpecifier)
		fields?: MandateFieldPolicy
	}
	MandateCustomerAcceptance?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| MandateCustomerAcceptanceKeySpecifier
			| (() => undefined | MandateCustomerAcceptanceKeySpecifier)
		fields?: MandateCustomerAcceptanceFieldPolicy
	}
	MandateCustomerAcceptanceOnline?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| MandateCustomerAcceptanceOnlineKeySpecifier
			| (() => undefined | MandateCustomerAcceptanceOnlineKeySpecifier)
		fields?: MandateCustomerAcceptanceOnlineFieldPolicy
	}
	MandatePaymentMethodDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| MandatePaymentMethodDetailsKeySpecifier
			| (() => undefined | MandatePaymentMethodDetailsKeySpecifier)
		fields?: MandatePaymentMethodDetailsFieldPolicy
	}
	MandatePaymentMethodDetailsAcssDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| MandatePaymentMethodDetailsAcssDebitKeySpecifier
			| (() => undefined | MandatePaymentMethodDetailsAcssDebitKeySpecifier)
		fields?: MandatePaymentMethodDetailsAcssDebitFieldPolicy
	}
	MandatePaymentMethodDetailsAuBecsDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| MandatePaymentMethodDetailsAuBecsDebitKeySpecifier
			| (() => undefined | MandatePaymentMethodDetailsAuBecsDebitKeySpecifier)
		fields?: MandatePaymentMethodDetailsAuBecsDebitFieldPolicy
	}
	MandatePaymentMethodDetailsBacsDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| MandatePaymentMethodDetailsBacsDebitKeySpecifier
			| (() => undefined | MandatePaymentMethodDetailsBacsDebitKeySpecifier)
		fields?: MandatePaymentMethodDetailsBacsDebitFieldPolicy
	}
	MandatePaymentMethodDetailsSepaDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| MandatePaymentMethodDetailsSepaDebitKeySpecifier
			| (() => undefined | MandatePaymentMethodDetailsSepaDebitKeySpecifier)
		fields?: MandatePaymentMethodDetailsSepaDebitFieldPolicy
	}
	MandateSingleUse?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MandateSingleUseKeySpecifier | (() => undefined | MandateSingleUseKeySpecifier)
		fields?: MandateSingleUseFieldPolicy
	}
	MarkAreaData?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MarkAreaDataKeySpecifier | (() => undefined | MarkAreaDataKeySpecifier)
		fields?: MarkAreaDataFieldPolicy
	}
	MarkLineData?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MarkLineDataKeySpecifier | (() => undefined | MarkLineDataKeySpecifier)
		fields?: MarkLineDataFieldPolicy
	}
	MarkerPosition?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MarkerPositionKeySpecifier | (() => undefined | MarkerPositionKeySpecifier)
		fields?: MarkerPositionFieldPolicy
	}
	MarketingFeature?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MarketingFeatureKeySpecifier | (() => undefined | MarketingFeatureKeySpecifier)
		fields?: MarketingFeatureFieldPolicy
	}
	Multibanco?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MultibancoKeySpecifier | (() => undefined | MultibancoKeySpecifier)
		fields?: MultibancoFieldPolicy
	}
	Mutation?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MutationKeySpecifier | (() => undefined | MutationKeySpecifier)
		fields?: MutationFieldPolicy
	}
	MyReaction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MyReactionKeySpecifier | (() => undefined | MyReactionKeySpecifier)
		fields?: MyReactionFieldPolicy
	}
	Networks?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | NetworksKeySpecifier | (() => undefined | NetworksKeySpecifier)
		fields?: NetworksFieldPolicy
	}
	NextActionAlipayHandleRedirect?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionAlipayHandleRedirectKeySpecifier
			| (() => undefined | NextActionAlipayHandleRedirectKeySpecifier)
		fields?: NextActionAlipayHandleRedirectFieldPolicy
	}
	NextActionAlipayHandleRedirectKonbiniDisplayDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionAlipayHandleRedirectKonbiniDisplayDetailsKeySpecifier
			| (() => undefined | NextActionAlipayHandleRedirectKonbiniDisplayDetailsKeySpecifier)
		fields?: NextActionAlipayHandleRedirectKonbiniDisplayDetailsFieldPolicy
	}
	NextActionAlipayHandleRedirectKonbiniDisplayDetailsStores?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresKeySpecifier
			| (() => undefined | NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresKeySpecifier)
		fields?: NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresFieldPolicy
	}
	NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresFamilymart?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresFamilymartKeySpecifier
			| (() => undefined | NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresFamilymartKeySpecifier)
		fields?: NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresFamilymartFieldPolicy
	}
	NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresLawson?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresLawsonKeySpecifier
			| (() => undefined | NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresLawsonKeySpecifier)
		fields?: NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresLawsonFieldPolicy
	}
	NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresMinistop?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresMinistopKeySpecifier
			| (() => undefined | NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresMinistopKeySpecifier)
		fields?: NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresMinistopFieldPolicy
	}
	NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresSeicomart?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresSeicomartKeySpecifier
			| (() => undefined | NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresSeicomartKeySpecifier)
		fields?: NextActionAlipayHandleRedirectKonbiniDisplayDetailsStoresSeicomartFieldPolicy
	}
	NextActionAlipayHandleRedirectOxxoDisplayDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionAlipayHandleRedirectOxxoDisplayDetailsKeySpecifier
			| (() => undefined | NextActionAlipayHandleRedirectOxxoDisplayDetailsKeySpecifier)
		fields?: NextActionAlipayHandleRedirectOxxoDisplayDetailsFieldPolicy
	}
	NextActionAlipayHandleRedirectPaynowDisplayQrCode?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionAlipayHandleRedirectPaynowDisplayQrCodeKeySpecifier
			| (() => undefined | NextActionAlipayHandleRedirectPaynowDisplayQrCodeKeySpecifier)
		fields?: NextActionAlipayHandleRedirectPaynowDisplayQrCodeFieldPolicy
	}
	NextActionAlipayHandleRedirectRedirectToUrl?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionAlipayHandleRedirectRedirectToUrlKeySpecifier
			| (() => undefined | NextActionAlipayHandleRedirectRedirectToUrlKeySpecifier)
		fields?: NextActionAlipayHandleRedirectRedirectToUrlFieldPolicy
	}
	NextActionBoletoDisplayDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionBoletoDisplayDetailsKeySpecifier
			| (() => undefined | NextActionBoletoDisplayDetailsKeySpecifier)
		fields?: NextActionBoletoDisplayDetailsFieldPolicy
	}
	NextActionCardAwaitNotification?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionCardAwaitNotificationKeySpecifier
			| (() => undefined | NextActionCardAwaitNotificationKeySpecifier)
		fields?: NextActionCardAwaitNotificationFieldPolicy
	}
	NextActionDisplayBankTransferInstructions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionDisplayBankTransferInstructionsKeySpecifier
			| (() => undefined | NextActionDisplayBankTransferInstructionsKeySpecifier)
		fields?: NextActionDisplayBankTransferInstructionsFieldPolicy
	}
	NextActionDisplayBankTransferInstructionsFinancialAddress?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionDisplayBankTransferInstructionsFinancialAddressKeySpecifier
			| (() => undefined | NextActionDisplayBankTransferInstructionsFinancialAddressKeySpecifier)
		fields?: NextActionDisplayBankTransferInstructionsFinancialAddressFieldPolicy
	}
	NextActionDisplayBankTransferInstructionsFinancialAddressIban?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionDisplayBankTransferInstructionsFinancialAddressIbanKeySpecifier
			| (() => undefined | NextActionDisplayBankTransferInstructionsFinancialAddressIbanKeySpecifier)
		fields?: NextActionDisplayBankTransferInstructionsFinancialAddressIbanFieldPolicy
	}
	NextActionDisplayBankTransferInstructionsFinancialAddressSortCode?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionDisplayBankTransferInstructionsFinancialAddressSortCodeKeySpecifier
			| (() => undefined | NextActionDisplayBankTransferInstructionsFinancialAddressSortCodeKeySpecifier)
		fields?: NextActionDisplayBankTransferInstructionsFinancialAddressSortCodeFieldPolicy
	}
	NextActionDisplayBankTransferInstructionsFinancialAddressSpei?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionDisplayBankTransferInstructionsFinancialAddressSpeiKeySpecifier
			| (() => undefined | NextActionDisplayBankTransferInstructionsFinancialAddressSpeiKeySpecifier)
		fields?: NextActionDisplayBankTransferInstructionsFinancialAddressSpeiFieldPolicy
	}
	NextActionDisplayBankTransferInstructionsFinancialAddressZengin?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionDisplayBankTransferInstructionsFinancialAddressZenginKeySpecifier
			| (() => undefined | NextActionDisplayBankTransferInstructionsFinancialAddressZenginKeySpecifier)
		fields?: NextActionDisplayBankTransferInstructionsFinancialAddressZenginFieldPolicy
	}
	NextActionDisplayDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | NextActionDisplayDetailsKeySpecifier | (() => undefined | NextActionDisplayDetailsKeySpecifier)
		fields?: NextActionDisplayDetailsFieldPolicy
	}
	NextActionDisplayDetailsEmailSent?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionDisplayDetailsEmailSentKeySpecifier
			| (() => undefined | NextActionDisplayDetailsEmailSentKeySpecifier)
		fields?: NextActionDisplayDetailsEmailSentFieldPolicy
	}
	NextActionRedirectToUrl?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | NextActionRedirectToUrlKeySpecifier | (() => undefined | NextActionRedirectToUrlKeySpecifier)
		fields?: NextActionRedirectToUrlFieldPolicy
	}
	NextActionVerifyWithMicrodeposits?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionVerifyWithMicrodepositsKeySpecifier
			| (() => undefined | NextActionVerifyWithMicrodepositsKeySpecifier)
		fields?: NextActionVerifyWithMicrodepositsFieldPolicy
	}
	NextActionWechatPayDisplayQrCode?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionWechatPayDisplayQrCodeKeySpecifier
			| (() => undefined | NextActionWechatPayDisplayQrCodeKeySpecifier)
		fields?: NextActionWechatPayDisplayQrCodeFieldPolicy
	}
	NextActionWechatPayRedirectToAndroidApp?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionWechatPayRedirectToAndroidAppKeySpecifier
			| (() => undefined | NextActionWechatPayRedirectToAndroidAppKeySpecifier)
		fields?: NextActionWechatPayRedirectToAndroidAppFieldPolicy
	}
	NextActionWechatPayRedirectToIosApp?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NextActionWechatPayRedirectToIosAppKeySpecifier
			| (() => undefined | NextActionWechatPayRedirectToIosAppKeySpecifier)
		fields?: NextActionWechatPayRedirectToIosAppFieldPolicy
	}
	Notification?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | NotificationKeySpecifier | (() => undefined | NotificationKeySpecifier)
		fields?: NotificationFieldPolicy
	}
	NotificationCMarkAllReadResult?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NotificationCMarkAllReadResultKeySpecifier
			| (() => undefined | NotificationCMarkAllReadResultKeySpecifier)
		fields?: NotificationCMarkAllReadResultFieldPolicy
	}
	Offline?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | OfflineKeySpecifier | (() => undefined | OfflineKeySpecifier)
		fields?: OfflineFieldPolicy
	}
	OidcClient?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | OidcClientKeySpecifier | (() => undefined | OidcClientKeySpecifier)
		fields?: OidcClientFieldPolicy
	}
	Organization?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | OrganizationKeySpecifier | (() => undefined | OrganizationKeySpecifier)
		fields?: OrganizationFieldPolicy
	}
	OrganizationTemplate?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | OrganizationTemplateKeySpecifier | (() => undefined | OrganizationTemplateKeySpecifier)
		fields?: OrganizationTemplateFieldPolicy
	}
	OrganizationWithRole?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | OrganizationWithRoleKeySpecifier | (() => undefined | OrganizationWithRoleKeySpecifier)
		fields?: OrganizationWithRoleFieldPolicy
	}
	Outcome?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | OutcomeKeySpecifier | (() => undefined | OutcomeKeySpecifier)
		fields?: OutcomeFieldPolicy
	}
	OutcomeRule?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | OutcomeRuleKeySpecifier | (() => undefined | OutcomeRuleKeySpecifier)
		fields?: OutcomeRuleFieldPolicy
	}
	Owner?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | OwnerKeySpecifier | (() => undefined | OwnerKeySpecifier)
		fields?: OwnerFieldPolicy
	}
	OwnershipDeclaration?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | OwnershipDeclarationKeySpecifier | (() => undefined | OwnershipDeclarationKeySpecifier)
		fields?: OwnershipDeclarationFieldPolicy
	}
	P24?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | P24KeySpecifier | (() => undefined | P24KeySpecifier)
		fields?: P24FieldPolicy
	}
	PackageDimensions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PackageDimensionsKeySpecifier | (() => undefined | PackageDimensionsKeySpecifier)
		fields?: PackageDimensionsFieldPolicy
	}
	PaginatedActivations?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedActivationsKeySpecifier | (() => undefined | PaginatedActivationsKeySpecifier)
		fields?: PaginatedActivationsFieldPolicy
	}
	PaginatedAdmins?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedAdminsKeySpecifier | (() => undefined | PaginatedAdminsKeySpecifier)
		fields?: PaginatedAdminsFieldPolicy
	}
	PaginatedAuditEvent?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedAuditEventKeySpecifier | (() => undefined | PaginatedAuditEventKeySpecifier)
		fields?: PaginatedAuditEventFieldPolicy
	}
	PaginatedComments?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedCommentsKeySpecifier | (() => undefined | PaginatedCommentsKeySpecifier)
		fields?: PaginatedCommentsFieldPolicy
	}
	PaginatedFederatedUser?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedFederatedUserKeySpecifier | (() => undefined | PaginatedFederatedUserKeySpecifier)
		fields?: PaginatedFederatedUserFieldPolicy
	}
	PaginatedInstallation?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedInstallationKeySpecifier | (() => undefined | PaginatedInstallationKeySpecifier)
		fields?: PaginatedInstallationFieldPolicy
	}
	PaginatedInvitation?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedInvitationKeySpecifier | (() => undefined | PaginatedInvitationKeySpecifier)
		fields?: PaginatedInvitationFieldPolicy
	}
	PaginatedNotification?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedNotificationKeySpecifier | (() => undefined | PaginatedNotificationKeySpecifier)
		fields?: PaginatedNotificationFieldPolicy
	}
	PaginatedOidcClient?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedOidcClientKeySpecifier | (() => undefined | PaginatedOidcClientKeySpecifier)
		fields?: PaginatedOidcClientFieldPolicy
	}
	PaginatedOrganization?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedOrganizationKeySpecifier | (() => undefined | PaginatedOrganizationKeySpecifier)
		fields?: PaginatedOrganizationFieldPolicy
	}
	PaginatedPerks?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedPerksKeySpecifier | (() => undefined | PaginatedPerksKeySpecifier)
		fields?: PaginatedPerksFieldPolicy
	}
	PaginatedProducts?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedProductsKeySpecifier | (() => undefined | PaginatedProductsKeySpecifier)
		fields?: PaginatedProductsFieldPolicy
	}
	PaginatedPrograms?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedProgramsKeySpecifier | (() => undefined | PaginatedProgramsKeySpecifier)
		fields?: PaginatedProgramsFieldPolicy
	}
	PaginatedRate?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedRateKeySpecifier | (() => undefined | PaginatedRateKeySpecifier)
		fields?: PaginatedRateFieldPolicy
	}
	PaginatedReaction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedReactionKeySpecifier | (() => undefined | PaginatedReactionKeySpecifier)
		fields?: PaginatedReactionFieldPolicy
	}
	PaginatedRole?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedRoleKeySpecifier | (() => undefined | PaginatedRoleKeySpecifier)
		fields?: PaginatedRoleFieldPolicy
	}
	PaginatedTags?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedTagsKeySpecifier | (() => undefined | PaginatedTagsKeySpecifier)
		fields?: PaginatedTagsFieldPolicy
	}
	PaginatedUser?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedUserKeySpecifier | (() => undefined | PaginatedUserKeySpecifier)
		fields?: PaginatedUserFieldPolicy
	}
	PaginatedWebhooks?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaginatedWebhooksKeySpecifier | (() => undefined | PaginatedWebhooksKeySpecifier)
		fields?: PaginatedWebhooksFieldPolicy
	}
	PauseCollection?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PauseCollectionKeySpecifier | (() => undefined | PauseCollectionKeySpecifier)
		fields?: PauseCollectionFieldPolicy
	}
	PaymentIntent?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentIntentKeySpecifier | (() => undefined | PaymentIntentKeySpecifier)
		fields?: PaymentIntentFieldPolicy
	}
	PaymentIntentList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentIntentListKeySpecifier | (() => undefined | PaymentIntentListKeySpecifier)
		fields?: PaymentIntentListFieldPolicy
	}
	PaymentIntentNextAction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentIntentNextActionKeySpecifier | (() => undefined | PaymentIntentNextActionKeySpecifier)
		fields?: PaymentIntentNextActionFieldPolicy
	}
	PaymentIntentSearchResults?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentIntentSearchResultsKeySpecifier
			| (() => undefined | PaymentIntentSearchResultsKeySpecifier)
		fields?: PaymentIntentSearchResultsFieldPolicy
	}
	PaymentMethod?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentMethodKeySpecifier | (() => undefined | PaymentMethodKeySpecifier)
		fields?: PaymentMethodFieldPolicy
	}
	PaymentMethodCard?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentMethodCardKeySpecifier | (() => undefined | PaymentMethodCardKeySpecifier)
		fields?: PaymentMethodCardFieldPolicy
	}
	PaymentMethodConfigurationDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodConfigurationDetailsKeySpecifier
			| (() => undefined | PaymentMethodConfigurationDetailsKeySpecifier)
		fields?: PaymentMethodConfigurationDetailsFieldPolicy
	}
	PaymentMethodDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentMethodDetailsKeySpecifier | (() => undefined | PaymentMethodDetailsKeySpecifier)
		fields?: PaymentMethodDetailsFieldPolicy
	}
	PaymentMethodList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentMethodListKeySpecifier | (() => undefined | PaymentMethodListKeySpecifier)
		fields?: PaymentMethodListFieldPolicy
	}
	PaymentMethodOptions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentMethodOptionsKeySpecifier | (() => undefined | PaymentMethodOptionsKeySpecifier)
		fields?: PaymentMethodOptionsFieldPolicy
	}
	PaymentMethodOptionsAcssDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsAcssDebitKeySpecifier
			| (() => undefined | PaymentMethodOptionsAcssDebitKeySpecifier)
		fields?: PaymentMethodOptionsAcssDebitFieldPolicy
	}
	PaymentMethodOptionsAcssDebitMandateOptions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsAcssDebitMandateOptionsKeySpecifier
			| (() => undefined | PaymentMethodOptionsAcssDebitMandateOptionsKeySpecifier)
		fields?: PaymentMethodOptionsAcssDebitMandateOptionsFieldPolicy
	}
	PaymentMethodOptionsAffirm?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsAffirmKeySpecifier
			| (() => undefined | PaymentMethodOptionsAffirmKeySpecifier)
		fields?: PaymentMethodOptionsAffirmFieldPolicy
	}
	PaymentMethodOptionsAfterpayClearpay?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsAfterpayClearpayKeySpecifier
			| (() => undefined | PaymentMethodOptionsAfterpayClearpayKeySpecifier)
		fields?: PaymentMethodOptionsAfterpayClearpayFieldPolicy
	}
	PaymentMethodOptionsAlipay?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsAlipayKeySpecifier
			| (() => undefined | PaymentMethodOptionsAlipayKeySpecifier)
		fields?: PaymentMethodOptionsAlipayFieldPolicy
	}
	PaymentMethodOptionsAuBecsDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsAuBecsDebitKeySpecifier
			| (() => undefined | PaymentMethodOptionsAuBecsDebitKeySpecifier)
		fields?: PaymentMethodOptionsAuBecsDebitFieldPolicy
	}
	PaymentMethodOptionsBacsDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsBacsDebitKeySpecifier
			| (() => undefined | PaymentMethodOptionsBacsDebitKeySpecifier)
		fields?: PaymentMethodOptionsBacsDebitFieldPolicy
	}
	PaymentMethodOptionsBancontact?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsBancontactKeySpecifier
			| (() => undefined | PaymentMethodOptionsBancontactKeySpecifier)
		fields?: PaymentMethodOptionsBancontactFieldPolicy
	}
	PaymentMethodOptionsBoleto?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsBoletoKeySpecifier
			| (() => undefined | PaymentMethodOptionsBoletoKeySpecifier)
		fields?: PaymentMethodOptionsBoletoFieldPolicy
	}
	PaymentMethodOptionsCard?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentMethodOptionsCardKeySpecifier | (() => undefined | PaymentMethodOptionsCardKeySpecifier)
		fields?: PaymentMethodOptionsCardFieldPolicy
	}
	PaymentMethodOptionsCardInstallments?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsCardInstallmentsKeySpecifier
			| (() => undefined | PaymentMethodOptionsCardInstallmentsKeySpecifier)
		fields?: PaymentMethodOptionsCardInstallmentsFieldPolicy
	}
	PaymentMethodOptionsCardInstallmentsPlan?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsCardInstallmentsPlanKeySpecifier
			| (() => undefined | PaymentMethodOptionsCardInstallmentsPlanKeySpecifier)
		fields?: PaymentMethodOptionsCardInstallmentsPlanFieldPolicy
	}
	PaymentMethodOptionsCardMandateOptions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsCardMandateOptionsKeySpecifier
			| (() => undefined | PaymentMethodOptionsCardMandateOptionsKeySpecifier)
		fields?: PaymentMethodOptionsCardMandateOptionsFieldPolicy
	}
	PaymentMethodOptionsCardPresent?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsCardPresentKeySpecifier
			| (() => undefined | PaymentMethodOptionsCardPresentKeySpecifier)
		fields?: PaymentMethodOptionsCardPresentFieldPolicy
	}
	PaymentMethodOptionsCustomerBalance?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsCustomerBalanceKeySpecifier
			| (() => undefined | PaymentMethodOptionsCustomerBalanceKeySpecifier)
		fields?: PaymentMethodOptionsCustomerBalanceFieldPolicy
	}
	PaymentMethodOptionsCustomerBalanceBankTransfer?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsCustomerBalanceBankTransferKeySpecifier
			| (() => undefined | PaymentMethodOptionsCustomerBalanceBankTransferKeySpecifier)
		fields?: PaymentMethodOptionsCustomerBalanceBankTransferFieldPolicy
	}
	PaymentMethodOptionsCustomerBalanceBankTransferEuBankTransfer?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsCustomerBalanceBankTransferEuBankTransferKeySpecifier
			| (() => undefined | PaymentMethodOptionsCustomerBalanceBankTransferEuBankTransferKeySpecifier)
		fields?: PaymentMethodOptionsCustomerBalanceBankTransferEuBankTransferFieldPolicy
	}
	PaymentMethodOptionsEps?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentMethodOptionsEpsKeySpecifier | (() => undefined | PaymentMethodOptionsEpsKeySpecifier)
		fields?: PaymentMethodOptionsEpsFieldPolicy
	}
	PaymentMethodOptionsFpx?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentMethodOptionsFpxKeySpecifier | (() => undefined | PaymentMethodOptionsFpxKeySpecifier)
		fields?: PaymentMethodOptionsFpxFieldPolicy
	}
	PaymentMethodOptionsGiropay?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsGiropayKeySpecifier
			| (() => undefined | PaymentMethodOptionsGiropayKeySpecifier)
		fields?: PaymentMethodOptionsGiropayFieldPolicy
	}
	PaymentMethodOptionsGrabpay?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsGrabpayKeySpecifier
			| (() => undefined | PaymentMethodOptionsGrabpayKeySpecifier)
		fields?: PaymentMethodOptionsGrabpayFieldPolicy
	}
	PaymentMethodOptionsIdeal?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsIdealKeySpecifier
			| (() => undefined | PaymentMethodOptionsIdealKeySpecifier)
		fields?: PaymentMethodOptionsIdealFieldPolicy
	}
	PaymentMethodOptionsKlarna?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsKlarnaKeySpecifier
			| (() => undefined | PaymentMethodOptionsKlarnaKeySpecifier)
		fields?: PaymentMethodOptionsKlarnaFieldPolicy
	}
	PaymentMethodOptionsKonbini?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsKonbiniKeySpecifier
			| (() => undefined | PaymentMethodOptionsKonbiniKeySpecifier)
		fields?: PaymentMethodOptionsKonbiniFieldPolicy
	}
	PaymentMethodOptionsLink?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentMethodOptionsLinkKeySpecifier | (() => undefined | PaymentMethodOptionsLinkKeySpecifier)
		fields?: PaymentMethodOptionsLinkFieldPolicy
	}
	PaymentMethodOptionsOxxo?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentMethodOptionsOxxoKeySpecifier | (() => undefined | PaymentMethodOptionsOxxoKeySpecifier)
		fields?: PaymentMethodOptionsOxxoFieldPolicy
	}
	PaymentMethodOptionsP24?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentMethodOptionsP24KeySpecifier | (() => undefined | PaymentMethodOptionsP24KeySpecifier)
		fields?: PaymentMethodOptionsP24FieldPolicy
	}
	PaymentMethodOptionsPaynow?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsPaynowKeySpecifier
			| (() => undefined | PaymentMethodOptionsPaynowKeySpecifier)
		fields?: PaymentMethodOptionsPaynowFieldPolicy
	}
	PaymentMethodOptionsSepaDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsSepaDebitKeySpecifier
			| (() => undefined | PaymentMethodOptionsSepaDebitKeySpecifier)
		fields?: PaymentMethodOptionsSepaDebitFieldPolicy
	}
	PaymentMethodOptionsSofort?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsSofortKeySpecifier
			| (() => undefined | PaymentMethodOptionsSofortKeySpecifier)
		fields?: PaymentMethodOptionsSofortFieldPolicy
	}
	PaymentMethodOptionsUsBankAccount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsUsBankAccountKeySpecifier
			| (() => undefined | PaymentMethodOptionsUsBankAccountKeySpecifier)
		fields?: PaymentMethodOptionsUsBankAccountFieldPolicy
	}
	PaymentMethodOptionsUsBankAccountFinancialConnections?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsUsBankAccountFinancialConnectionsKeySpecifier
			| (() => undefined | PaymentMethodOptionsUsBankAccountFinancialConnectionsKeySpecifier)
		fields?: PaymentMethodOptionsUsBankAccountFinancialConnectionsFieldPolicy
	}
	PaymentMethodOptionsUsBankAccountNetworks?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsUsBankAccountNetworksKeySpecifier
			| (() => undefined | PaymentMethodOptionsUsBankAccountNetworksKeySpecifier)
		fields?: PaymentMethodOptionsUsBankAccountNetworksFieldPolicy
	}
	PaymentMethodOptionsWechatPay?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentMethodOptionsWechatPayKeySpecifier
			| (() => undefined | PaymentMethodOptionsWechatPayKeySpecifier)
		fields?: PaymentMethodOptionsWechatPayFieldPolicy
	}
	PaymentSettings?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PaymentSettingsKeySpecifier | (() => undefined | PaymentSettingsKeySpecifier)
		fields?: PaymentSettingsFieldPolicy
	}
	PaymentSettingsPaymentMethodOptions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentSettingsPaymentMethodOptionsKeySpecifier
			| (() => undefined | PaymentSettingsPaymentMethodOptionsKeySpecifier)
		fields?: PaymentSettingsPaymentMethodOptionsFieldPolicy
	}
	PaymentSettingsPaymentMethodOptionsAcssDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentSettingsPaymentMethodOptionsAcssDebitKeySpecifier
			| (() => undefined | PaymentSettingsPaymentMethodOptionsAcssDebitKeySpecifier)
		fields?: PaymentSettingsPaymentMethodOptionsAcssDebitFieldPolicy
	}
	PaymentSettingsPaymentMethodOptionsAcssDebitMandateOptions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PaymentSettingsPaymentMethodOptionsAcssDebitMandateOptionsKeySpecifier
			| (() => undefined | PaymentSettingsPaymentMethodOptionsAcssDebitMandateOptionsKeySpecifier)
		fields?: PaymentSettingsPaymentMethodOptionsAcssDebitMandateOptionsFieldPolicy
	}
	Payout?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PayoutKeySpecifier | (() => undefined | PayoutKeySpecifier)
		fields?: PayoutFieldPolicy
	}
	PendingInvoiceItemInterval?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PendingInvoiceItemIntervalKeySpecifier
			| (() => undefined | PendingInvoiceItemIntervalKeySpecifier)
		fields?: PendingInvoiceItemIntervalFieldPolicy
	}
	PendingUpdate?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PendingUpdateKeySpecifier | (() => undefined | PendingUpdateKeySpecifier)
		fields?: PendingUpdateFieldPolicy
	}
	Percentiles?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PercentilesKeySpecifier | (() => undefined | PercentilesKeySpecifier)
		fields?: PercentilesFieldPolicy
	}
	Period?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PeriodKeySpecifier | (() => undefined | PeriodKeySpecifier)
		fields?: PeriodFieldPolicy
	}
	Perk?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PerkKeySpecifier | (() => undefined | PerkKeySpecifier)
		fields?: PerkFieldPolicy
	}
	Person?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PersonKeySpecifier | (() => undefined | PersonKeySpecifier)
		fields?: PersonFieldPolicy
	}
	PersonVerification?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PersonVerificationKeySpecifier | (() => undefined | PersonVerificationKeySpecifier)
		fields?: PersonVerificationFieldPolicy
	}
	Phase?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PhaseKeySpecifier | (() => undefined | PhaseKeySpecifier)
		fields?: PhaseFieldPolicy
	}
	PhaseAddInvoiceItem?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PhaseAddInvoiceItemKeySpecifier | (() => undefined | PhaseAddInvoiceItemKeySpecifier)
		fields?: PhaseAddInvoiceItemFieldPolicy
	}
	PhaseAutomaticTax?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PhaseAutomaticTaxKeySpecifier | (() => undefined | PhaseAutomaticTaxKeySpecifier)
		fields?: PhaseAutomaticTaxFieldPolicy
	}
	PhaseBillingThresholds?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PhaseBillingThresholdsKeySpecifier | (() => undefined | PhaseBillingThresholdsKeySpecifier)
		fields?: PhaseBillingThresholdsFieldPolicy
	}
	PhaseDiscount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PhaseDiscountKeySpecifier | (() => undefined | PhaseDiscountKeySpecifier)
		fields?: PhaseDiscountFieldPolicy
	}
	PhaseInvoiceSettings?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PhaseInvoiceSettingsKeySpecifier | (() => undefined | PhaseInvoiceSettingsKeySpecifier)
		fields?: PhaseInvoiceSettingsFieldPolicy
	}
	PhaseItem?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PhaseItemKeySpecifier | (() => undefined | PhaseItemKeySpecifier)
		fields?: PhaseItemFieldPolicy
	}
	PhaseItemBillingThresholds?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PhaseItemBillingThresholdsKeySpecifier
			| (() => undefined | PhaseItemBillingThresholdsKeySpecifier)
		fields?: PhaseItemBillingThresholdsFieldPolicy
	}
	PhaseTransferData?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PhaseTransferDataKeySpecifier | (() => undefined | PhaseTransferDataKeySpecifier)
		fields?: PhaseTransferDataFieldPolicy
	}
	Plan?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PlanKeySpecifier | (() => undefined | PlanKeySpecifier)
		fields?: PlanFieldPolicy
	}
	PretaxCreditAmount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PretaxCreditAmountKeySpecifier | (() => undefined | PretaxCreditAmountKeySpecifier)
		fields?: PretaxCreditAmountFieldPolicy
	}
	Price?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PriceKeySpecifier | (() => undefined | PriceKeySpecifier)
		fields?: PriceFieldPolicy
	}
	PriceRecurring?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PriceRecurringKeySpecifier | (() => undefined | PriceRecurringKeySpecifier)
		fields?: PriceRecurringFieldPolicy
	}
	PriorUndisputedTransaction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PriorUndisputedTransactionKeySpecifier
			| (() => undefined | PriorUndisputedTransactionKeySpecifier)
		fields?: PriorUndisputedTransactionFieldPolicy
	}
	Processing?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ProcessingKeySpecifier | (() => undefined | ProcessingKeySpecifier)
		fields?: ProcessingFieldPolicy
	}
	ProcessingCard?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ProcessingCardKeySpecifier | (() => undefined | ProcessingCardKeySpecifier)
		fields?: ProcessingCardFieldPolicy
	}
	ProcessingCardCustomerNotification?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| ProcessingCardCustomerNotificationKeySpecifier
			| (() => undefined | ProcessingCardCustomerNotificationKeySpecifier)
		fields?: ProcessingCardCustomerNotificationFieldPolicy
	}
	Product?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ProductKeySpecifier | (() => undefined | ProductKeySpecifier)
		fields?: ProductFieldPolicy
	}
	ProductDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ProductDetailsKeySpecifier | (() => undefined | ProductDetailsKeySpecifier)
		fields?: ProductDetailsFieldPolicy
	}
	Program?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ProgramKeySpecifier | (() => undefined | ProgramKeySpecifier)
		fields?: ProgramFieldPolicy
	}
	PromotionCode?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PromotionCodeKeySpecifier | (() => undefined | PromotionCodeKeySpecifier)
		fields?: PromotionCodeFieldPolicy
	}
	PromotionCodeRestrictions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| PromotionCodeRestrictionsKeySpecifier
			| (() => undefined | PromotionCodeRestrictionsKeySpecifier)
		fields?: PromotionCodeRestrictionsFieldPolicy
	}
	ProrationDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ProrationDetailsKeySpecifier | (() => undefined | ProrationDetailsKeySpecifier)
		fields?: ProrationDetailsFieldPolicy
	}
	ProrationDetailsCreditedItems?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| ProrationDetailsCreditedItemsKeySpecifier
			| (() => undefined | ProrationDetailsCreditedItemsKeySpecifier)
		fields?: ProrationDetailsCreditedItemsFieldPolicy
	}
	Query?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | QueryKeySpecifier | (() => undefined | QueryKeySpecifier)
		fields?: QueryFieldPolicy
	}
	Quote?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | QuoteKeySpecifier | (() => undefined | QuoteKeySpecifier)
		fields?: QuoteFieldPolicy
	}
	QuoteInvoiceSettings?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | QuoteInvoiceSettingsKeySpecifier | (() => undefined | QuoteInvoiceSettingsKeySpecifier)
		fields?: QuoteInvoiceSettingsFieldPolicy
	}
	QuoteStatusTransitions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | QuoteStatusTransitionsKeySpecifier | (() => undefined | QuoteStatusTransitionsKeySpecifier)
		fields?: QuoteStatusTransitionsFieldPolicy
	}
	QuoteTransferData?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | QuoteTransferDataKeySpecifier | (() => undefined | QuoteTransferDataKeySpecifier)
		fields?: QuoteTransferDataFieldPolicy
	}
	RadarOptions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RadarOptionsKeySpecifier | (() => undefined | RadarOptionsKeySpecifier)
		fields?: RadarOptionsFieldPolicy
	}
	Rate?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RateKeySpecifier | (() => undefined | RateKeySpecifier)
		fields?: RateFieldPolicy
	}
	Reaction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ReactionKeySpecifier | (() => undefined | ReactionKeySpecifier)
		fields?: ReactionFieldPolicy
	}
	ReactionCount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ReactionCountKeySpecifier | (() => undefined | ReactionCountKeySpecifier)
		fields?: ReactionCountFieldPolicy
	}
	Reactions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ReactionsKeySpecifier | (() => undefined | ReactionsKeySpecifier)
		fields?: ReactionsFieldPolicy
	}
	Receiver?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ReceiverKeySpecifier | (() => undefined | ReceiverKeySpecifier)
		fields?: ReceiverFieldPolicy
	}
	Redirect?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RedirectKeySpecifier | (() => undefined | RedirectKeySpecifier)
		fields?: RedirectFieldPolicy
	}
	Refund?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RefundKeySpecifier | (() => undefined | RefundKeySpecifier)
		fields?: RefundFieldPolicy
	}
	RefundList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RefundListKeySpecifier | (() => undefined | RefundListKeySpecifier)
		fields?: RefundListFieldPolicy
	}
	RefundNextAction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RefundNextActionKeySpecifier | (() => undefined | RefundNextActionKeySpecifier)
		fields?: RefundNextActionFieldPolicy
	}
	Relationship?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RelationshipKeySpecifier | (() => undefined | RelationshipKeySpecifier)
		fields?: RelationshipFieldPolicy
	}
	Rendering?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RenderingKeySpecifier | (() => undefined | RenderingKeySpecifier)
		fields?: RenderingFieldPolicy
	}
	RenderingPdf?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RenderingPdfKeySpecifier | (() => undefined | RenderingPdfKeySpecifier)
		fields?: RenderingPdfFieldPolicy
	}
	Requirements?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RequirementsKeySpecifier | (() => undefined | RequirementsKeySpecifier)
		fields?: RequirementsFieldPolicy
	}
	RequirementsAlternative?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RequirementsAlternativeKeySpecifier | (() => undefined | RequirementsAlternativeKeySpecifier)
		fields?: RequirementsAlternativeFieldPolicy
	}
	RequirementsError?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RequirementsErrorKeySpecifier | (() => undefined | RequirementsErrorKeySpecifier)
		fields?: RequirementsErrorFieldPolicy
	}
	ReserveTransaction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ReserveTransactionKeySpecifier | (() => undefined | ReserveTransactionKeySpecifier)
		fields?: ReserveTransactionFieldPolicy
	}
	Review?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ReviewKeySpecifier | (() => undefined | ReviewKeySpecifier)
		fields?: ReviewFieldPolicy
	}
	Role?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | RoleKeySpecifier | (() => undefined | RoleKeySpecifier)
		fields?: RoleFieldPolicy
	}
	SGLN96Components?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SGLN96ComponentsKeySpecifier | (() => undefined | SGLN96ComponentsKeySpecifier)
		fields?: SGLN96ComponentsFieldPolicy
	}
	SGTIN96Components?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SGTIN96ComponentsKeySpecifier | (() => undefined | SGTIN96ComponentsKeySpecifier)
		fields?: SGTIN96ComponentsFieldPolicy
	}
	SSCC96Components?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SSCC96ComponentsKeySpecifier | (() => undefined | SSCC96ComponentsKeySpecifier)
		fields?: SSCC96ComponentsFieldPolicy
	}
	SepaCreditTransfer?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SepaCreditTransferKeySpecifier | (() => undefined | SepaCreditTransferKeySpecifier)
		fields?: SepaCreditTransferFieldPolicy
	}
	SepaDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SepaDebitKeySpecifier | (() => undefined | SepaDebitKeySpecifier)
		fields?: SepaDebitFieldPolicy
	}
	SepaDebitGeneratedFrom?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SepaDebitGeneratedFromKeySpecifier | (() => undefined | SepaDebitGeneratedFromKeySpecifier)
		fields?: SepaDebitGeneratedFromFieldPolicy
	}
	Session?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SessionKeySpecifier | (() => undefined | SessionKeySpecifier)
		fields?: SessionFieldPolicy
	}
	SetupAttempt?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SetupAttemptKeySpecifier | (() => undefined | SetupAttemptKeySpecifier)
		fields?: SetupAttemptFieldPolicy
	}
	SetupError?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SetupErrorKeySpecifier | (() => undefined | SetupErrorKeySpecifier)
		fields?: SetupErrorFieldPolicy
	}
	SetupIntent?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SetupIntentKeySpecifier | (() => undefined | SetupIntentKeySpecifier)
		fields?: SetupIntentFieldPolicy
	}
	SetupIntentNextAction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SetupIntentNextActionKeySpecifier | (() => undefined | SetupIntentNextActionKeySpecifier)
		fields?: SetupIntentNextActionFieldPolicy
	}
	SetupIntentPaymentMethodOptions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SetupIntentPaymentMethodOptionsKeySpecifier
			| (() => undefined | SetupIntentPaymentMethodOptionsKeySpecifier)
		fields?: SetupIntentPaymentMethodOptionsFieldPolicy
	}
	SetupIntentPaymentMethodOptionsAcssDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SetupIntentPaymentMethodOptionsAcssDebitKeySpecifier
			| (() => undefined | SetupIntentPaymentMethodOptionsAcssDebitKeySpecifier)
		fields?: SetupIntentPaymentMethodOptionsAcssDebitFieldPolicy
	}
	SetupIntentPaymentMethodOptionsCard?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SetupIntentPaymentMethodOptionsCardKeySpecifier
			| (() => undefined | SetupIntentPaymentMethodOptionsCardKeySpecifier)
		fields?: SetupIntentPaymentMethodOptionsCardFieldPolicy
	}
	SetupIntentPaymentMethodOptionsLink?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SetupIntentPaymentMethodOptionsLinkKeySpecifier
			| (() => undefined | SetupIntentPaymentMethodOptionsLinkKeySpecifier)
		fields?: SetupIntentPaymentMethodOptionsLinkFieldPolicy
	}
	SetupIntentPaymentMethodOptionsSepaDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SetupIntentPaymentMethodOptionsSepaDebitKeySpecifier
			| (() => undefined | SetupIntentPaymentMethodOptionsSepaDebitKeySpecifier)
		fields?: SetupIntentPaymentMethodOptionsSepaDebitFieldPolicy
	}
	SetupIntentPaymentMethodOptionsUsBankAccount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SetupIntentPaymentMethodOptionsUsBankAccountKeySpecifier
			| (() => undefined | SetupIntentPaymentMethodOptionsUsBankAccountKeySpecifier)
		fields?: SetupIntentPaymentMethodOptionsUsBankAccountFieldPolicy
	}
	SetupIntentPaymentMethodOptionsUsBankAccountFinancialConnections?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SetupIntentPaymentMethodOptionsUsBankAccountFinancialConnectionsKeySpecifier
			| (() => undefined | SetupIntentPaymentMethodOptionsUsBankAccountFinancialConnectionsKeySpecifier)
		fields?: SetupIntentPaymentMethodOptionsUsBankAccountFinancialConnectionsFieldPolicy
	}
	Shipping?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ShippingKeySpecifier | (() => undefined | ShippingKeySpecifier)
		fields?: ShippingFieldPolicy
	}
	ShippingCost?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ShippingCostKeySpecifier | (() => undefined | ShippingCostKeySpecifier)
		fields?: ShippingCostFieldPolicy
	}
	ShippingCostTax?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ShippingCostTaxKeySpecifier | (() => undefined | ShippingCostTaxKeySpecifier)
		fields?: ShippingCostTaxFieldPolicy
	}
	ShippingDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ShippingDetailsKeySpecifier | (() => undefined | ShippingDetailsKeySpecifier)
		fields?: ShippingDetailsFieldPolicy
	}
	ShippingRate?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ShippingRateKeySpecifier | (() => undefined | ShippingRateKeySpecifier)
		fields?: ShippingRateFieldPolicy
	}
	ShippingRateDeliveryEstimate?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| ShippingRateDeliveryEstimateKeySpecifier
			| (() => undefined | ShippingRateDeliveryEstimateKeySpecifier)
		fields?: ShippingRateDeliveryEstimateFieldPolicy
	}
	ShippingRateDeliveryEstimateMaximum?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| ShippingRateDeliveryEstimateMaximumKeySpecifier
			| (() => undefined | ShippingRateDeliveryEstimateMaximumKeySpecifier)
		fields?: ShippingRateDeliveryEstimateMaximumFieldPolicy
	}
	ShippingRateDeliveryEstimateMinimum?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| ShippingRateDeliveryEstimateMinimumKeySpecifier
			| (() => undefined | ShippingRateDeliveryEstimateMinimumKeySpecifier)
		fields?: ShippingRateDeliveryEstimateMinimumFieldPolicy
	}
	ShippingRateFixedAmount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ShippingRateFixedAmountKeySpecifier | (() => undefined | ShippingRateFixedAmountKeySpecifier)
		fields?: ShippingRateFixedAmountFieldPolicy
	}
	ShippingRateFixedAmountCurrencyOptions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| ShippingRateFixedAmountCurrencyOptionsKeySpecifier
			| (() => undefined | ShippingRateFixedAmountCurrencyOptionsKeySpecifier)
		fields?: ShippingRateFixedAmountCurrencyOptionsFieldPolicy
	}
	Sofort?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SofortKeySpecifier | (() => undefined | SofortKeySpecifier)
		fields?: SofortFieldPolicy
	}
	Source?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceKeySpecifier | (() => undefined | SourceKeySpecifier)
		fields?: SourceFieldPolicy
	}
	SourceAcssDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceAcssDebitKeySpecifier | (() => undefined | SourceAcssDebitKeySpecifier)
		fields?: SourceAcssDebitFieldPolicy
	}
	SourceAlipay?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceAlipayKeySpecifier | (() => undefined | SourceAlipayKeySpecifier)
		fields?: SourceAlipayFieldPolicy
	}
	SourceAuBecsDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceAuBecsDebitKeySpecifier | (() => undefined | SourceAuBecsDebitKeySpecifier)
		fields?: SourceAuBecsDebitFieldPolicy
	}
	SourceBancontact?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceBancontactKeySpecifier | (() => undefined | SourceBancontactKeySpecifier)
		fields?: SourceBancontactFieldPolicy
	}
	SourceCard?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceCardKeySpecifier | (() => undefined | SourceCardKeySpecifier)
		fields?: SourceCardFieldPolicy
	}
	SourceCardPresent?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceCardPresentKeySpecifier | (() => undefined | SourceCardPresentKeySpecifier)
		fields?: SourceCardPresentFieldPolicy
	}
	SourceEps?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceEpsKeySpecifier | (() => undefined | SourceEpsKeySpecifier)
		fields?: SourceEpsFieldPolicy
	}
	SourceGiropay?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceGiropayKeySpecifier | (() => undefined | SourceGiropayKeySpecifier)
		fields?: SourceGiropayFieldPolicy
	}
	SourceIdeal?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceIdealKeySpecifier | (() => undefined | SourceIdealKeySpecifier)
		fields?: SourceIdealFieldPolicy
	}
	SourceKlarna?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceKlarnaKeySpecifier | (() => undefined | SourceKlarnaKeySpecifier)
		fields?: SourceKlarnaFieldPolicy
	}
	SourceOrder?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceOrderKeySpecifier | (() => undefined | SourceOrderKeySpecifier)
		fields?: SourceOrderFieldPolicy
	}
	SourceP24?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceP24KeySpecifier | (() => undefined | SourceP24KeySpecifier)
		fields?: SourceP24FieldPolicy
	}
	SourceSepaDebit?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceSepaDebitKeySpecifier | (() => undefined | SourceSepaDebitKeySpecifier)
		fields?: SourceSepaDebitFieldPolicy
	}
	SourceSofort?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SourceSofortKeySpecifier | (() => undefined | SourceSofortKeySpecifier)
		fields?: SourceSofortFieldPolicy
	}
	Statistics?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StatisticsKeySpecifier | (() => undefined | StatisticsKeySpecifier)
		fields?: StatisticsFieldPolicy
	}
	StatisticsOverTime?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StatisticsOverTimeKeySpecifier | (() => undefined | StatisticsOverTimeKeySpecifier)
		fields?: StatisticsOverTimeFieldPolicy
	}
	Stats?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StatsKeySpecifier | (() => undefined | StatsKeySpecifier)
		fields?: StatsFieldPolicy
	}
	Stats2D?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | Stats2DKeySpecifier | (() => undefined | Stats2DKeySpecifier)
		fields?: Stats2DFieldPolicy
	}
	StatusDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StatusDetailsKeySpecifier | (() => undefined | StatusDetailsKeySpecifier)
		fields?: StatusDetailsFieldPolicy
	}
	StatusTransitions?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StatusTransitionsKeySpecifier | (() => undefined | StatusTransitionsKeySpecifier)
		fields?: StatusTransitionsFieldPolicy
	}
	StripeAccountController?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StripeAccountControllerKeySpecifier | (() => undefined | StripeAccountControllerKeySpecifier)
		fields?: StripeAccountControllerFieldPolicy
	}
	StripeAddress?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StripeAddressKeySpecifier | (() => undefined | StripeAddressKeySpecifier)
		fields?: StripeAddressFieldPolicy
	}
	StripeEntityId?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StripeEntityIdKeySpecifier | (() => undefined | StripeEntityIdKeySpecifier)
		fields?: StripeEntityIdFieldPolicy
	}
	StripeItem?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StripeItemKeySpecifier | (() => undefined | StripeItemKeySpecifier)
		fields?: StripeItemFieldPolicy
	}
	StripeProduct?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StripeProductKeySpecifier | (() => undefined | StripeProductKeySpecifier)
		fields?: StripeProductFieldPolicy
	}
	StripeReview?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StripeReviewKeySpecifier | (() => undefined | StripeReviewKeySpecifier)
		fields?: StripeReviewFieldPolicy
	}
	StripeSubscription?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | StripeSubscriptionKeySpecifier | (() => undefined | StripeSubscriptionKeySpecifier)
		fields?: StripeSubscriptionFieldPolicy
	}
	SubscriptionAutomaticTax?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SubscriptionAutomaticTaxKeySpecifier | (() => undefined | SubscriptionAutomaticTaxKeySpecifier)
		fields?: SubscriptionAutomaticTaxFieldPolicy
	}
	SubscriptionCancellationDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SubscriptionCancellationDetailsKeySpecifier
			| (() => undefined | SubscriptionCancellationDetailsKeySpecifier)
		fields?: SubscriptionCancellationDetailsFieldPolicy
	}
	SubscriptionData?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SubscriptionDataKeySpecifier | (() => undefined | SubscriptionDataKeySpecifier)
		fields?: SubscriptionDataFieldPolicy
	}
	SubscriptionDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SubscriptionDetailsKeySpecifier | (() => undefined | SubscriptionDetailsKeySpecifier)
		fields?: SubscriptionDetailsFieldPolicy
	}
	SubscriptionInvoiceSettings?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SubscriptionInvoiceSettingsKeySpecifier
			| (() => undefined | SubscriptionInvoiceSettingsKeySpecifier)
		fields?: SubscriptionInvoiceSettingsFieldPolicy
	}
	SubscriptionItem?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SubscriptionItemKeySpecifier | (() => undefined | SubscriptionItemKeySpecifier)
		fields?: SubscriptionItemFieldPolicy
	}
	SubscriptionItemList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SubscriptionItemListKeySpecifier | (() => undefined | SubscriptionItemListKeySpecifier)
		fields?: SubscriptionItemListFieldPolicy
	}
	SubscriptionList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SubscriptionListKeySpecifier | (() => undefined | SubscriptionListKeySpecifier)
		fields?: SubscriptionListFieldPolicy
	}
	SubscriptionSchedule?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SubscriptionScheduleKeySpecifier | (() => undefined | SubscriptionScheduleKeySpecifier)
		fields?: SubscriptionScheduleFieldPolicy
	}
	SubscriptionSearchResults?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SubscriptionSearchResultsKeySpecifier
			| (() => undefined | SubscriptionSearchResultsKeySpecifier)
		fields?: SubscriptionSearchResultsFieldPolicy
	}
	SubscriptionTransferData?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SubscriptionTransferDataKeySpecifier | (() => undefined | SubscriptionTransferDataKeySpecifier)
		fields?: SubscriptionTransferDataFieldPolicy
	}
	SubscriptionTrialSettings?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SubscriptionTrialSettingsKeySpecifier
			| (() => undefined | SubscriptionTrialSettingsKeySpecifier)
		fields?: SubscriptionTrialSettingsFieldPolicy
	}
	SubscriptionTrialSettingsEndBehavior?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| SubscriptionTrialSettingsEndBehaviorKeySpecifier
			| (() => undefined | SubscriptionTrialSettingsEndBehaviorKeySpecifier)
		fields?: SubscriptionTrialSettingsEndBehaviorFieldPolicy
	}
	Tag?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TagKeySpecifier | (() => undefined | TagKeySpecifier)
		fields?: TagFieldPolicy
	}
	Tap?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TapKeySpecifier | (() => undefined | TapKeySpecifier)
		fields?: TapFieldPolicy
	}
	Tax?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TaxKeySpecifier | (() => undefined | TaxKeySpecifier)
		fields?: TaxFieldPolicy
	}
	TaxAmount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TaxAmountKeySpecifier | (() => undefined | TaxAmountKeySpecifier)
		fields?: TaxAmountFieldPolicy
	}
	TaxCode?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TaxCodeKeySpecifier | (() => undefined | TaxCodeKeySpecifier)
		fields?: TaxCodeFieldPolicy
	}
	TaxDeductedAtSource?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TaxDeductedAtSourceKeySpecifier | (() => undefined | TaxDeductedAtSourceKeySpecifier)
		fields?: TaxDeductedAtSourceFieldPolicy
	}
	TaxId?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TaxIdKeySpecifier | (() => undefined | TaxIdKeySpecifier)
		fields?: TaxIdFieldPolicy
	}
	TaxIdList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TaxIdListKeySpecifier | (() => undefined | TaxIdListKeySpecifier)
		fields?: TaxIdListFieldPolicy
	}
	TaxLocation?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TaxLocationKeySpecifier | (() => undefined | TaxLocationKeySpecifier)
		fields?: TaxLocationFieldPolicy
	}
	TaxRate?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TaxRateKeySpecifier | (() => undefined | TaxRateKeySpecifier)
		fields?: TaxRateFieldPolicy
	}
	TestClock?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TestClockKeySpecifier | (() => undefined | TestClockKeySpecifier)
		fields?: TestClockFieldPolicy
	}
	ThreeDSecure?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ThreeDSecureKeySpecifier | (() => undefined | ThreeDSecureKeySpecifier)
		fields?: ThreeDSecureFieldPolicy
	}
	ThresholdReason?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ThresholdReasonKeySpecifier | (() => undefined | ThresholdReasonKeySpecifier)
		fields?: ThresholdReasonFieldPolicy
	}
	ThresholdReasonItemReason?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| ThresholdReasonItemReasonKeySpecifier
			| (() => undefined | ThresholdReasonItemReasonKeySpecifier)
		fields?: ThresholdReasonItemReasonFieldPolicy
	}
	Tier?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TierKeySpecifier | (() => undefined | TierKeySpecifier)
		fields?: TierFieldPolicy
	}
	Topup?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TopupKeySpecifier | (() => undefined | TopupKeySpecifier)
		fields?: TopupFieldPolicy
	}
	TosAcceptance?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TosAcceptanceKeySpecifier | (() => undefined | TosAcceptanceKeySpecifier)
		fields?: TosAcceptanceFieldPolicy
	}
	TotalDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TotalDetailsKeySpecifier | (() => undefined | TotalDetailsKeySpecifier)
		fields?: TotalDetailsFieldPolicy
	}
	TotalDiscountAmount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TotalDiscountAmountKeySpecifier | (() => undefined | TotalDiscountAmountKeySpecifier)
		fields?: TotalDiscountAmountFieldPolicy
	}
	TotalTaxAmount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TotalTaxAmountKeySpecifier | (() => undefined | TotalTaxAmountKeySpecifier)
		fields?: TotalTaxAmountFieldPolicy
	}
	TraceId?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TraceIdKeySpecifier | (() => undefined | TraceIdKeySpecifier)
		fields?: TraceIdFieldPolicy
	}
	Transfer?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TransferKeySpecifier | (() => undefined | TransferKeySpecifier)
		fields?: TransferFieldPolicy
	}
	TransferData?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TransferDataKeySpecifier | (() => undefined | TransferDataKeySpecifier)
		fields?: TransferDataFieldPolicy
	}
	TransferReversal?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TransferReversalKeySpecifier | (() => undefined | TransferReversalKeySpecifier)
		fields?: TransferReversalFieldPolicy
	}
	TransferReversalList?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TransferReversalListKeySpecifier | (() => undefined | TransferReversalListKeySpecifier)
		fields?: TransferReversalListFieldPolicy
	}
	TransformQuantity?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TransformQuantityKeySpecifier | (() => undefined | TransformQuantityKeySpecifier)
		fields?: TransformQuantityFieldPolicy
	}
	TransformUsage?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | TransformUsageKeySpecifier | (() => undefined | TransformUsageKeySpecifier)
		fields?: TransformUsageFieldPolicy
	}
	UpcomingInvoice?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | UpcomingInvoiceKeySpecifier | (() => undefined | UpcomingInvoiceKeySpecifier)
		fields?: UpcomingInvoiceFieldPolicy
	}
	UsBankAccount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | UsBankAccountKeySpecifier | (() => undefined | UsBankAccountKeySpecifier)
		fields?: UsBankAccountFieldPolicy
	}
	UsBankAccountNetworks?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | UsBankAccountNetworksKeySpecifier | (() => undefined | UsBankAccountNetworksKeySpecifier)
		fields?: UsBankAccountNetworksFieldPolicy
	}
	UsBankAccountStatusDetails?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| UsBankAccountStatusDetailsKeySpecifier
			| (() => undefined | UsBankAccountStatusDetailsKeySpecifier)
		fields?: UsBankAccountStatusDetailsFieldPolicy
	}
	UsBankAccountStatusDetailsBlocked?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| UsBankAccountStatusDetailsBlockedKeySpecifier
			| (() => undefined | UsBankAccountStatusDetailsBlockedKeySpecifier)
		fields?: UsBankAccountStatusDetailsBlockedFieldPolicy
	}
	User?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | UserKeySpecifier | (() => undefined | UserKeySpecifier)
		fields?: UserFieldPolicy
	}
	UserWithRole?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | UserWithRoleKeySpecifier | (() => undefined | UserWithRoleKeySpecifier)
		fields?: UserWithRoleFieldPolicy
	}
	Verification?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | VerificationKeySpecifier | (() => undefined | VerificationKeySpecifier)
		fields?: VerificationFieldPolicy
	}
	VerificationDocument?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | VerificationDocumentKeySpecifier | (() => undefined | VerificationDocumentKeySpecifier)
		fields?: VerificationDocumentFieldPolicy
	}
	Webhook?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | WebhookKeySpecifier | (() => undefined | WebhookKeySpecifier)
		fields?: WebhookFieldPolicy
	}
	Wechat?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | WechatKeySpecifier | (() => undefined | WechatKeySpecifier)
		fields?: WechatFieldPolicy
	}
}
export type TypedTypePolicies = StrictTypedTypePolicies & TypePolicies
