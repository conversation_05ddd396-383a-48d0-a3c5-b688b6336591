export interface PossibleTypesResultData {
	possibleTypes: {
		[key: string]: string[]
	}
}
const result: PossibleTypesResultData = {
	possibleTypes: {
		AccountUnion: ['Account', 'StripeEntityId'],
		ApplicationFeeUnion: ['ApplicationFee', 'StripeEntityId'],
		ApplicationUnion: ['Application', 'StripeEntityId'],
		AuditEvent: [
			'EntityCreated',
			'EntityDeleted',
			'EntityDisabled',
			'EntityEnabled',
			'EntityRestored',
			'EntityUpdated',
			'GenericAuditEvent',
		],
		BalanceTransactionSourceUnion: [
			'ApplicationFee',
			'Charge',
			'ConnectCollectionTransfer',
			'Dispute',
			'FeeRefund',
			'IssuingAuthorization',
			'IssuingDispute',
			'IssuingTransaction',
			'Payout',
			'Refund',
			'ReserveTransaction',
			'StripeEntityId',
			'TaxDeductedAtSource',
			'Topup',
			'Transfer',
			'TransferReversal',
		],
		BalanceTransactionUnion: ['BalanceTransaction', 'StripeEntityId'],
		ChargeUnion: ['Charge', 'StripeEntityId'],
		CouponUnion: ['Coupon', 'StripeEntityId'],
		CreditBalanceTransactionUnion: ['CreditBalanceTransaction', 'StripeEntityId'],
		CreditNoteUnion: ['CreditNote', 'StripeEntityId'],
		CustomerBalanceTransactionUnion: ['CustomerBalanceTransaction', 'StripeEntityId'],
		CustomerSource: ['Account', 'BankAccount', 'Card', 'Source'],
		CustomerSourceUnion: ['Account', 'BankAccount', 'Card', 'Source', 'StripeEntityId'],
		CustomerUnion: ['Customer', 'StripeEntityId'],
		DestinationUnion: ['BankAccount', 'Card', 'StripeEntityId'],
		DiscountUnion: ['Discount', 'StripeEntityId'],
		EPCComponentsUnion: [
			'GIAI96Components',
			'GID96Components',
			'GRAI96Components',
			'ISO15963Components',
			'SGLN96Components',
			'SGTIN96Components',
			'SSCC96Components',
		],
		EntityEvent: [
			'EntityCreated',
			'EntityDeleted',
			'EntityDisabled',
			'EntityEnabled',
			'EntityRestored',
			'EntityUpdated',
			'GenericAuditEvent',
		],
		ExternalAccountsUnion: ['BankAccount', 'Card'],
		FileUnion: ['File', 'StripeEntityId'],
		InvoiceUnion: ['Invoice', 'StripeEntityId'],
		MandateUnion: ['Mandate', 'StripeEntityId'],
		PaymentIntentUnion: ['PaymentIntent', 'StripeEntityId'],
		PaymentMethodUnion: ['PaymentMethod', 'StripeEntityId'],
		PayoutUnion: ['Payout', 'StripeEntityId'],
		PlanUnion: ['Plan', 'StripeEntityId'],
		PriceUnion: ['Price', 'StripeEntityId'],
		ProductUnion: ['StripeEntityId', 'StripeProduct'],
		PromotionCodeUnion: ['PromotionCode', 'StripeEntityId'],
		QuoteUnion: ['Quote', 'StripeEntityId'],
		RefundUnion: ['Refund', 'StripeEntityId'],
		ReviewUnion: ['StripeEntityId', 'StripeReview'],
		SetupAttemptUnion: ['SetupAttempt', 'StripeEntityId'],
		SetupIntentUnion: ['SetupIntent', 'StripeEntityId'],
		SubscriptionScheduleUnion: ['StripeEntityId', 'SubscriptionSchedule'],
		SubscriptionUnion: ['StripeEntityId', 'StripeSubscription'],
		TaxCodeUnion: ['StripeEntityId', 'TaxCode'],
		TaxRateUnion: ['StripeEntityId', 'TaxRate'],
		TestClockUnion: ['StripeEntityId', 'TestClock'],
		TransferReversalUnion: ['StripeEntityId', 'TransferReversal'],
		TransferUnion: ['StripeEntityId', 'Transfer'],
	},
}
export default result
