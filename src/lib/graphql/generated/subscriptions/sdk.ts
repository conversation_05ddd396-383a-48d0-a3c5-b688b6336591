import client from '$lib/graphql/subscriptions-apollo-client'
import type { SubscriptionOptions } from '@apollo/client'
import { readable } from 'svelte/store'
import type { Readable } from 'svelte/store'
import gql from 'graphql-tag'
export type Maybe<T> = T | null
export type InputMaybe<T> = Maybe<T>
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] }
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> }
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> }
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never }
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never }
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
	ID: { input: string; output: string }
	String: { input: string; output: string }
	Boolean: { input: boolean; output: boolean }
	Int: { input: number; output: number }
	Float: { input: number; output: number }
	DateTime: { input: any; output: any }
	EmailAddress: { input: any; output: any }
	IP: { input: any; output: any }
	JSON: { input: any; output: any }
	JSONObject: { input: any; output: any }
	ObjectId: { input: any; output: any }
	PhoneNumber: { input: any; output: any }
	TimeZone: { input: any; output: any }
	Timestamp: { input: any; output: any }
}

export type Address = {
	__typename?: 'Address'
	country: Scalars['String']['output']
	formatted?: Maybe<Scalars['String']['output']>
	locality: Scalars['String']['output']
	postalCode: Scalars['String']['output']
	region: Scalars['String']['output']
	streetAddress: Scalars['String']['output']
}

export type ChangeStreamOptionsInput = {
	batchSize?: InputMaybe<Scalars['Float']['input']>
	collation?: InputMaybe<CollationOptionsInput>
	fullDocument?: InputMaybe<Scalars['String']['input']>
	maxAwaitTimeMS?: InputMaybe<Scalars['Float']['input']>
	resumeAfter?: InputMaybe<Scalars['String']['input']>
	startAfter?: InputMaybe<Scalars['String']['input']>
	startAtOperationTime?: InputMaybe<Scalars['Timestamp']['input']>
}

export type CollationOptionsInput = {
	alternate?: InputMaybe<Scalars['String']['input']>
	backwards?: InputMaybe<Scalars['Boolean']['input']>
	caseFirst?: InputMaybe<Scalars['String']['input']>
	caseLevel?: InputMaybe<Scalars['Boolean']['input']>
	locale: Scalars['String']['input']
	maxVariable?: InputMaybe<Scalars['String']['input']>
	normalization?: InputMaybe<Scalars['Boolean']['input']>
	numericOrdering?: InputMaybe<Scalars['Boolean']['input']>
	strength?: InputMaybe<Scalars['Float']['input']>
}

export type Comment = {
	__typename?: 'Comment'
	/** Unique identifier for the object. */
	_id: Scalars['String']['output']
	/** The body of the comment. */
	body: Scalars['String']['output']
	/** Time at which the object was created. */
	createdAt: Scalars['DateTime']['output']
	/** Time at which the object was deleted. */
	deletedAt?: Maybe<Scalars['DateTime']['output']>
	/** The Entity Type of the parent. */
	parent: Scalars['String']['output']
	/** The ID of the parent. */
	parentId: Scalars['String']['output']
	/** Time at which the object was last updated. */
	updatedAt: Scalars['DateTime']['output']
	/** The ID of the user who created the comment. */
	userId: Scalars['String']['output']
}

export type EmailTemplate = {
	__typename?: 'EmailTemplate'
	name: Scalars['String']['output']
	provider: EmailTemplateProvider
	type: Scalars['String']['output']
}

export type EmailTemplateInput = {
	name: Scalars['String']['input']
	provider: EmailTemplateProvider
	type: Scalars['String']['input']
}

/** The supported email template providers. */
export enum EmailTemplateProvider {
	Mailgun = 'Mailgun',
	Mandrill = 'Mandrill',
	Sendgrid = 'Sendgrid',
}

export type EntityRelations = {
	__typename?: 'EntityRelations'
	/** The entity ids that relate to it. */
	entityIds: Array<Scalars['String']['output']>
	/** The entity id. */
	id: Scalars['String']['output']
	/** The entity type. */
	type: Scalars['String']['output']
}

export type FilterArgs = {
	after?: InputMaybe<Scalars['String']['input']>
	before?: InputMaybe<Scalars['String']['input']>
	limit?: Scalars['Float']['input']
	offset?: InputMaybe<Scalars['Float']['input']>
	sort?: InputMaybe<Scalars['JSONObject']['input']>
	where?: InputMaybe<Scalars['JSONObject']['input']>
}

export type Fingerprint = {
	__typename?: 'Fingerprint'
	/** Iso 3166 Alpha 2 country code of the request. */
	country: Iso3166Alpha2
	/** IP address of the request. */
	ip: Scalars['IP']['output']
	/** Referer of the request. */
	referer: Scalars['String']['output']
	/** Unique identifier for the request. */
	requestId: Scalars['String']['output']
	/** User agent of the request. */
	userAgent: Scalars['String']['output']
	/** Unique identifier for a User. */
	userId?: Maybe<Scalars['String']['output']>
}

/** ISO3166 Alpha 2 country code */
export enum Iso3166Alpha2 {
	Ad = 'AD',
	Ae = 'AE',
	Af = 'AF',
	Ag = 'AG',
	Ai = 'AI',
	Al = 'AL',
	Am = 'AM',
	Ao = 'AO',
	Aq = 'AQ',
	Ar = 'AR',
	As = 'AS',
	At = 'AT',
	Au = 'AU',
	Aw = 'AW',
	Ax = 'AX',
	Az = 'AZ',
	Ba = 'BA',
	Bb = 'BB',
	Bd = 'BD',
	Be = 'BE',
	Bf = 'BF',
	Bg = 'BG',
	Bh = 'BH',
	Bi = 'BI',
	Bj = 'BJ',
	Bl = 'BL',
	Bm = 'BM',
	Bn = 'BN',
	Bo = 'BO',
	Bq = 'BQ',
	Br = 'BR',
	Bs = 'BS',
	Bt = 'BT',
	Bv = 'BV',
	Bw = 'BW',
	By = 'BY',
	Bz = 'BZ',
	Ca = 'CA',
	Cc = 'CC',
	Cd = 'CD',
	Cf = 'CF',
	Cg = 'CG',
	Ch = 'CH',
	Ci = 'CI',
	Ck = 'CK',
	Cl = 'CL',
	Cm = 'CM',
	Cn = 'CN',
	Co = 'CO',
	Cr = 'CR',
	Cu = 'CU',
	Cv = 'CV',
	Cw = 'CW',
	Cx = 'CX',
	Cy = 'CY',
	Cz = 'CZ',
	De = 'DE',
	Dj = 'DJ',
	Dk = 'DK',
	Dm = 'DM',
	Do = 'DO',
	Dz = 'DZ',
	Ec = 'EC',
	Ee = 'EE',
	Eg = 'EG',
	Eh = 'EH',
	Er = 'ER',
	Es = 'ES',
	Et = 'ET',
	Eu = 'EU',
	Fi = 'FI',
	Fj = 'FJ',
	Fk = 'FK',
	Fm = 'FM',
	Fo = 'FO',
	Fr = 'FR',
	Ga = 'GA',
	Gb = 'GB',
	Gd = 'GD',
	Ge = 'GE',
	Gf = 'GF',
	Gg = 'GG',
	Gh = 'GH',
	Gi = 'GI',
	Gl = 'GL',
	Gm = 'GM',
	Gn = 'GN',
	Gp = 'GP',
	Gq = 'GQ',
	Gr = 'GR',
	Gs = 'GS',
	Gt = 'GT',
	Gu = 'GU',
	Gw = 'GW',
	Gy = 'GY',
	Hk = 'HK',
	Hm = 'HM',
	Hn = 'HN',
	Hr = 'HR',
	Ht = 'HT',
	Hu = 'HU',
	Id = 'ID',
	Ie = 'IE',
	Il = 'IL',
	Im = 'IM',
	In = 'IN',
	Io = 'IO',
	Iq = 'IQ',
	Ir = 'IR',
	Is = 'IS',
	It = 'IT',
	Je = 'JE',
	Jm = 'JM',
	Jo = 'JO',
	Jp = 'JP',
	Ke = 'KE',
	Kg = 'KG',
	Kh = 'KH',
	Ki = 'KI',
	Km = 'KM',
	Kn = 'KN',
	Kp = 'KP',
	Kr = 'KR',
	Kw = 'KW',
	Ky = 'KY',
	Kz = 'KZ',
	La = 'LA',
	Lb = 'LB',
	Lc = 'LC',
	Li = 'LI',
	Lk = 'LK',
	Lr = 'LR',
	Ls = 'LS',
	Lt = 'LT',
	Lu = 'LU',
	Lv = 'LV',
	Ly = 'LY',
	Ma = 'MA',
	Mc = 'MC',
	Md = 'MD',
	Me = 'ME',
	Mf = 'MF',
	Mg = 'MG',
	Mh = 'MH',
	Mk = 'MK',
	Ml = 'ML',
	Mm = 'MM',
	Mn = 'MN',
	Mo = 'MO',
	Mp = 'MP',
	Mq = 'MQ',
	Mr = 'MR',
	Ms = 'MS',
	Mt = 'MT',
	Mu = 'MU',
	Mv = 'MV',
	Mw = 'MW',
	Mx = 'MX',
	My = 'MY',
	Mz = 'MZ',
	Na = 'NA',
	Nc = 'NC',
	Ne = 'NE',
	Nf = 'NF',
	Ng = 'NG',
	Ni = 'NI',
	Nl = 'NL',
	No = 'NO',
	Np = 'NP',
	Nr = 'NR',
	Nu = 'NU',
	Nz = 'NZ',
	Om = 'OM',
	Pa = 'PA',
	Pe = 'PE',
	Pf = 'PF',
	Pg = 'PG',
	Ph = 'PH',
	Pk = 'PK',
	Pl = 'PL',
	Pm = 'PM',
	Pn = 'PN',
	Pr = 'PR',
	Ps = 'PS',
	Pt = 'PT',
	Pw = 'PW',
	Py = 'PY',
	Qa = 'QA',
	Rc = 'RC',
	Re = 'RE',
	Ro = 'RO',
	Rs = 'RS',
	Ru = 'RU',
	Rw = 'RW',
	Sa = 'SA',
	Sb = 'SB',
	Sc = 'SC',
	Sd = 'SD',
	Se = 'SE',
	Sg = 'SG',
	Sh = 'SH',
	Si = 'SI',
	Sj = 'SJ',
	Sk = 'SK',
	Sl = 'SL',
	Sm = 'SM',
	Sn = 'SN',
	So = 'SO',
	Sr = 'SR',
	Ss = 'SS',
	St = 'ST',
	Sv = 'SV',
	Sx = 'SX',
	Sy = 'SY',
	Sz = 'SZ',
	Tc = 'TC',
	Td = 'TD',
	Tf = 'TF',
	Tg = 'TG',
	Th = 'TH',
	Tj = 'TJ',
	Tk = 'TK',
	Tl = 'TL',
	Tm = 'TM',
	Tn = 'TN',
	To = 'TO',
	Tr = 'TR',
	Tt = 'TT',
	Tv = 'TV',
	Tw = 'TW',
	Tz = 'TZ',
	Ua = 'UA',
	Ug = 'UG',
	Um = 'UM',
	Us = 'US',
	Uy = 'UY',
	Uz = 'UZ',
	Va = 'VA',
	Vc = 'VC',
	Ve = 'VE',
	Vg = 'VG',
	Vi = 'VI',
	Vn = 'VN',
	Vu = 'VU',
	Wf = 'WF',
	Ws = 'WS',
	Ww = 'WW',
	Xk = 'XK',
	Xx = 'XX',
	Ye = 'YE',
	Yt = 'YT',
	Za = 'ZA',
	Zm = 'ZM',
	Zw = 'ZW',
}

export type Label = {
	__typename?: 'Label'
	/** Unique identifier for the object. */
	_id: Scalars['String']['output']
	/** Time at which the object was created. */
	createdAt: Scalars['DateTime']['output']
	/** Time at which the object was deleted. */
	deletedAt?: Maybe<Scalars['DateTime']['output']>
	/** The name of the label. */
	name: Scalars['String']['output']
	/** The type of the label. */
	type: Scalars['String']['output']
	/** Time at which the object was last updated. */
	updatedAt: Scalars['DateTime']['output']
}

export type MarkAreaData = {
	__typename?: 'MarkAreaData'
	type?: Maybe<MarkerStatisticType>
	valueDim?: Maybe<Scalars['String']['output']>
	/** When using statistic method with type. valueIndex and valueDim can be specify which dim the statistic is used on. */
	valueIndex?: Maybe<Scalars['Float']['output']>
	xAxis?: Maybe<Scalars['Float']['output']>
	yAxis?: Maybe<Scalars['Float']['output']>
}

export type MarkLineData = {
	__typename?: 'MarkLineData'
	type?: Maybe<MarkerStatisticType>
	valueDim?: Maybe<Scalars['String']['output']>
	/** When using statistic method with type. valueIndex and valueDim can be specify which dim the statistic is used on. */
	valueIndex?: Maybe<Scalars['Float']['output']>
	xAxis?: Maybe<Scalars['Float']['output']>
	yAxis?: Maybe<Scalars['Float']['output']>
}

export type MarkerPosition = {
	__typename?: 'MarkerPosition'
	angleAxis?: Maybe<Scalars['String']['output']>
	/** Coord on any coordinate system */
	coord?: Maybe<Array<Scalars['String']['output']>>
	radiusAxis?: Maybe<Scalars['String']['output']>
	type?: Maybe<MarkerStatisticType>
	/** Value to be displayed as label. Totally optional */
	value?: Maybe<Scalars['String']['output']>
	valueDim?: Maybe<Scalars['String']['output']>
	/** When using statistic method with type. valueIndex and valueDim can be specify which dim the statistic is used on. */
	valueIndex?: Maybe<Scalars['Float']['output']>
	x?: Maybe<Scalars['String']['output']>
	xAxis?: Maybe<Scalars['Float']['output']>
	y?: Maybe<Scalars['String']['output']>
	yAxis?: Maybe<Scalars['Float']['output']>
}

/** The type of statistic */
export enum MarkerStatisticType {
	Average = 'average',
	Max = 'max',
	Median = 'median',
	Min = 'min',
}

export type MyReaction = {
	__typename?: 'MyReaction'
	/** Unique identifier for the object. */
	_id: Scalars['String']['output']
	/** The reaction type. */
	type: ReactionType
}

export type NotificationSubscriptionResult = {
	__typename?: 'NotificationSubscriptionResult'
	/** Unique identifier for the object. */
	_id?: Maybe<Scalars['String']['output']>
	/** Time at which the object was created. */
	createdAt?: Maybe<Scalars['DateTime']['output']>
	/** Additional data for the notification. */
	data?: Maybe<Scalars['JSONObject']['output']>
	/** Time at which the object was deleted. */
	deletedAt?: Maybe<Scalars['DateTime']['output']>
	/** The message of the notification. */
	message?: Maybe<Scalars['String']['output']>
	/** The Entity Type of the parent. */
	parent?: Maybe<Scalars['String']['output']>
	/** The ID of the parent. */
	parentId?: Maybe<Scalars['String']['output']>
	/** The title of the notification. */
	title?: Maybe<Scalars['String']['output']>
	/** The type of notification. */
	type?: Maybe<NotificationType>
	/** Time at which the object was last updated. */
	updatedAt?: Maybe<Scalars['DateTime']['output']>
	/**  The ID of the user the notification is for. */
	userId?: Maybe<Scalars['String']['output']>
	/** Whether the notification has been viewed. */
	viewed?: Maybe<Scalars['Boolean']['output']>
}

export enum NotificationType {
	Alert = 'ALERT',
	Block = 'BLOCK',
	FriendRequest = 'FRIEND_REQUEST',
	Invitation = 'INVITATION',
	Message = 'MESSAGE',
}

export type OrganizationSubscriptionResult = {
	__typename?: 'OrganizationSubscriptionResult'
	/** Unique identifier for the object. */
	_id?: Maybe<Scalars['ObjectId']['output']>
	billing?: Maybe<Scalars['EmailAddress']['output']>
	billingVerified?: Maybe<Scalars['Boolean']['output']>
	contactEmail?: Maybe<Scalars['EmailAddress']['output']>
	contactEmailVerified?: Maybe<Scalars['Boolean']['output']>
	contactPhoneNumber?: Maybe<Scalars['PhoneNumber']['output']>
	contactPhoneNumberVerified?: Maybe<Scalars['Boolean']['output']>
	/** The date when the notification was created. */
	createdAt?: Maybe<Scalars['DateTime']['output']>
	customDomains?: Maybe<Array<Scalars['String']['output']>>
	/** The date when the notification was deleted, if applicable. */
	deletedAt?: Maybe<Scalars['DateTime']['output']>
	description?: Maybe<Scalars['String']['output']>
	emailTemplates?: Maybe<Array<EmailTemplate>>
	logo?: Maybe<Scalars['String']['output']>
	name?: Maybe<Scalars['String']['output']>
	tag?: Maybe<Scalars['String']['output']>
	template?: Maybe<OrganizationTemplate>
	/** The date when the notification was last updated. */
	updatedAt?: Maybe<Scalars['DateTime']['output']>
	website?: Maybe<Scalars['String']['output']>
}

export type OrganizationTemplate = {
	__typename?: 'OrganizationTemplate'
	backgroundColor?: Maybe<Scalars['String']['output']>
	backgroundImageUrl?: Maybe<Scalars['String']['output']>
	footerMessage?: Maybe<Scalars['String']['output']>
	primaryColor?: Maybe<Scalars['String']['output']>
	primaryTextColor?: Maybe<Scalars['String']['output']>
	secondaryColor?: Maybe<Scalars['String']['output']>
	secondaryTextColor?: Maybe<Scalars['String']['output']>
	tertiaryColor?: Maybe<Scalars['String']['output']>
	tertiaryTextColor?: Maybe<Scalars['String']['output']>
	textColor?: Maybe<Scalars['String']['output']>
	zendeskKey?: Maybe<Scalars['String']['output']>
}

export type Percentiles = {
	__typename?: 'Percentiles'
	p25: Scalars['Float']['output']
	p50: Scalars['Float']['output']
	p75: Scalars['Float']['output']
}

export type Query = {
	__typename?: 'Query'
	health: Scalars['String']['output']
}

export type ReactionCount = {
	__typename?: 'ReactionCount'
	/** The count of the reaction. */
	count: Scalars['Float']['output']
	/** The reaction type. */
	type: ReactionType
}

export enum ReactionType {
	Angry = 'angry',
	Annoyed = 'annoyed',
	Confused = 'confused',
	Dislike = 'dislike',
	Excited = 'excited',
	Happpy = 'happpy',
	Laugh = 'laugh',
	Like = 'like',
	Love = 'love',
	Sad = 'sad',
	Wishful = 'wishful',
}

export type Review = {
	__typename?: 'Review'
	/** Unique identifier for the object. */
	_id: Scalars['String']['output']
	/** The body of the review. */
	body?: Maybe<Scalars['String']['output']>
	/** Time at which the object was created. */
	createdAt: Scalars['DateTime']['output']
	/** Time at which the object was deleted. */
	deletedAt?: Maybe<Scalars['DateTime']['output']>
	/** The Entity Type of the parent. */
	parent: Scalars['String']['output']
	/** The ID of the parent. */
	parentId: Scalars['String']['output']
	/** The review status. */
	review?: Maybe<ReviewStatus>
	/** Time at which the object was last updated. */
	updatedAt: Scalars['DateTime']['output']
	/** The ID of the user who created the review. */
	userId?: Maybe<Scalars['String']['output']>
}

export enum ReviewStatus {
	Approved = 'approved',
	Dismissed = 'dismissed',
}

export type Stats2D = {
	__typename?: 'Stats2D'
	x: Array<Scalars['String']['output']>
	y: Array<Scalars['Float']['output']>
}

export type Subscription = {
	__typename?: 'Subscription'
	subscribeNotifications: NotificationSubscriptionResult
	subscribeOrganizations: OrganizationSubscriptionResult
	subscribeUsers: UserSubscriptionResult
}

export type SubscriptionSubscribeNotificationsArgs = {
	filter?: InputMaybe<FilterArgs>
	options?: InputMaybe<ChangeStreamOptionsInput>
}

export type SubscriptionSubscribeOrganizationsArgs = {
	filter?: InputMaybe<FilterArgs>
	options?: InputMaybe<ChangeStreamOptionsInput>
}

export type SubscriptionSubscribeUsersArgs = {
	filter?: InputMaybe<FilterArgs>
	options?: InputMaybe<ChangeStreamOptionsInput>
}

export type User = {
	__typename?: 'User'
	/** Unique identifier for the object. */
	_id: Scalars['ObjectId']['output']
	additionalEmail?: Maybe<Scalars['EmailAddress']['output']>
	additionalEmailVerified?: Maybe<Scalars['Boolean']['output']>
	address?: Maybe<Address>
	birthdate?: Maybe<Scalars['DateTime']['output']>
	contactEmail?: Maybe<Scalars['EmailAddress']['output']>
	contactEmailVerified?: Maybe<Scalars['Boolean']['output']>
	/** The date when the notification was created. */
	createdAt: Scalars['DateTime']['output']
	/** The date when the notification was deleted, if applicable. */
	deletedAt?: Maybe<Scalars['DateTime']['output']>
	email?: Maybe<Scalars['EmailAddress']['output']>
	emailVerified?: Maybe<Scalars['Boolean']['output']>
	familyName?: Maybe<Scalars['String']['output']>
	gender?: Maybe<Scalars['String']['output']>
	givenName?: Maybe<Scalars['String']['output']>
	locale?: Maybe<Scalars['String']['output']>
	middleName?: Maybe<Scalars['String']['output']>
	name?: Maybe<Scalars['String']['output']>
	nickname?: Maybe<Scalars['String']['output']>
	phoneNumber?: Maybe<Scalars['PhoneNumber']['output']>
	phoneNumberVerified?: Maybe<Scalars['Boolean']['output']>
	picture?: Maybe<Scalars['String']['output']>
	recoveryEmail?: Maybe<Scalars['EmailAddress']['output']>
	recoveryEmailVerified?: Maybe<Scalars['Boolean']['output']>
	specialBadge?: Maybe<UserSpecialBadge>
	/** The date when the notification was last updated. */
	updatedAt: Scalars['DateTime']['output']
	website?: Maybe<Scalars['String']['output']>
	zoneinfo?: Maybe<Scalars['TimeZone']['output']>
}

/** The supported User special badges. */
export enum UserSpecialBadge {
	Demo = 'Demo',
	Press = 'Press',
	Vip = 'VIP',
}

export type UserSubscriptionResult = {
	__typename?: 'UserSubscriptionResult'
	/** Unique identifier for the object. */
	_id?: Maybe<Scalars['ObjectId']['output']>
	additionalEmail?: Maybe<Scalars['EmailAddress']['output']>
	additionalEmailVerified?: Maybe<Scalars['Boolean']['output']>
	address?: Maybe<Address>
	birthdate?: Maybe<Scalars['DateTime']['output']>
	contactEmail?: Maybe<Scalars['EmailAddress']['output']>
	contactEmailVerified?: Maybe<Scalars['Boolean']['output']>
	/** The date when the notification was created. */
	createdAt?: Maybe<Scalars['DateTime']['output']>
	/** The date when the notification was deleted, if applicable. */
	deletedAt?: Maybe<Scalars['DateTime']['output']>
	email?: Maybe<Scalars['EmailAddress']['output']>
	emailVerified?: Maybe<Scalars['Boolean']['output']>
	familyName?: Maybe<Scalars['String']['output']>
	gender?: Maybe<Scalars['String']['output']>
	givenName?: Maybe<Scalars['String']['output']>
	locale?: Maybe<Scalars['String']['output']>
	middleName?: Maybe<Scalars['String']['output']>
	name?: Maybe<Scalars['String']['output']>
	nickname?: Maybe<Scalars['String']['output']>
	phoneNumber?: Maybe<Scalars['PhoneNumber']['output']>
	phoneNumberVerified?: Maybe<Scalars['Boolean']['output']>
	picture?: Maybe<Scalars['String']['output']>
	recoveryEmail?: Maybe<Scalars['EmailAddress']['output']>
	recoveryEmailVerified?: Maybe<Scalars['Boolean']['output']>
	specialBadge?: Maybe<UserSpecialBadge>
	/** The date when the notification was last updated. */
	updatedAt?: Maybe<Scalars['DateTime']['output']>
	website?: Maybe<Scalars['String']['output']>
	zoneinfo?: Maybe<Scalars['TimeZone']['output']>
}

export type Webhook = {
	__typename?: 'Webhook'
	/** Unique identifier for the object. */
	_id: Scalars['String']['output']
	active: Scalars['Boolean']['output']
	/** Time at which the object was created. */
	createdAt: Scalars['DateTime']['output']
	/** Time at which the object was deleted. */
	deletedAt?: Maybe<Scalars['DateTime']['output']>
	/** The Entity Type of the parent. */
	parent?: Maybe<Scalars['String']['output']>
	/** The ID of the parent. */
	parentId?: Maybe<Scalars['String']['output']>
	secret?: Maybe<Scalars['String']['output']>
	/** ['user.create', 'user.update', 'user.delete', 'organization.*'] */
	subscribeTo: Array<Scalars['String']['output']>
	/** Time at which the object was last updated. */
	updatedAt: Scalars['DateTime']['output']
	url: Scalars['String']['output']
}

export type SubscribeNotificationsSubscriptionVariables = Exact<{
	filter?: InputMaybe<FilterArgs>
	options?: InputMaybe<ChangeStreamOptionsInput>
}>

export type SubscribeNotificationsSubscription = {
	__typename?: 'Subscription'
	subscribeNotifications: { __typename?: 'NotificationSubscriptionResult'; _id?: string | null }
}

export type SubscribeUsersSubscriptionVariables = Exact<{
	filter?: InputMaybe<FilterArgs>
	options?: InputMaybe<ChangeStreamOptionsInput>
}>

export type SubscribeUsersSubscription = {
	__typename?: 'Subscription'
	subscribeUsers: { __typename?: 'UserSubscriptionResult'; _id?: any | null; name?: string | null }
}

export const SubscribeNotificationsDoc = gql`
	subscription SubscribeNotifications($filter: FilterArgs, $options: ChangeStreamOptionsInput) {
		subscribeNotifications(filter: $filter, options: $options) {
			_id
		}
	}
`
export const SubscribeUsersDoc = gql`
	subscription SubscribeUsers($filter: FilterArgs, $options: ChangeStreamOptionsInput) {
		subscribeUsers(filter: $filter, options: $options) {
			_id
			name
		}
	}
`
export const SubscribeNotifications = (
	options: Omit<SubscriptionOptions<SubscribeNotificationsSubscriptionVariables>, 'query'>,
) => {
	const q = client.subscribe<SubscribeNotificationsSubscription, SubscribeNotificationsSubscriptionVariables>({
		query: SubscribeNotificationsDoc,
		...options,
	})
	return q
}
export const SubscribeUsers = (options: Omit<SubscriptionOptions<SubscribeUsersSubscriptionVariables>, 'query'>) => {
	const q = client.subscribe<SubscribeUsersSubscription, SubscribeUsersSubscriptionVariables>({
		query: SubscribeUsersDoc,
		...options,
	})
	return q
}
