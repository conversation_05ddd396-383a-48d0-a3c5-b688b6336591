import type { FieldPolicy, FieldReadFunction, TypePolicies, TypePolicy } from '@apollo/client/cache'
export type AddressKeySpecifier = (
	| 'country'
	| 'formatted'
	| 'locality'
	| 'postalCode'
	| 'region'
	| 'streetAddress'
	| AddressKeySpecifier
)[]
export type AddressFieldPolicy = {
	country?: FieldPolicy<any> | FieldReadFunction<any>
	formatted?: FieldPolicy<any> | FieldReadFunction<any>
	locality?: FieldPolicy<any> | FieldReadFunction<any>
	postalCode?: FieldPolicy<any> | FieldReadFunction<any>
	region?: FieldPolicy<any> | FieldReadFunction<any>
	streetAddress?: FieldPolicy<any> | FieldReadFunction<any>
}
export type CommentKeySpecifier = (
	| '_id'
	| 'body'
	| 'createdAt'
	| 'deletedAt'
	| 'parent'
	| 'parentId'
	| 'updatedAt'
	| 'userId'
	| CommentKeySpecifier
)[]
export type CommentFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	body?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
	parentId?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EmailTemplateKeySpecifier = ('name' | 'provider' | 'type' | EmailTemplateKeySpecifier)[]
export type EmailTemplateFieldPolicy = {
	name?: FieldPolicy<any> | FieldReadFunction<any>
	provider?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type EntityRelationsKeySpecifier = ('entityIds' | 'id' | 'type' | EntityRelationsKeySpecifier)[]
export type EntityRelationsFieldPolicy = {
	entityIds?: FieldPolicy<any> | FieldReadFunction<any>
	id?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type FingerprintKeySpecifier = (
	| 'country'
	| 'ip'
	| 'referer'
	| 'requestId'
	| 'userAgent'
	| 'userId'
	| FingerprintKeySpecifier
)[]
export type FingerprintFieldPolicy = {
	country?: FieldPolicy<any> | FieldReadFunction<any>
	ip?: FieldPolicy<any> | FieldReadFunction<any>
	referer?: FieldPolicy<any> | FieldReadFunction<any>
	requestId?: FieldPolicy<any> | FieldReadFunction<any>
	userAgent?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
}
export type LabelKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'deletedAt'
	| 'name'
	| 'type'
	| 'updatedAt'
	| LabelKeySpecifier
)[]
export type LabelFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MarkAreaDataKeySpecifier = (
	| 'type'
	| 'valueDim'
	| 'valueIndex'
	| 'xAxis'
	| 'yAxis'
	| MarkAreaDataKeySpecifier
)[]
export type MarkAreaDataFieldPolicy = {
	type?: FieldPolicy<any> | FieldReadFunction<any>
	valueDim?: FieldPolicy<any> | FieldReadFunction<any>
	valueIndex?: FieldPolicy<any> | FieldReadFunction<any>
	xAxis?: FieldPolicy<any> | FieldReadFunction<any>
	yAxis?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MarkLineDataKeySpecifier = (
	| 'type'
	| 'valueDim'
	| 'valueIndex'
	| 'xAxis'
	| 'yAxis'
	| MarkLineDataKeySpecifier
)[]
export type MarkLineDataFieldPolicy = {
	type?: FieldPolicy<any> | FieldReadFunction<any>
	valueDim?: FieldPolicy<any> | FieldReadFunction<any>
	valueIndex?: FieldPolicy<any> | FieldReadFunction<any>
	xAxis?: FieldPolicy<any> | FieldReadFunction<any>
	yAxis?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MarkerPositionKeySpecifier = (
	| 'angleAxis'
	| 'coord'
	| 'radiusAxis'
	| 'type'
	| 'value'
	| 'valueDim'
	| 'valueIndex'
	| 'x'
	| 'xAxis'
	| 'y'
	| 'yAxis'
	| MarkerPositionKeySpecifier
)[]
export type MarkerPositionFieldPolicy = {
	angleAxis?: FieldPolicy<any> | FieldReadFunction<any>
	coord?: FieldPolicy<any> | FieldReadFunction<any>
	radiusAxis?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	value?: FieldPolicy<any> | FieldReadFunction<any>
	valueDim?: FieldPolicy<any> | FieldReadFunction<any>
	valueIndex?: FieldPolicy<any> | FieldReadFunction<any>
	x?: FieldPolicy<any> | FieldReadFunction<any>
	xAxis?: FieldPolicy<any> | FieldReadFunction<any>
	y?: FieldPolicy<any> | FieldReadFunction<any>
	yAxis?: FieldPolicy<any> | FieldReadFunction<any>
}
export type MyReactionKeySpecifier = ('_id' | 'type' | MyReactionKeySpecifier)[]
export type MyReactionFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type NotificationSubscriptionResultKeySpecifier = (
	| '_id'
	| 'createdAt'
	| 'data'
	| 'deletedAt'
	| 'message'
	| 'parent'
	| 'parentId'
	| 'title'
	| 'type'
	| 'updatedAt'
	| 'userId'
	| 'viewed'
	| NotificationSubscriptionResultKeySpecifier
)[]
export type NotificationSubscriptionResultFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	data?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	message?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
	parentId?: FieldPolicy<any> | FieldReadFunction<any>
	title?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
	viewed?: FieldPolicy<any> | FieldReadFunction<any>
}
export type OrganizationSubscriptionResultKeySpecifier = (
	| '_id'
	| 'billing'
	| 'billingVerified'
	| 'contactEmail'
	| 'contactEmailVerified'
	| 'contactPhoneNumber'
	| 'contactPhoneNumberVerified'
	| 'createdAt'
	| 'customDomains'
	| 'deletedAt'
	| 'description'
	| 'emailTemplates'
	| 'logo'
	| 'name'
	| 'tag'
	| 'template'
	| 'updatedAt'
	| 'website'
	| OrganizationSubscriptionResultKeySpecifier
)[]
export type OrganizationSubscriptionResultFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	billing?: FieldPolicy<any> | FieldReadFunction<any>
	billingVerified?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmail?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	contactPhoneNumber?: FieldPolicy<any> | FieldReadFunction<any>
	contactPhoneNumberVerified?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	customDomains?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	description?: FieldPolicy<any> | FieldReadFunction<any>
	emailTemplates?: FieldPolicy<any> | FieldReadFunction<any>
	logo?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	tag?: FieldPolicy<any> | FieldReadFunction<any>
	template?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	website?: FieldPolicy<any> | FieldReadFunction<any>
}
export type OrganizationTemplateKeySpecifier = (
	| 'backgroundColor'
	| 'backgroundImageUrl'
	| 'footerMessage'
	| 'primaryColor'
	| 'primaryTextColor'
	| 'secondaryColor'
	| 'secondaryTextColor'
	| 'tertiaryColor'
	| 'tertiaryTextColor'
	| 'textColor'
	| 'zendeskKey'
	| OrganizationTemplateKeySpecifier
)[]
export type OrganizationTemplateFieldPolicy = {
	backgroundColor?: FieldPolicy<any> | FieldReadFunction<any>
	backgroundImageUrl?: FieldPolicy<any> | FieldReadFunction<any>
	footerMessage?: FieldPolicy<any> | FieldReadFunction<any>
	primaryColor?: FieldPolicy<any> | FieldReadFunction<any>
	primaryTextColor?: FieldPolicy<any> | FieldReadFunction<any>
	secondaryColor?: FieldPolicy<any> | FieldReadFunction<any>
	secondaryTextColor?: FieldPolicy<any> | FieldReadFunction<any>
	tertiaryColor?: FieldPolicy<any> | FieldReadFunction<any>
	tertiaryTextColor?: FieldPolicy<any> | FieldReadFunction<any>
	textColor?: FieldPolicy<any> | FieldReadFunction<any>
	zendeskKey?: FieldPolicy<any> | FieldReadFunction<any>
}
export type PercentilesKeySpecifier = ('p25' | 'p50' | 'p75' | PercentilesKeySpecifier)[]
export type PercentilesFieldPolicy = {
	p25?: FieldPolicy<any> | FieldReadFunction<any>
	p50?: FieldPolicy<any> | FieldReadFunction<any>
	p75?: FieldPolicy<any> | FieldReadFunction<any>
}
export type QueryKeySpecifier = ('health' | QueryKeySpecifier)[]
export type QueryFieldPolicy = {
	health?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ReactionCountKeySpecifier = ('count' | 'type' | ReactionCountKeySpecifier)[]
export type ReactionCountFieldPolicy = {
	count?: FieldPolicy<any> | FieldReadFunction<any>
	type?: FieldPolicy<any> | FieldReadFunction<any>
}
export type ReviewKeySpecifier = (
	| '_id'
	| 'body'
	| 'createdAt'
	| 'deletedAt'
	| 'parent'
	| 'parentId'
	| 'review'
	| 'updatedAt'
	| 'userId'
	| ReviewKeySpecifier
)[]
export type ReviewFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	body?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
	parentId?: FieldPolicy<any> | FieldReadFunction<any>
	review?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	userId?: FieldPolicy<any> | FieldReadFunction<any>
}
export type Stats2DKeySpecifier = ('x' | 'y' | Stats2DKeySpecifier)[]
export type Stats2DFieldPolicy = {
	x?: FieldPolicy<any> | FieldReadFunction<any>
	y?: FieldPolicy<any> | FieldReadFunction<any>
}
export type SubscriptionKeySpecifier = (
	| 'subscribeNotifications'
	| 'subscribeOrganizations'
	| 'subscribeUsers'
	| SubscriptionKeySpecifier
)[]
export type SubscriptionFieldPolicy = {
	subscribeNotifications?: FieldPolicy<any> | FieldReadFunction<any>
	subscribeOrganizations?: FieldPolicy<any> | FieldReadFunction<any>
	subscribeUsers?: FieldPolicy<any> | FieldReadFunction<any>
}
export type UserKeySpecifier = (
	| '_id'
	| 'additionalEmail'
	| 'additionalEmailVerified'
	| 'address'
	| 'birthdate'
	| 'contactEmail'
	| 'contactEmailVerified'
	| 'createdAt'
	| 'deletedAt'
	| 'email'
	| 'emailVerified'
	| 'familyName'
	| 'gender'
	| 'givenName'
	| 'locale'
	| 'middleName'
	| 'name'
	| 'nickname'
	| 'phoneNumber'
	| 'phoneNumberVerified'
	| 'picture'
	| 'recoveryEmail'
	| 'recoveryEmailVerified'
	| 'specialBadge'
	| 'updatedAt'
	| 'website'
	| 'zoneinfo'
	| UserKeySpecifier
)[]
export type UserFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	additionalEmail?: FieldPolicy<any> | FieldReadFunction<any>
	additionalEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	address?: FieldPolicy<any> | FieldReadFunction<any>
	birthdate?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmail?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	emailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	familyName?: FieldPolicy<any> | FieldReadFunction<any>
	gender?: FieldPolicy<any> | FieldReadFunction<any>
	givenName?: FieldPolicy<any> | FieldReadFunction<any>
	locale?: FieldPolicy<any> | FieldReadFunction<any>
	middleName?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	nickname?: FieldPolicy<any> | FieldReadFunction<any>
	phoneNumber?: FieldPolicy<any> | FieldReadFunction<any>
	phoneNumberVerified?: FieldPolicy<any> | FieldReadFunction<any>
	picture?: FieldPolicy<any> | FieldReadFunction<any>
	recoveryEmail?: FieldPolicy<any> | FieldReadFunction<any>
	recoveryEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	specialBadge?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	website?: FieldPolicy<any> | FieldReadFunction<any>
	zoneinfo?: FieldPolicy<any> | FieldReadFunction<any>
}
export type UserSubscriptionResultKeySpecifier = (
	| '_id'
	| 'additionalEmail'
	| 'additionalEmailVerified'
	| 'address'
	| 'birthdate'
	| 'contactEmail'
	| 'contactEmailVerified'
	| 'createdAt'
	| 'deletedAt'
	| 'email'
	| 'emailVerified'
	| 'familyName'
	| 'gender'
	| 'givenName'
	| 'locale'
	| 'middleName'
	| 'name'
	| 'nickname'
	| 'phoneNumber'
	| 'phoneNumberVerified'
	| 'picture'
	| 'recoveryEmail'
	| 'recoveryEmailVerified'
	| 'specialBadge'
	| 'updatedAt'
	| 'website'
	| 'zoneinfo'
	| UserSubscriptionResultKeySpecifier
)[]
export type UserSubscriptionResultFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	additionalEmail?: FieldPolicy<any> | FieldReadFunction<any>
	additionalEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	address?: FieldPolicy<any> | FieldReadFunction<any>
	birthdate?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmail?: FieldPolicy<any> | FieldReadFunction<any>
	contactEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	email?: FieldPolicy<any> | FieldReadFunction<any>
	emailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	familyName?: FieldPolicy<any> | FieldReadFunction<any>
	gender?: FieldPolicy<any> | FieldReadFunction<any>
	givenName?: FieldPolicy<any> | FieldReadFunction<any>
	locale?: FieldPolicy<any> | FieldReadFunction<any>
	middleName?: FieldPolicy<any> | FieldReadFunction<any>
	name?: FieldPolicy<any> | FieldReadFunction<any>
	nickname?: FieldPolicy<any> | FieldReadFunction<any>
	phoneNumber?: FieldPolicy<any> | FieldReadFunction<any>
	phoneNumberVerified?: FieldPolicy<any> | FieldReadFunction<any>
	picture?: FieldPolicy<any> | FieldReadFunction<any>
	recoveryEmail?: FieldPolicy<any> | FieldReadFunction<any>
	recoveryEmailVerified?: FieldPolicy<any> | FieldReadFunction<any>
	specialBadge?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	website?: FieldPolicy<any> | FieldReadFunction<any>
	zoneinfo?: FieldPolicy<any> | FieldReadFunction<any>
}
export type WebhookKeySpecifier = (
	| '_id'
	| 'active'
	| 'createdAt'
	| 'deletedAt'
	| 'parent'
	| 'parentId'
	| 'secret'
	| 'subscribeTo'
	| 'updatedAt'
	| 'url'
	| WebhookKeySpecifier
)[]
export type WebhookFieldPolicy = {
	_id?: FieldPolicy<any> | FieldReadFunction<any>
	active?: FieldPolicy<any> | FieldReadFunction<any>
	createdAt?: FieldPolicy<any> | FieldReadFunction<any>
	deletedAt?: FieldPolicy<any> | FieldReadFunction<any>
	parent?: FieldPolicy<any> | FieldReadFunction<any>
	parentId?: FieldPolicy<any> | FieldReadFunction<any>
	secret?: FieldPolicy<any> | FieldReadFunction<any>
	subscribeTo?: FieldPolicy<any> | FieldReadFunction<any>
	updatedAt?: FieldPolicy<any> | FieldReadFunction<any>
	url?: FieldPolicy<any> | FieldReadFunction<any>
}
export type StrictTypedTypePolicies = {
	Address?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | AddressKeySpecifier | (() => undefined | AddressKeySpecifier)
		fields?: AddressFieldPolicy
	}
	Comment?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | CommentKeySpecifier | (() => undefined | CommentKeySpecifier)
		fields?: CommentFieldPolicy
	}
	EmailTemplate?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EmailTemplateKeySpecifier | (() => undefined | EmailTemplateKeySpecifier)
		fields?: EmailTemplateFieldPolicy
	}
	EntityRelations?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | EntityRelationsKeySpecifier | (() => undefined | EntityRelationsKeySpecifier)
		fields?: EntityRelationsFieldPolicy
	}
	Fingerprint?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | FingerprintKeySpecifier | (() => undefined | FingerprintKeySpecifier)
		fields?: FingerprintFieldPolicy
	}
	Label?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | LabelKeySpecifier | (() => undefined | LabelKeySpecifier)
		fields?: LabelFieldPolicy
	}
	MarkAreaData?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MarkAreaDataKeySpecifier | (() => undefined | MarkAreaDataKeySpecifier)
		fields?: MarkAreaDataFieldPolicy
	}
	MarkLineData?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MarkLineDataKeySpecifier | (() => undefined | MarkLineDataKeySpecifier)
		fields?: MarkLineDataFieldPolicy
	}
	MarkerPosition?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MarkerPositionKeySpecifier | (() => undefined | MarkerPositionKeySpecifier)
		fields?: MarkerPositionFieldPolicy
	}
	MyReaction?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | MyReactionKeySpecifier | (() => undefined | MyReactionKeySpecifier)
		fields?: MyReactionFieldPolicy
	}
	NotificationSubscriptionResult?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| NotificationSubscriptionResultKeySpecifier
			| (() => undefined | NotificationSubscriptionResultKeySpecifier)
		fields?: NotificationSubscriptionResultFieldPolicy
	}
	OrganizationSubscriptionResult?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?:
			| false
			| OrganizationSubscriptionResultKeySpecifier
			| (() => undefined | OrganizationSubscriptionResultKeySpecifier)
		fields?: OrganizationSubscriptionResultFieldPolicy
	}
	OrganizationTemplate?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | OrganizationTemplateKeySpecifier | (() => undefined | OrganizationTemplateKeySpecifier)
		fields?: OrganizationTemplateFieldPolicy
	}
	Percentiles?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | PercentilesKeySpecifier | (() => undefined | PercentilesKeySpecifier)
		fields?: PercentilesFieldPolicy
	}
	Query?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | QueryKeySpecifier | (() => undefined | QueryKeySpecifier)
		fields?: QueryFieldPolicy
	}
	ReactionCount?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ReactionCountKeySpecifier | (() => undefined | ReactionCountKeySpecifier)
		fields?: ReactionCountFieldPolicy
	}
	Review?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | ReviewKeySpecifier | (() => undefined | ReviewKeySpecifier)
		fields?: ReviewFieldPolicy
	}
	Stats2D?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | Stats2DKeySpecifier | (() => undefined | Stats2DKeySpecifier)
		fields?: Stats2DFieldPolicy
	}
	Subscription?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | SubscriptionKeySpecifier | (() => undefined | SubscriptionKeySpecifier)
		fields?: SubscriptionFieldPolicy
	}
	User?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | UserKeySpecifier | (() => undefined | UserKeySpecifier)
		fields?: UserFieldPolicy
	}
	UserSubscriptionResult?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | UserSubscriptionResultKeySpecifier | (() => undefined | UserSubscriptionResultKeySpecifier)
		fields?: UserSubscriptionResultFieldPolicy
	}
	Webhook?: Omit<TypePolicy, 'fields' | 'keyFields'> & {
		keyFields?: false | WebhookKeySpecifier | (() => undefined | WebhookKeySpecifier)
		fields?: WebhookFieldPolicy
	}
}
export type TypedTypePolicies = StrictTypedTypePolicies & TypePolicies
