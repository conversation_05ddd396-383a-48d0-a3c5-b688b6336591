fragment activationFields on Activation {
	_id
	type
	name
	url
	runFrom
	runTo
	limit
	numberOfParticipants
	picture
	description
	date
	location
	organizationId
	createdAt
	deletedAt
	updatedAt
}

fragment fullActivationFields on Activation {
	...activationFields
	filter {
		labels
		productIds
		programIds
		registeredMinDate
		registeredMaxDate
	}
	participantIds
	# participantUsers
	filterProducts {
		_id
		name
		picture
	}
	filterPrograms {
		_id
		name
	}
}
