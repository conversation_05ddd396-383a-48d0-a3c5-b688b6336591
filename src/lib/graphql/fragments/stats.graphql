fragment stats2d on Stats2D {
	x
	y
}

fragment stats on Stats {
	total
	perDay {
		...stats2d
	}
	perWeek {
		...stats2d
	}
	perMonth {
		...stats2d
	}
	perYear {
		...stats2d
	}
	newPerDay {
		...stats2d
	}
	newPerWeek {
		...stats2d
	}
	newPerMonth {
		...stats2d
	}
	newPerYear {
		...stats2d
	}
	averageNewPerWeekDay {
		...stats2d
	}
	minNewPerWeekDay {
		...stats2d
	}
	maxNewPerWeekDay {
		...stats2d
	}
	stdDevNewPerWeekDay {
		...stats2d
	}
}

# fragment markPoint on MarkPoint {
# 	name
# 	symbol
# 	symbolSize
# 	symbolOffset
# 	data {
# 		value
# 		x
# 		y
# 		xAxis
# 		yAxis
# 		coord
# 		radiusAxis
# 		angleAxis
# 		type
# 		valueIndex
# 		valueDim
# 	}
# }

# fragment markLine on MarkLine {
# 	name
# 	symbol
# 	symbolSize
# 	symbolOffset
# 	data {
# 		xAxis
# 		yAxis
# 		type
# 		valueIndex
# 		valueDim
# 	}
# }

# fragment markArea on MarkArea {
# 	name
# 	symbol
# 	symbolSize
# 	symbolOffset
# 	data {
# 		xAxis
# 		yAxis
# 		type
# 		valueIndex
# 		valueDim
# 	}
# }
