fragment userFields on User {
	_id
	name
	familyName
	givenName
	middleName
	# nickname
	# birthdate
	# email
	# phoneNumber
	picture
	# address {
	# 	streetAddress
	# 	formatted
	# 	locality
	# 	postalCode
	# 	region
	# 	country
	# }
}

fragment fullUserFields on User {
	...userFields
	createdAt
	updatedAt
	nickname
	birthdate
	email
	emailVerified
	contactEmail
	contactEmailVerified
	recoveryEmail
	recoveryEmailVerified
	additionalEmail
	additionalEmailVerified
	phoneNumber
	phoneNumberVerified
	address {
		streetAddress
		formatted
		locality
		postalCode
		region
		country
	}
	gender
	website
	locale
	zoneinfo
	roles {
		_id
		createdAt
		updatedAt
		userId
		role
		organizationId
	}
	organizations {
		_id
		name
		description
		logo
		role
	}
}

fragment minimalUserFields on User {
	_id
	nickname
	picture
	# isBlocked
	# isFriend
	# isFollowing
	# isFollower
}

fragment roleFields on Role {
	_id
	userId
	organizationId
	role
}

fragment installationFields on Installation {
	_id
	subscription
	type
	userId
}

# fragment userRelationships on User {
# 	identities {
# 		externalId
# 		ensName
# 		provider
# 	}
# 	friendRequests {
# 		nodes {
# 			...minimalUserFields
# 		}
# 	}
# 	friends {
# 		nodes {
# 			...minimalUserFields
# 		}
# 	}
# }
