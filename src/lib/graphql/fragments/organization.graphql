fragment organizationFields on Organization {
	_id
	name
	description
	logo
}

fragment organizationWithRoleFields on OrganizationWithRole {
	_id
	name
	description
	logo
}

fragment oidcClientFields on OidcClient {
	_id
	client_id
	redirect_uris
	application_type
	client_name
	client_uri
	grant_types
	contacts
	logo_uri
	policy_uri
	tos_uri
	createdAt
	updatedAt
	organizationId
}

fragment fullOrganizationFields on Organization {
	...organizationFields
	createdAt
	updatedAt
	deletedAt
	tag
	billing
	billingVerified
	contactEmail
	contactEmailVerified
	contactPhoneNumber
	contactPhoneNumberVerified
	emailTemplates {
		type
		name
		provider
	}
	template {
		backgroundColor
		backgroundImageUrl
		textColor
		primaryColor
		primaryTextColor
		secondaryColor
		secondaryTextColor
		tertiaryColor
		tertiaryTextColor
		footerMessage
		zendeskKey
	}
	website
	customDomains
	customerId
}
