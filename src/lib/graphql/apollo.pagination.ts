import { mergeDeep } from '@apollo/client/utilities'
import { __rest } from 'tslib'

import type { Reference, FieldPolicy } from '@apollo/client'

type KeyArgs = FieldPolicy<unknown>['keyArgs']

interface DTO extends Reference {
	_id: string
}

type TPageInfo = {
	hasPreviousPage: boolean
	hasNextPage: boolean
	startCursor: string
	endCursor: string
}

type TExisting<TNode> = Readonly<{
	nodes: TNode[]
	pageInfo: TPageInfo
	totalCount?: number
}>

type TIncoming<TNode> = {
	nodes?: TNode[]
	pageInfo?: TPageInfo
	totalCount?: number
}

type PaginationFieldPolicy<TNode> = FieldPolicy<
	TExisting<TNode> | null,
	TIncoming<TNode> | null,
	TIncoming<TNode> | null
>

const getExtras = <TNode>(obj: Record<string, TNode>) => __rest(obj, notExtras)
const notExtras = ['nodes', 'pageInfo', 'pageInfo']

function makeEmptyData<TNode>(): TExisting<TNode> {
	return {
		nodes: [],
		pageInfo: {
			hasPreviousPage: false,
			hasNextPage: true,
			startCursor: '',
			endCursor: '',
		},
	}
}

export function pagination<TNode extends DTO>(
	keyArgs: KeyArgs = false,
	pageArg = 'filter',
): PaginationFieldPolicy<TNode> {
	return {
		keyArgs,

		read(existing) {
			if (!existing) return existing

			const nodes: TNode[] = []
			let firstEdgeCursor = ''
			let lastEdgeCursor = ''
			existing.nodes.forEach((node) => {
				// Edges themselves could be Reference objects, so it's important
				// to use readField to access the edge.edge.node property.
				nodes.push(node)
				if (node._id) {
					firstEdgeCursor = firstEdgeCursor || node._id || ''
					lastEdgeCursor = node._id || lastEdgeCursor
				}
			})

			const { startCursor, endCursor } = existing.pageInfo || {}

			return {
				// Some implementations return additional Connection fields, such
				// as existing.totalCount. These fields are saved by the merge
				// function, so the read function should also preserve them.
				...getExtras(existing),
				nodes,
				pageInfo: {
					...existing.pageInfo,
					// If existing.pageInfo.{start,end}Cursor are undefined or "", default
					// to firstEdgeCursor and/or lastEdgeCursor.
					startCursor: startCursor || firstEdgeCursor,
					endCursor: endCursor || lastEdgeCursor,
				},
				totalCount: existing.totalCount,
			}
		},

		merge(existing, incoming, { args }) {
			if (!existing) {
				existing = makeEmptyData()
			}

			if (!incoming) {
				return existing
			}

			const incomingNodes = incoming.nodes || []

			if (incoming.pageInfo) {
				const { pageInfo } = incoming
				const { startCursor, endCursor } = pageInfo
				const firstNode = incomingNodes[0]
				const lastNode = incomingNodes[incomingNodes.length - 1]

				// Cursors can also come from nodes, so we default
				// pageInfo.{start,end}Cursor to {first,last}Edge.cursor.
				const firstCursor = firstNode && firstNode._id
				if (firstCursor && !startCursor) {
					incoming = mergeDeep(incoming, {
						pageInfo: {
							startCursor: firstCursor,
						},
					})
				}
				const lastCursor = lastNode && lastNode._id
				if (lastCursor && !endCursor) {
					incoming = mergeDeep(incoming, {
						pageInfo: {
							endCursor: lastCursor,
						},
					})
				}
			}

			let prefix = existing.nodes
			let suffix: typeof prefix = []

			if (args && args[pageArg]?.after) {
				// This comparison does not need to use readField("cursor", edge),
				// because we stored the cursor field of any Reference nodes as an
				// extra property of the Reference object.
				const index = prefix.findIndex((node) => node._id === args[pageArg].after)
				if (index >= 0) {
					prefix = prefix.slice(0, index + 1)
					// suffix = []; // already true
				}
			} else if (args && args[pageArg] && args[pageArg].before) {
				const index = prefix.findIndex((node) => node._id === args[pageArg].before)
				suffix = index < 0 ? prefix : prefix.slice(index)
				prefix = []
			} else if (args && args[pageArg] && args[pageArg].offset) {
				prefix = prefix.slice(0, args[pageArg].offset)
			} else if (incoming.nodes) {
				// If we have neither args.after nor args.before, the incoming
				// nodes cannot be spliced into the existing nodes, so they must
				// replace the existing nodes. See #6592 for a motivating example.
				prefix = []
			}

			const nodes = [...prefix, ...incomingNodes, ...suffix]

			const pageInfo: TPageInfo = {
				// The ordering of these two ...spreads may be surprising, but it
				// makes sense because we want to combine PageInfo properties with a
				// preference for existing values, *unless* the existing values are
				// overridden by the logic below, which is permitted only when the
				// incoming page falls at the beginning or end of the data.
				...incoming.pageInfo,
				...existing.pageInfo,
			}

			if (incoming.pageInfo) {
				const { hasPreviousPage, hasNextPage, startCursor, endCursor, ...extras } = incoming.pageInfo

				// If incoming.pageInfo had any extra non-standard properties,
				// assume they should take precedence over any existing properties
				// of the same name, regardless of where this page falls with
				// respect to the existing data.
				Object.assign(pageInfo, extras)

				// Keep existing.pageInfo.has{Previous,Next}Page unless the
				// placement of the incoming nodes means incoming.hasPreviousPage
				// or incoming.hasNextPage should become the new values for those
				// properties in existing.pageInfo. Note that these updates are
				// only permitted when the beginning or end of the incoming page
				// coincides with the beginning or end of the existing data, as
				// determined using prefix.length and suffix.length.
				if (!prefix.length) {
					if (void 0 !== hasPreviousPage) pageInfo.hasPreviousPage = hasPreviousPage
					if (void 0 !== startCursor) pageInfo.startCursor = startCursor
				}
				if (!suffix.length) {
					if (void 0 !== hasNextPage) pageInfo.hasNextPage = hasNextPage
					if (void 0 !== endCursor) pageInfo.endCursor = endCursor
				}
			}

			return {
				...getExtras(existing),
				...getExtras(incoming),
				nodes,
				pageInfo,
				totalCount: incoming.totalCount || existing.totalCount,
			}
		},
	}
}
