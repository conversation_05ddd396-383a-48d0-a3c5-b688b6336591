import { ApolloClient, ApolloLink, HttpLink, InMemoryCache, split } from '@apollo/client/core/index.js'
import { GraphQLWsLink } from '@apollo/client/link/subscriptions'
import { getMainDefinition } from '@apollo/client/utilities'
import { Kind, OperationTypeNode } from 'graphql'
import { createClient } from 'graphql-ws'

import { browser } from '$app/environment'

import { getAuth } from '$lib/services/auth.svelte'

const httpLink = new HttpLink({
	uri: import.meta.env.VITE_SUBSCRIPTIONS_URL,
})

const links: ApolloLink[] = []

const auth = getAuth()
// This check is needed for SSR.
if (!browser && auth) {
	const wsLink = new GraphQLWsLink(
		createClient({
			url: import.meta.env.VITE_SUBSCRIPTIONS_WS_URL,
			connectionParams: async () => {
				const token = await auth.token

				return token
					? {
							authorization: `Bearer ${token}`,
						}
					: {}
			},
		}),
	)

	links.push(wsLink)
}
links.push(httpLink)

const splitLink = split(
	({ query }) => {
		const definition = getMainDefinition(query)
		return definition.kind === Kind.OPERATION_DEFINITION && definition.operation === OperationTypeNode.SUBSCRIPTION
	},
	links[0],
	links[1],
)

export default new ApolloClient({
	name: 'subscriptions',
	version: import.meta.env.CF_PAGES_COMMIT_SHA || undefined,
	cache: new InMemoryCache({}),
	defaultOptions: { watchQuery: { fetchPolicy: 'cache-and-network' } },
	link: splitLink,
	ssrMode: true,
	connectToDevTools: true,
})
