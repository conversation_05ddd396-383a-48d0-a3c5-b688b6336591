query Webhooks($filter: FilterArgs) {
	webhooks(filter: $filter) {
		nodes {
			_id
			active
			createdAt
			deletedAt
			parent
			parentId
			secret
			subscribeTo
			updatedAt
			url
		}
		totalCount
	}
}

mutation CreateWebhook($partialEntity: CreateWebhookInput!) {
	createWebhook(partialEntity: $partialEntity) {
		_id
		active
		createdAt
		deletedAt
		parent
		parentId
		secret
		subscribeTo
		updatedAt
		url
	}
}

mutation UpdateWebhook($partialEntity: UpdateWebhookInput!) {
	updateWebhook(partialEntity: $partialEntity) {
		_id
		active
		createdAt
		deletedAt
		parent
		parentId
		secret
		subscribeTo
		updatedAt
		url
	}
}

mutation DeleteWebhook($filter: FilterOneArgs!) {
	deleteWebhook(filter: $filter) {
		_id
	}
}
