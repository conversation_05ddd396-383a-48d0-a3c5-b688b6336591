query Invitations($filter: FilterArgs) {
	invitations(filter: $filter) {
		totalCount
		nodes {
			_id
			code
			createdAt
			organizationId
			organization {
				_id
				name
				logo
			}
			createdBy {
				_id
				name
				picture
			}
			createdById
			# deletedAt
			expiresAt
			limit
			roles
			type
			updatedAt
			used
			usedCount
			emails
			phones
			userIds
			users {
				_id
				name
				picture
			}
		}
	}
}

query InviteUsersSearch($filter: FilterArgs, $rolesFilter: FilterArgs) {
	users(filter: $filter) {
		totalCount
		nodes {
			_id
			name
			picture
			email
			roles(filter: $rolesFilter) {
				role
				organizationId
				userId
			}
		}
	}
}

mutation CreateInvitation($partialEntity: CreateInvitationInput!) {
	createInvitation(partialEntity: $partialEntity) {
		_id
		code
		type
		used
		limit
		usedCount
		createdAt
		updatedAt
		organizationId
		roles
		emails
		phones
		consumerIds
		consumers {
			_id
			name
			picture
		}
		organization {
			_id
			name
			logo
		}
		users {
			_id
			name
			picture
		}
		createdById
		createdBy {
			_id
			name
			picture
		}
	}
}

mutation UpdateInvitation($partialEntity: UpdateInvitationInput!, $upsert: Boolean!) {
	updateInvitation(partialEntity: $partialEntity, upsert: $upsert) {
		_id
		code
		type
		used
		limit
		usedCount
		createdAt
		updatedAt
		organizationId
		roles
		emails
		phones
		consumerIds
		consumers {
			_id
			name
			picture
		}
		organization {
			_id
			name
			logo
		}
		users {
			_id
			name
			picture
		}
		createdById
		createdBy {
			_id
			name
			picture
		}
	}
}

mutation AnswerInvitation($filter: FilterOneArgs!, $answer: Boolean!) {
	answerInvitation(filter: $filter, answer: $answer) {
		_id
		code
		type
		used
		limit
		usedCount
		createdAt
		updatedAt
		organizationId
		roles
		emails
		phones
		consumerIds
		consumers {
			_id
			name
			picture
		}
		organization {
			_id
			name
			logo
		}
		users {
			_id
			name
			picture
		}
		createdById
		createdBy {
			_id
			name
			picture
		}
	}
}

mutation DeleteInvitation($filter: FilterOneArgs!) {
	deleteInvitation(filter: $filter) {
		_id
	}
}
