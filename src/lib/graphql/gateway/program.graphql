query Programs($filter: FilterArgs) {
	programs(filter: $filter) {
		totalCount
		nodes {
			...programFields
			organization {
				...organizationFields
			}
		}
	}
}

query Program($filter: FilterOneArgs!) {
	program(filter: $filter) {
		...programFields
		organization {
			...organizationFields
		}
	}
}

query ProgramExists($filter: FilterOneArgs!) {
	program(filter: $filter) {
		_id
	}
}

mutation CreateProgram($partialEntity: CreateProgramInput!) {
	createProgram(partialEntity: $partialEntity) {
		_id
		name
		createdAt
		deletedAt
		updatedAt
		organizationId
	}
}

mutation UpdateProgram($partialEntity: UpdateProgramInput!) {
	updateProgram(partialEntity: $partialEntity) {
		...programFields
		organization {
			...organizationFields
		}
	}
}

mutation DeleteProgram($filter: FilterOneArgs!) {
	deleteProgram(filter: $filter) {
		_id
	}
}

query ProgramStatsTotal($filter: FilterArgs) {
	programStats(filter: $filter) {
		total
	}
}

query ProgramStatsPerDay($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		perDay {
			...stats2d
		}
	}
}

query ProgramStatsPerWeek($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		perWeek {
			...stats2d
		}
	}
}

query ProgramStatsPerMonth($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		perMonth {
			...stats2d
		}
	}
}

query ProgramStatsPerYear($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		perYear {
			...stats2d
		}
	}
}

query ProgramStatsNewPerDay($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		newPerDay {
			...stats2d
		}
	}
}

query ProgramStatsNewPerWeek($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		newPerWeek {
			...stats2d
		}
	}
}

query ProgramStatsNewPerMonth($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		newPerMonth {
			...stats2d
		}
	}
}

query ProgramStatsNewPerYear($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		newPerYear {
			...stats2d
		}
	}
}

query ProgramStatsAverageNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		averageNewPerWeekDay {
			...stats2d
		}
	}
}

query ProgramStatsMinNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		minNewPerWeekDay {
			...stats2d
		}
	}
}

query ProgramStatsMaxNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		maxNewPerWeekDay {
			...stats2d
		}
	}
}

query ProgramStatsStdDevNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	programStats(filter: $filter, scope: $scope) {
		stdDevNewPerWeekDay {
			...stats2d
		}
	}
}
