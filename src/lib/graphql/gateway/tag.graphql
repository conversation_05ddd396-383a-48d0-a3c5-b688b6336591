query Tags($filter: FilterArgs) {
	tags(filter: $filter) {
		totalCount
		nodes {
			...tagFields
			organization {
				...organizationFields
			}
		}
	}
}

query Tag($filter: FilterOneArgs!) {
	tag(filter: $filter) {
		...tagFields
		organization {
			...organizationFields
			template {
				backgroundColor
				backgroundImageUrl
				textColor
				primaryColor
				primaryTextColor
				secondaryColor
				secondaryTextColor
				tertiaryColor
				tertiaryTextColor
				footerMessage
				zendeskKey
			}
		}
	}
}

query Tap($tapId: String!, $args: ChipValidationArguments!) {
	tap(id: $tapId, args: $args) {
		tag {
			_id
			organizationId
			programId
			productId
			program {
				customRedirect
			}
			organization {
				customDomains
			}
			disabled
			disabledReason
			# epc {
			# 	... on SGTIN96Components {
			# 		scheme
			# 		filter
			# 		companyPrefix
			# 		itemReference
			# 		serialNumber
			# 	}
			# 	... on SSCC96Components {
			# 		scheme
			# 		filter
			# 		extensionDigit
			# 		companyPrefix
			# 		serialReference
			# 	}
			# 	... on GID96Components {
			# 		scheme
			# 		generalManagerNumber
			# 		objectClass
			# 		serialNumber
			# 	}
			# 	... on SGLN96Components {
			# 		scheme
			# 		filter
			# 		companyPrefix
			# 		locationReference
			# 		extension
			# 	}
			# 	... on GRAI96Components {
			# 		scheme
			# 		filter
			# 		companyPrefix
			# 		assetType
			# 		serialNumber
			# 	}
			# 	... on GIAI96Components {
			# 		scheme
			# 		filter
			# 		companyPrefix
			# 		individualAssetReference
			# 	}
			# 	... on ISO15963Components {
			# 		scheme
			# 		manufacturerCode
			# 		serialNumber
			# 	}
			# }
		}
		# sessionPassword
	}
}

query TagExists($filter: FilterOneArgs!) {
	tag(filter: $filter) {
		_id
	}
}

mutation CreateTag($data: CreateTag!) {
	createTag(data: $data) {
		...tagFields
		organization {
			...organizationFields
		}
	}
}

mutation CreateTags($data: CreateTags!) {
	createTags(data: $data)
}

mutation UpdateTag($partialEntity: UpdateTagInput!) {
	updateTag(partialEntity: $partialEntity) {
		...tagFields
		organization {
			...organizationFields
		}
	}
}

mutation DeleteTag($filter: FilterOneArgs!) {
	deleteTag(filter: $filter) {
		_id
	}
}

mutation ClaimTag($id: String!) {
	claimTag(id: $id) {
		...tagFields
	}
}

mutation ReleaseTag($id: String!) {
	releaseTag(id: $id) {
		...tagFields
	}
}

mutation TransferTag($id: String!, $userId: String!) {
	transferTag(id: $id, userId: $userId) {
		...tagFields
	}
}

mutation AttachProductToTags($filter: FilterArgs!, $productId: String!, $productDetails: ProductDetailsInput!) {
	attachProductToTags(filter: $filter, productId: $productId, productDetails: $productDetails) {
		...tagFields
	}
}

mutation AttachProgramToTags($filter: FilterArgs!, $programId: String!) {
	attachProgramToTags(filter: $filter, programId: $programId) {
		...tagFields
	}
}

mutation DetachProductFromTags($filter: FilterArgs!) {
	detachProductFromTags(filter: $filter) {
		...tagFields
	}
}

mutation DetachProgramFromTags($filter: FilterArgs!) {
	detachProgramFromTags(filter: $filter) {
		...tagFields
	}
}

query TagStatsTotal($filter: FilterArgs) {
	tagStats(filter: $filter) {
		total
	}
}

query TagStatsPerDay($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		perDay {
			...stats2d
		}
	}
}

query TagStatsPerWeek($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		perWeek {
			...stats2d
		}
	}
}

query TagStatsPerMonth($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		perMonth {
			...stats2d
		}
	}
}

query TagStatsPerYear($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		perYear {
			...stats2d
		}
	}
}

query TagStatsNewPerDay($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		newPerDay {
			...stats2d
		}
	}
}

query TagStatsNewPerWeek($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		newPerWeek {
			...stats2d
		}
	}
}

query TagStatsNewPerMonth($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		newPerMonth {
			...stats2d
		}
	}
}

query TagStatsNewPerYear($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		newPerYear {
			...stats2d
		}
	}
}

query TagStatsAverageNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		averageNewPerWeekDay {
			...stats2d
		}
	}
}

query TagStatsMinNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		minNewPerWeekDay {
			...stats2d
		}
	}
}

query TagStatsMaxNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		maxNewPerWeekDay {
			...stats2d
		}
	}
}

query TagStatsStdDevNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	tagStats(filter: $filter, scope: $scope) {
		stdDevNewPerWeekDay {
			...stats2d
		}
	}
}
