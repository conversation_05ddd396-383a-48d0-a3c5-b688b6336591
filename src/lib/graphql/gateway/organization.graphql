query OrganizationExists($filter: FilterOneArgs!) {
	organization(filter: $filter) {
		_id
	}
}

query Organization($filter: FilterOneArgs!) {
	organization(filter: $filter) {
		...organizationFields
	}
}

query FullOrganization($filter: FilterOneArgs!) {
	organization(filter: $filter) {
		...fullOrganizationFields
	}
}

query Organizations($filter: FilterArgs) {
	organizations(filter: $filter) {
		nodes {
			...organizationFields
		}
		totalCount
	}
}

query OrganizationsTable($filter: FilterArgs) {
	organizations(filter: $filter) {
		nodes {
			...organizationFields
			billing
			billingVerified
			contactEmail
			contactEmailVerified
			contactPhoneNumber
			contactPhoneNumberVerified
			createdAt
			updatedAt
		}
		totalCount
	}
}

query OrganizationFromCustomDomain($domain: String!) {
	oidcClient(filter: { where: { customDomains: $domain } }) {
		_id
		organizationId
		organization {
			...organizationFields
			template {
				backgroundColor
				backgroundImageUrl
				textColor
				primaryColor
				primaryTextColor
				secondaryColor
				secondaryTextColor
				tertiaryColor
				tertiaryTextColor
				footerMessage
				zendeskKey
			}
		}
	}
}

query OrganizationRoles($filter: FilterArgs) {
	roles(filter: $filter) {
		totalCount
		nodes {
			_id
			createdAt
			updatedAt
			userId
			role
			user {
				_id
				name
				email
				picture
			}
		}
	}
}

query OrganizationOidcClients($filter: FilterArgs) {
	oidcClients(filter: $filter) {
		totalCount
		nodes {
			...oidcClientFields
		}
	}
}

mutation CreateOrganization($partialEntity: CreateOrganizationInput!) {
	createOrganization(partialEntity: $partialEntity) {
		...fullOrganizationFields
	}
}

mutation UpdateOrganization($partialEntity: UpdateOrganizationInput!) {
	updateOrganization(partialEntity: $partialEntity) {
		...fullOrganizationFields
	}
}

mutation DeleteOrganization($filter: FilterOneArgs!) {
	deleteOrganization(filter: $filter) {
		_id
	}
}

mutation OrganizationChangeRole($partialEntity: UpdateRoleInput!) {
	updateRole(partialEntity: $partialEntity) {
		_id
		role
	}
}

mutation OrganizationCreateOidcClient($partialEntity: OidcClientCreateInput!) {
	createOidcClient(partialEntity: $partialEntity) {
		...oidcClientFields
	}
}

mutation OrganizationUpdateOidcClient($partialEntity: UpdateOidcClientInput!) {
	updateOidcClient(partialEntity: $partialEntity) {
		...oidcClientFields
	}
}

mutation OrganizationDeleteOidcClient($filter: FilterOneArgs!) {
	deleteOidcClient(filter: $filter) {
		_id
	}
}

query OrganizationStatsTotal($filter: FilterArgs) {
	organizationStats(filter: $filter) {
		total
	}
}

query OrganizationStatsPerDay($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		perDay {
			...stats2d
		}
	}
}

query OrganizationStatsPerWeek($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		perWeek {
			...stats2d
		}
	}
}

query OrganizationStatsPerMonth($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		perMonth {
			...stats2d
		}
	}
}

query OrganizationStatsPerYear($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		perYear {
			...stats2d
		}
	}
}

query OrganizationStatsNewPerDay($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		newPerDay {
			...stats2d
		}
	}
}

query OrganizationStatsNewPerWeek($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		newPerWeek {
			...stats2d
		}
	}
}

query OrganizationStatsNewPerMonth($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		newPerMonth {
			...stats2d
		}
	}
}

query OrganizationStatsNewPerYear($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		newPerYear {
			...stats2d
		}
	}
}

query OrganizationStatsAverageNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		averageNewPerWeekDay {
			...stats2d
		}
	}
}

query OrganizationStatsMinNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		minNewPerWeekDay {
			...stats2d
		}
	}
}

query OrganizationStatsMaxNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		maxNewPerWeekDay {
			...stats2d
		}
	}
}

query OrganizationStatsStdDevNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	organizationStats(filter: $filter, scope: $scope) {
		stdDevNewPerWeekDay {
			...stats2d
		}
	}
}
