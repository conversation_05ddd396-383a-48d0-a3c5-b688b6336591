query Activations($search: ActivationSearch!, $options: PaginationArgs) {
	activations(search: $search, options: $options) {
		totalCount
		nodes {
			...fullActivationFields
			organization {
				...organizationFields
			}
		}
	}
}

query Activation($id: String, $search: ActivationSearch) {
	activation(id: $id, search: $search) {
		...fullActivationFields
		organization {
			...organizationFields
		}
	}
}

query ActivationExists($id: String, $search: ActivationSearch) {
	activation(id: $id, search: $search) {
		_id
	}
}

mutation CreateActivation($data: CreateActivation!) {
	createActivation(data: $data) {
		...fullActivationFields
		organization {
			...organizationFields
		}
	}
}

mutation UpdateActivation($partialEntity: UpdateActivationInput!) {
	updateActivation(partialEntity: $partialEntity) {
		...fullActivationFields
		organization {
			...organizationFields
		}
	}
}

mutation DeleteActivation($filter: FilterOneArgs!) {
	deleteActivation(filter: $filter) {
		_id
	}
}

query ActivationStatsTotal($filter: FilterArgs) {
	activationStats(filter: $filter) {
		total
	}
}

query ActivationStatsPerDay($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		perDay {
			...stats2d
		}
	}
}

query ActivationStatsPerWeek($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		perWeek {
			...stats2d
		}
	}
}

query ActivationStatsPerMonth($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		perMonth {
			...stats2d
		}
	}
}

query ActivationStatsPerYear($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		perYear {
			...stats2d
		}
	}
}

query ActivationStatsNewPerDay($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		newPerDay {
			...stats2d
		}
	}
}

query ActivationStatsNewPerWeek($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		newPerWeek {
			...stats2d
		}
	}
}

query ActivationStatsNewPerMonth($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		newPerMonth {
			...stats2d
		}
	}
}

query ActivationStatsNewPerYear($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		newPerYear {
			...stats2d
		}
	}
}

query ActivationStatsAverageNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		averageNewPerWeekDay {
			...stats2d
		}
	}
}

query ActivationStatsMinNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		minNewPerWeekDay {
			...stats2d
		}
	}
}

query ActivationStatsMaxNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		maxNewPerWeekDay {
			...stats2d
		}
	}
}

query ActivationStatsStdDevNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	activationStats(filter: $filter, scope: $scope) {
		stdDevNewPerWeekDay {
			...stats2d
		}
	}
}
