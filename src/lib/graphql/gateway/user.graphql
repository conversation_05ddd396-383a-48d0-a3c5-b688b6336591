query Me {
	me {
		...userFields
		# ...userRelationships
		organizations {
			...organizationWithRoleFields
		}
		roles {
			...roleFields
		}
		installations {
			...installationFields
		}
	}
}

query FullUserProfile($filter: FilterOneArgs!) {
	user(filter: $filter) {
		...fullUserFields
	}
}

query Users($filter: FilterArgs) {
	users(filter: $filter) {
		nodes {
			...minimalUserFields
		}
		totalCount
	}
}

query UsersTable($filter: FilterArgs) {
	users(filter: $filter) {
		totalCount
		nodes {
			...userFields
			emailVerified
			contactEmailVerified
			recoveryEmailVerified
			phoneNumberVerified
			locale
			zoneinfo
			createdAt
			updatedAt
		}
	}
}

mutation UpdateUser($partialEntity: UpdateUserInput!) {
	updateUser(partialEntity: $partialEntity) {
		...userFields
		emailVerified
		contactEmailVerified
		recoveryEmailVerified
		phoneNumberVerified
		locale
		zoneinfo
		createdAt
		updatedAt
	}
}

mutation UpdateFullUser($partialEntity: UpdateUserInput!) {
	updateUser(partialEntity: $partialEntity) {
		...fullUserFields
	}
}

mutation DeleteUser($filter: FilterOneArgs!) {
	deleteUser(filter: $filter) {
		_id
	}
}

query Admins($roles: [AdminRoles!], $filter: FilterArgs) {
	admins(roles: $roles, filter: $filter) {
		totalCount
		nodes {
			_id
			roles
			picture
			nickname
			name
			middleName
			givenName
			familyName
		}
	}
}

mutation CreateRoles($partialEntities: [CreateRoleInput!]!) {
	createRoles(partialEntities: $partialEntities) {
		_id
		createdAt
		updatedAt
		userId
		role
		organizationId
		user {
			_id
			name
			email
			picture
		}
	}
}

mutation DeleteRole($filter: FilterOneArgs!) {
	deleteRole(filter: $filter) {
		_id
	}
}

mutation MeChangePassword($currentPassword: String!, $newPassword: String!) {
	meChangePassword(currentPassword: $currentPassword, newPassword: $newPassword) {
		_id
	}
}

mutation MeSendVerification($type: String!) {
	meSendVerification(type: $type) {
		_id
	}
}

query UserStatsTotal($filter: FilterArgs) {
	userStats(filter: $filter) {
		total
	}
}

query UserStatsPerDay($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		perDay {
			...stats2d
		}
	}
}

query UserStatsPerWeek($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		perWeek {
			...stats2d
		}
	}
}

query UserStatsPerMonth($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		perMonth {
			...stats2d
		}
	}
}

query UserStatsPerYear($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		perYear {
			...stats2d
		}
	}
}

query UserStatsNewPerDay($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		newPerDay {
			...stats2d
		}
	}
}

query UserStatsNewPerWeek($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		newPerWeek {
			...stats2d
		}
	}
}

query UserStatsNewPerMonth($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		newPerMonth {
			...stats2d
		}
	}
}

query UserStatsNewPerYear($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		newPerYear {
			...stats2d
		}
	}
}

query UserStatsAverageNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		averageNewPerWeekDay {
			...stats2d
		}
	}
}

query UserStatsMinNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		minNewPerWeekDay {
			...stats2d
		}
	}
}

query UserStatsMaxNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		maxNewPerWeekDay {
			...stats2d
		}
	}
}

query UserStatsStdDevNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	userStats(filter: $filter, scope: $scope) {
		stdDevNewPerWeekDay {
			...stats2d
		}
	}
}
