query PeopleStatsTotal($filter: FilterArgs) {
	peopleStats(filter: $filter) {
		total
	}
}

query PeopleStatsPerDay($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		perDay {
			x
			y
		}
	}
}

query PeopleStatsPerWeek($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		perWeek {
			x
			y
		}
	}
}

query PeopleStatsPerMonth($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		perMonth {
			x
			y
		}
	}
}

query PeopleStatsPerYear($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		perYear {
			x
			y
		}
	}
}

query PeopleStatsNewPerDay($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		newPerDay {
			x
			y
		}
	}
}

query PeopleStatsNewPerWeek($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		newPerWeek {
			x
			y
		}
	}
}

query PeopleStatsNewPerMonth($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		newPerMonth {
			x
			y
		}
	}
}

query PeopleStatsNewPerYear($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		newPerYear {
			x
			y
		}
	}
}

query PeopleStatsAverageNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		averageNewPerWeekDay {
			x
			y
		}
	}
}

query PeopleStatsMinNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		minNewPerWeekDay {
			x
			y
		}
	}
}

query PeopleStatsMaxNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		maxNewPerWeekDay {
			x
			y
		}
	}
}

query PeopleStatsStdDevNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	peopleStats(filter: $filter, scope: $scope) {
		stdDevNewPerWeekDay {
			x
			y
		}
	}
}
