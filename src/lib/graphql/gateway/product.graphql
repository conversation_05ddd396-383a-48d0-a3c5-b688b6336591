query Products($filter: FilterArgs) {
	products(filter: $filter) {
		totalCount
		nodes {
			...productFields
			organization {
				...organizationFields
			}
		}
	}
}

query Product($filter: FilterOneArgs!) {
	product(filter: $filter) {
		...productFields
		organization {
			...organizationFields
		}
	}
}

query ProductExists($filter: FilterOneArgs!) {
	product(filter: $filter) {
		_id
	}
}

mutation CreateProduct($partialEntity: CreateProductInput!) {
	createProduct(partialEntity: $partialEntity) {
		...productFields
		organization {
			...organizationFields
		}
	}
}

mutation UpdateProduct($partialEntity: UpdateProductInput!) {
	updateProduct(partialEntity: $partialEntity) {
		...productFields
		organization {
			...organizationFields
		}
	}
}

mutation DeleteProduct($filter: FilterOneArgs!) {
	deleteProduct(filter: $filter) {
		_id
	}
}

query ProductStatsTotal($filter: FilterArgs) {
	productStats(filter: $filter) {
		total
	}
}

query ProductStatsPerDay($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		perDay {
			...stats2d
		}
	}
}

query ProductStatsPerWeek($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		perWeek {
			...stats2d
		}
	}
}

query ProductStatsPerMonth($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		perMonth {
			...stats2d
		}
	}
}

query ProductStatsPerYear($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		perYear {
			...stats2d
		}
	}
}

query ProductStatsNewPerDay($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		newPerDay {
			...stats2d
		}
	}
}

query ProductStatsNewPerWeek($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		newPerWeek {
			...stats2d
		}
	}
}

query ProductStatsNewPerMonth($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		newPerMonth {
			...stats2d
		}
	}
}

query ProductStatsNewPerYear($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		newPerYear {
			...stats2d
		}
	}
}

query ProductStatsAverageNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		averageNewPerWeekDay {
			...stats2d
		}
	}
}

query ProductStatsMinNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		minNewPerWeekDay {
			...stats2d
		}
	}
}

query ProductStatsMaxNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		maxNewPerWeekDay {
			...stats2d
		}
	}
}

query ProductStatsStdDevNewPerWeekDay($filter: FilterArgs, $scope: StatsInput) {
	productStats(filter: $filter, scope: $scope) {
		stdDevNewPerWeekDay {
			...stats2d
		}
	}
}
