query UnreadNotificationsCount($filter: FilterArgs) {
	notifications(filter: $filter) {
		totalCount
	}
}

query Notifications($filter: FilterArgs) {
	notifications(filter: $filter) {
		nodes {
			_id
			type
			viewed
			title
			message
			parent
			parentId
			createdAt
			updatedAt
		}
		totalCount
	}
}

mutation UpdateNotification($partialEntity: UpdateNotificationInput!) {
	updateNotification(partialEntity: $partialEntity) {
		_id
		type
		viewed
		title
		message
		parent
		parentId
		createdAt
		updatedAt
	}
}

mutation MarkAllReadNotifications {
	markAllReadNotifications {
		_id
	}
}
