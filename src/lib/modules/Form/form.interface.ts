import * as fileUpload from '@zag-js/file-upload'
import * as tagsInput from '@zag-js/tags-input'
import clone from 'just-clone'

import type { DocumentNode, ExtractValues, NestedFields, ResolvedPropertyType } from '$lib/utils'
import type { SearchAutocompleteItem, SearchAutocompleteProps } from './components/SearchAutocomplete.svelte'
import type { DatePicker } from '@svelte-plugins/datepicker'
import type { Component, ComponentProps, Snippet } from 'svelte'

export enum FieldType {
	AUTOCOMPLETE = 'autocomplete',
	SELECT = 'select',
	INPUT = 'input',
	RADIO = 'radio',
	TEXTAREA = 'textarea',

	DATEPICKER = 'date-picker',

	LIST = 'list',
	INPUT_TAGS = 'input-tags',

	FILE_UPLOAD = 'file',

	BREAK = 'break',
	CUSTOM = 'custom',

	HIDDEN = 'hidden',
}

export interface ImageAddOn {
	img: string | 'property'
	href?: string | 'property'
	target?: HTMLAnchorElement['target']
	onclick?: (e: MouseEvent) => void
	shim?: boolean
	rounded?: boolean | string
}
export interface IconAddOn {
	icon?: string | 'property'
	iconSet?: string
	target?: HTMLAnchorElement['target']
	onclick?: (e: MouseEvent) => void
	shim?: boolean
}
export interface TextAddOn {
	text?: string | 'property'
	target?: HTMLAnchorElement['target']
	onclick?: (e: MouseEvent) => void
	shim?: boolean
}

export type AddOn<DTO> =
	| ImageAddOn
	| IconAddOn
	| TextAddOn
	| ((data: DTO) => ImageAddOn | IconAddOn | TextAddOn | undefined)

export interface FieldAddOns<DTO> {
	addonRight?: AddOn<DTO>
	addonLeft?: AddOn<DTO>
	addonDivider?: boolean
}

export interface BaseProperties<DTO, P extends NestedFields<DTO>> {
	label?: string
	description?: string
	default?: ResolvedPropertyType<DTO, P> | ((filter: P) => ResolvedPropertyType<DTO, P>)
	required?: HTMLInputElement['required']
	disabled?: boolean
	readOnly?: boolean
}

export interface Option<DTO, P extends NestedFields<DTO> = NestedFields<DTO>> {
	value: ResolvedPropertyType<DTO, P>
	label: string
	class?: string
	title?: string
}

export interface AutocompleteProperties<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends Omit<BaseProperties<DTO, P>, 'default'>,
		Omit<SearchAutocompleteProps, 'options'> {
	default?: ResolvedPropertyType<DTO, P> | ((filter: P) => ResolvedPropertyType<DTO, P>) | []
	options:
		| SearchAutocompleteItem[]
		| Promise<SearchAutocompleteItem[]>
		| ((search: string, formData: DTO) => Promise<SearchAutocompleteItem[]>)
}

export interface SelectProperties<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends BaseProperties<DTO, P>,
		FieldAddOns<DTO> {
	placeholder?: string
	options: Promise<Array<Option<DTO, P>>> | Array<Option<DTO, P>>
	multiple?: boolean
	autocomplete?: HTMLInputElement['autocomplete']
	colors?: { [index: string | number | symbol]: string }
}

export interface ListProperties<DTO, P extends NestedFields<DTO> = NestedFields<DTO>> extends BaseProperties<DTO, P> {
	placeholder?: string
	options: Promise<Array<Option<DTO, P>>> | Array<Option<DTO, P>>
	multiple?: boolean
	active?: string
	colors?: { [index: string | number | symbol]: string }
}

export interface SelectMultipleProperties<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends Omit<BaseProperties<DTO, P>, 'default'> {
	placeholder?: string
	multiple: true
	// default?: ResolvedPropertyType<DTO, P> | ResolvedPropertyType<DTO, P>[] | ((filter: Filter<P>) => ResolvedPropertyType<DTO, P> | ResolvedPropertyType<DTO, P>[] | null) | null
	default?: ResolvedPropertyType<DTO, P> | Array<ResolvedPropertyType<DTO, P>>
	options: Promise<Array<Option<DTO, P>>> | Array<Option<DTO, P>>
}

// TODO: extend TagsInputProps when https://github.com/skeletonlabs/skeleton/issues/3471
export interface TagsInputProps extends Omit<tagsInput.Props, 'id'> {
	/** Set the add tag input placeholder. */
	placeholder?: string
	/** Set base classes for the root. */
	base?: string
	/** Set gap classes for the root. */
	gap?: string
	/** Set padding classes for the root. */
	padding?: string
	/** Provide arbitrary classes to the root. */
	classes?: string
	/** Set base classes for the add tag input. */
	inputBase?: string
	/** Provide arbitrary classes to the add tag input. */
	inputClasses?: string
	/** Set base classes for the tag list. */
	tagListBase?: string
	/** Provide arbitrary classes to the tag list. */
	tagListClasses?: string
	/** Set base classes for each tag. */
	tagBase?: string
	/** Set background classes for each tag. */
	tagBackground?: string
	/** Provide arbitrary classes to each tag. */
	tagClasses?: string
	/** Set base classes for the edit tag input. */
	tagEditInputBase?: string
	/** Provide arbitrary classes to the edit tag input. */
	tagEditInputClasses?: string
	/** Set base classes for the delete button. */
	buttonDeleteBase?: string
	/** Provide arbitrary classes to the delete button. */
	buttonDeleteClasses?: string
	/** Set the component disabled state. */
	stateDisabled?: string
	/** The delete button label snippet. */
	buttonDelete?: Snippet
}

export type InputTagsProperties<DTO, P extends NestedFields<DTO> = NestedFields<DTO>> = BaseProperties<DTO, P> &
	TagsInputProps

// TODO: extend FileUploadProps when https://github.com/skeletonlabs/skeleton/issues/3471
export interface FileUploadProps extends Omit<fileUpload.Props, 'id'> {
	/** Set the interface text value. */
	label?: string
	/** Set the interface subtext value. */
	subtext?: string
	/** Set root base classes */
	base?: string
	/** Set root arbitrary classes */
	classes?: string
	/** Set interface base classes */
	interfaceBase?: string
	/** Set interface background classes */
	interfaceBg?: string
	/** Set interface border classes */
	interfaceBorder?: string
	/** Set interface border color classes */
	interfaceBorderColor?: string
	/** Set interface border classes */
	interfacePadding?: string
	/** Set interface border radius classes */
	interfaceRounded?: string
	/** Set interface arbitrary classes */
	interfaceClasses?: string
	/** Set interface icon classes */
	interfaceIcon?: string
	/** Set interface text classes */
	interfaceText?: string
	/** Set interface subtext classes */
	interfaceSubtext?: string
	/** Set file list base classes */
	filesListBase?: string
	/** Set file list arbitrary classes */
	filesListClasses?: string
	/** Set file base classes */
	fileBase?: string
	/** Set file background classes */
	fileBg?: string
	/** Set file gap classes */
	fileGap?: string
	/** Set file padding classes */
	filePadding?: string
	/** Set file border-radius classes */
	fileRounded?: string
	/** Set file arbitrary classes */
	fileClasses?: string
	/** Set file icon classes */
	fileIcon?: string
	/** Set file name classes */
	fileName?: string
	/** Set file size classes */
	fileSize?: string
	/** Set file button classes */
	fileButton?: string
	/** Set disabled state classes for the root. */
	stateDisabled?: string
	/** Set invalid state classes for the interface. */
	stateInvalid?: string
	/** Set dragging state classes for the interface. */
	stateDragging?: string
	/** The default children content. */
	children?: Snippet
	/** Provide an icon for the interface. */
	iconInterface?: Snippet
	/** Provide an icon proceeding each file. */
	iconFile?: Snippet
	/** Provide an icon for the remove file action. */
	iconFileRemove?: Snippet
}

export type FileUploadProperties<DTO, P extends NestedFields<DTO> = NestedFields<DTO>> = BaseProperties<DTO, P> &
	FileUploadProps

export interface InputProperties<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends BaseProperties<DTO, P>,
		FieldAddOns<DTO> {
	title?: string
	type?: HTMLInputElement['type']
	placeholder?: string
	readonly?: boolean
	multiple?: boolean
	autocomplete?: HTMLInputElement['autocomplete']
	tabindex?: number
	pattern?: HTMLInputElement['pattern']
	accept?: HTMLInputElement['accept']
	max?: HTMLInputElement['max']
	maxLength?: HTMLInputElement['maxLength']
	min?: HTMLInputElement['min']
	minLength?: HTMLInputElement['minLength']
}

export interface RadioProperties<DTO, P extends NestedFields<DTO> = NestedFields<DTO>> extends BaseProperties<DTO, P> {
	options: Promise<Array<Option<DTO, P>>> | Array<Option<DTO, P>>
	colors?: { [index: string | number | symbol]: string }
}

export interface TextAreaProperties<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends BaseProperties<DTO, P> {
	title?: string
	placeholder?: string
	readonly?: boolean
	multiple?: boolean
	autocomplete?: HTMLTextAreaElement['autocomplete']
	tabindex?: number
	maxLength?: HTMLTextAreaElement['maxLength']
	minLength?: HTMLTextAreaElement['minLength']
	cols?: HTMLTextAreaElement['cols']
	rows?: HTMLTextAreaElement['rows']
}

export interface DateProperties<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends BaseProperties<DTO, P>,
		Omit<DatePicker['$$prop_def'], 'startDate' | 'endDate' | 'startDateTime' | 'endDateTime'> {
	isRange?: false
}

export interface DateRangeProperties<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends BaseProperties<DTO, P>,
		Omit<DatePicker['$$prop_def'], 'startDate' | 'endDate' | 'startDateTime' | 'endDateTime'> {
	isRange: true
	start: NestedFields<ResolvedPropertyType<DTO, P>>
	end?: NestedFields<ResolvedPropertyType<DTO, P>>
}

// export interface CustomComponentProperties<Value = unknown, Result = Value> {
// 	value?: Value
// 	onValueChange?: (value?: Result) => void
// 	disabled?: boolean
// 	required?: boolean
// 	[key: string]: any
// }

// export interface CustomProperties<
// 	DTO,
// 	P extends NestedFields<DTO> = NestedFields<DTO>,
// 	// eslint-disable-next-line @typescript-eslint/no-explicit-any
// 	S extends CustomComponentProperties<any, ResolvedPropertyType<DTO, P>> = CustomComponentProperties<
// 		// eslint-disable-next-line @typescript-eslint/no-explicit-any
// 		any,
// 		ResolvedPropertyType<DTO, P>
// 	>,
// > extends BaseProperties<DTO, P> {
// 	snippet: (props: S) => ReturnType<Snippet<[S]>>
// 	placeholder?: string
// }

// export interface CustomProperties<
// 	DTO,
// 	C extends Component<CustomComponentProperties<any, ResolvedPropertyType<DTO, P>>>,
// 	P extends NestedFields<DTO> = NestedFields<DTO>,
// > extends BaseProperties<DTO, P> {
// 	component: Partial<C>
// 	props?: ComponentProps<C>
// 	placeholder?: string
// }

export interface CustomProperties<
	DTO,
	P extends NestedFields<DTO> = NestedFields<DTO>,
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	C extends Component<any> = Component<any>,
> extends BaseProperties<DTO, P> {
	component: C
	props?: ComponentProps<C>
	placeholder?: string
}

export interface BreakProperties {
	label?: string
	description?: string
	size?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
	classes?: string
	disabled?: false
	readOnly?: false
}

export type Properties<
	DTO,
	P extends NestedFields<DTO> = NestedFields<DTO>,
	T extends FieldType = FieldType,
> = T extends FieldType.SELECT
	? SelectProperties<DTO, P> | SelectMultipleProperties<DTO, P>
	: T extends FieldType.INPUT
		? InputProperties<DTO, P>
		: T extends FieldType.RADIO
			? RadioProperties<DTO, P>
			: T extends FieldType.AUTOCOMPLETE
				? AutocompleteProperties<DTO, P>
				: T extends FieldType.TEXTAREA
					? TextAreaProperties<DTO, P>
					: T extends FieldType.LIST
						? ListProperties<DTO, P>
						: T extends FieldType.INPUT_TAGS
							? InputTagsProperties<DTO, P>
							: T extends FieldType.DATEPICKER
								? DateProperties<DTO, P> | DateRangeProperties<DTO, P>
								: T extends FieldType.FILE_UPLOAD
									? FileUploadProperties<DTO, P>
									: T extends FieldType.CUSTOM
										? CustomProperties<DTO, P>
										: T extends FieldType.HIDDEN
											? InputProperties<DTO, P>
											: T extends FieldType.BREAK
												? BreakProperties
												: BaseProperties<DTO, P>

export interface BaseField<DTO, P extends NestedFields<DTO> = NestedFields<DTO>> {
	key: P | 'break'
	isLoading?: boolean
	hide?: string | ((data: Partial<DTO>) => boolean) | boolean
	resetOn?: Array<NestedFields<DTO>>
	dependent?: Array<NestedFields<DTO>>
}

export type Field<DTO> = ExtractValues<{
	[P in NestedFields<DTO>]: ExtractValues<{
		[T in FieldType]: {
			type: T
			props: Properties<DTO, P, T>
		} & BaseField<DTO, P>
	}>
}>

export interface FieldGroup<DTO> {
	title: string
	fields: Array<Field<DTO>>
	divider?: boolean
	hide?: string | ((data: Partial<DTO>) => boolean) | boolean
	description?: string
}

export interface FieldTabs<DTO> {
	title?: string
	tabs: Array<FieldGroup<DTO>>
	divider?: boolean
	hide?: string | ((data: Partial<DTO>) => boolean) | boolean
	description?: string
}

export type Form<DTO> = Array<Field<DTO> | FieldGroup<DTO> | FieldTabs<DTO>>

/**
 * The configuration for the form modal.
 */
export interface FormModalConfig<DTO extends DocumentNode> {
	fields: Form<DTO>
	data?: Partial<DTO>

	onValueChange?: (data: DTO) => Promise<void> | void
	onSubmit?: (data: DTO) => Promise<void> | void
	onClose?: (data: DTO) => Promise<void> | void

	reset?: boolean | string
	onReset?: (data: DTO) => Promise<void> | void
}

/**
 * Utilities
 */

export interface ChangeField<DTO, P extends NestedFields<DTO> = NestedFields<DTO>> {
	key: P
	change: 'disable' | 'enable' | 'hide' | 'show' | 'readOnly'
}

export interface ChangeGroup {
	group: string
	change: 'hide' | 'show'
}

/**
 * Applies a set of changes to a given form structure and returns a new form instance with those changes.
 *
 * This function clones the provided form, flattens its fields (including groups and tabs),
 * and then applies each change from the `changes` array. Changes can target either groups or individual fields,
 * allowing for toggling visibility (`hide`/`show`) and enabling/disabling fields.
 *
 * @template DTO - The data transfer object type associated with the form.
 * @param form - The original form structure to apply changes to.
 * @param changes - An array of changes to apply. Each change can target a field or a group.
 * @returns A new form instance with the specified changes applied.
 */
export function applyChanges<DTO>(form: Form<DTO>, changes: Array<ChangeField<Partial<DTO>> | ChangeGroup>) {
	const newForm = clone(form)
	const fattenFields = newForm.flatMap(
		(field) =>
			(field as FieldGroup<DTO>).fields ||
			(field as FieldTabs<DTO>).tabs?.flatMap((field) => (field as FieldGroup<DTO>).fields || field) ||
			field,
	)

	for (const change of changes) {
		if ('group' in change) {
			const group = newForm.find((field) => 'title' in field && field.title === change.group)

			if (group) {
				if (change.change === 'hide') {
					group.hide = true
				} else if (change.change === 'show') {
					group.hide = false
				}
			}
		} else {
			const field = fattenFields.find((field) => field.key === change.key)

			if (field) {
				if (change.change === 'disable') {
					field.props.disabled = true
				} else if (change.change === 'enable') {
					field.props.disabled = false
				} else if (change.change === 'hide') {
					field.hide = true
				} else if (change.change === 'show') {
					field.hide = false
				} else if (change.change === 'readOnly') {
					field.props.readOnly = true
				}
			}
		}
	}

	return newForm as Form<DTO>
}

/**
 * Clears and reconstructs form data by extracting values from a partial DTO object
 * based on the keys defined in the form structure. Handles nested fields, field groups,
 * and tabbed fields, preserving only the relevant values for each field.
 *
 * @template DTO - The data transfer object type representing the form data structure.
 * @param data - A partial DTO object containing the current form data.
 * @param form - The form definition, which may include fields, field groups, and tabs.
 * @returns A new partial DTO object containing only the values corresponding to the form's fields.
 */
export function clearFormData<DTO>(data: Partial<DTO>, form: Form<DTO>): Partial<DTO> {
	const validKeys = new Set(
		form
			.flatMap(
				(field) =>
					(field as FieldGroup<DTO>).fields ||
					(field as FieldTabs<DTO>).tabs?.flatMap((field) => (field as FieldGroup<DTO>).fields || field) ||
					field,
			)
			.filter((field) => field.type !== FieldType.BREAK)
			.map((field) => field.key as NestedFields<DTO>),
	)

	function filterObject<T>(obj: T, prefix = ''): Partial<T> | undefined {
		if (obj === null || obj === undefined) return obj as Partial<T>
		if (typeof obj !== 'object' || obj instanceof Date || obj instanceof RegExp) {
			return obj as Partial<T>
		}

		const result = (Array.isArray(obj) ? [] : {}) as Partial<T>

		for (const [key, value] of Object.entries(obj)) {
			const fullKey = prefix ? `${prefix}.${key}` : key

			// Type-safe key checking
			const isValidKey = validKeys.has(fullKey as NestedFields<DTO>)
			const hasValidChildren = Array.from(validKeys).some((validKey) => String(validKey).startsWith(fullKey + '.'))

			if (isValidKey || hasValidChildren) {
				const filteredValue = filterObject(value, fullKey)
				if (filteredValue !== undefined && filteredValue !== null) {
					;(result as Record<string, unknown>)[key] = filteredValue
				}
			}
		}

		return Object.keys(result).length > 0 ? result : undefined
	}

	return filterObject(data) ?? ({} as Partial<DTO>)
}

// export type F<DTO extends DocumentNode> = Field<DTO> | Omit<Field<DTO>, 'isLoading' | 'hide' | 'resetOn' | 'dependent'>

// Helper type that ensures Field<DTO> and F<DTO> are compatible
export type F<DTO extends DocumentNode> = Field<DTO>

// // --- FieldValue Utility Type ---
// // Takes a specific Field type T from the F<DTO> union and returns its value type
// export type FieldValue<T extends F<DTO>, DTO extends DocumentNode> = T extends { type: FieldType.INPUT }
// 	? // HTML input values are strings, numbers might need parsing later
// 		string | number | undefined
// 	: T extends { type: FieldType.TEXTAREA }
// 		? string | undefined
// 		: T extends { type: FieldType.SELECT; props: SelectProperties<DTO, infer V> }
// 			? // Single select: value is the type of Option.value
// 				V | undefined
// 			: T extends { type: FieldType.SELECT; props: SelectMultipleProperties<DTO, infer V> }
// 				? // Multi select: value is an array of Option.value types
// 					V[] | undefined
// 				: T extends { type: FieldType.RADIO; props: RadioProperties<DTO, infer V> }
// 					? // Radio: value is the type of the selected Option.value
// 						V | undefined
// 					: T extends { type: FieldType.LIST; props: ListProperties<DTO, infer V> }
// 						? // List: Depends on how you use it, often an array of the option type
// 							V[] | undefined // Adjust if your List component returns something else
// 						: T extends { type: FieldType.INPUT_TAGS }
// 							? // InputTags usually deals with strings
// 								string[] | undefined
// 							: T extends { type: FieldType.FILE; props: { multiple?: false } }
// 								? // Single File: File object
// 									File | undefined
// 								: T extends { type: FieldType.FILE; props: { multiple: true } }
// 									? // Multiple Files: FileList or Array<File>
// 										FileList | File[] | undefined
// 												FileList | File[] | undefined
// 											: T extends { type: FieldType.AUTOCOMPLETE; props: AutocompleteProperties<DTO, infer P, infer M> }
// 												? M extends true // Check if 'multiple' is true (inferred as M)
// 													? Array<ResolvedPropertyType<DTO, P>> // If multiple, the value is an array of the resolved type
// 													: ResolvedPropertyType<DTO, P> | undefined // If single, the value is the resolved type or undefined
// 												: // eslint-disable-next-line @typescript-eslint/no-explicit-any
// 													T extends { type: FieldType.CUSTOM; props: CustomProperties<infer DTO, infer P, any> }
// 													? ResolvedPropertyType<DTO, P> | undefined
// 													: T extends { type: FieldType.HIDDEN }
// 														? // Hidden fields still have a value type corresponding to the DTO key
// 															string
// 														: // BREAK has no value, other unhandled types
// 															undefined
export function isAutocomplete<DTO extends DocumentNode>(
	field: Field<DTO>,
): field is Field<DTO> & {
	type: FieldType.AUTOCOMPLETE
	props: AutocompleteProperties<DTO>
} {
	return field.type === FieldType.AUTOCOMPLETE
}
export function isInput<DTO extends DocumentNode>(
	field: Field<DTO>,
): field is Field<DTO> & {
	type: FieldType.INPUT
	props: InputProperties<DTO>
} {
	return field.type === FieldType.INPUT
}
export function isRadio<DTO extends DocumentNode>(
	field: Field<DTO>,
): field is Field<DTO> & {
	type: FieldType.RADIO
	props: RadioProperties<DTO>
} {
	return field.type === FieldType.RADIO
}
export function isTextArea<DTO extends DocumentNode>(
	field: Field<DTO>,
): field is Field<DTO> & {
	type: FieldType.TEXTAREA
	props: TextAreaProperties<DTO>
} {
	return field.type === FieldType.TEXTAREA
}
export function isSelect<DTO extends DocumentNode>(
	field: Field<DTO>,
): field is Field<DTO> & {
	type: FieldType.SELECT
	props: SelectProperties<DTO> | SelectMultipleProperties<DTO>
} {
	return field.type === FieldType.SELECT
}
export function isList<DTO extends DocumentNode>(
	field: Field<DTO>,
): field is Field<DTO> & { type: FieldType.LIST; props: ListProperties<DTO> } {
	return field.type === FieldType.LIST
}
export function isInputTags<DTO extends DocumentNode>(
	field: Field<DTO>,
): field is Field<DTO> & {
	type: FieldType.INPUT_TAGS
	props: InputTagsProperties<DTO>
} {
	return field.type === FieldType.INPUT_TAGS
}
export function isDatePicker<DTO extends DocumentNode>(
	field: Field<DTO>,
): field is Field<DTO> & {
	type: FieldType.DATEPICKER
	props: DateProperties<DTO> | DateRangeProperties<DTO>
} {
	return field.type === FieldType.DATEPICKER
}
export function isFileUpload<DTO extends DocumentNode>(
	field: Field<DTO>,
): field is Field<DTO> & {
	type: FieldType.FILE_UPLOAD
	props: FileUploadProperties<DTO>
} {
	return field.type === FieldType.FILE_UPLOAD
}
export function isCustom<DTO extends DocumentNode>(
	field: Field<DTO>,
): field is Field<DTO> & {
	type: FieldType.CUSTOM
	props: CustomProperties<DTO>
} {
	return field.type === FieldType.CUSTOM
}
