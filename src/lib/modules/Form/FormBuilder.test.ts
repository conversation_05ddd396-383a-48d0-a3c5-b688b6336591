// https://github.com/testing-library/jest-dom/issues/515
/// <reference types="@testing-library/jest-dom" />

import { render } from '@testing-library/svelte'
import { tick } from 'svelte'
import { test, expect } from 'vitest'

import FormBuilder from './FormBuilder.svelte'

test('Render empty form', async () => {
	const { getByRole } = render(FormBuilder, {
		fields: [],
		submit: 'Submit',
	})

	await tick()

	expect(getByRole('button', { name: 'Submit' })).toBeInTheDocument()
})

test('Do note render submit button if option not provided', async () => {
	const { queryByRole } = render(FormBuilder, {
		fields: [],
	})

	await tick()

	expect(queryByRole('button')).not.toBeInTheDocument()
})
