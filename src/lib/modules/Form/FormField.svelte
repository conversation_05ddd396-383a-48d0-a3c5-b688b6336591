<script lang="ts" generics="DTO extends DocumentNode">
	import { TagsInput, Segment, FileUpload } from '@skeletonlabs/skeleton-svelte'

	import DatePicker from './components/DatePicker.svelte'
	import SearchAutocomplete from './components/SearchAutocomplete.svelte'
	import {
		FieldType,
		isAutocomplete,
		isCustom,
		isDatePicker,
		isFileUpload,
		isInput,
		isInputTags,
		isList,
		isRadio,
		isSelect,
		isTextArea,
		type AutocompleteProperties,
		type Field,
		type FieldAddOns,
		type Properties,
	} from './form.interface'
	import FormFieldWrapper from './FormFieldWrapper.svelte'
	import ToggleGroup from '../ToggleGroup'

	import type { DocumentNode } from '$lib/utils'

	const uid = 'field' + Math.random().toString(36)

	let {
		id = uid,
		field,
		value = $bindable(undefined),
		formData = {} as DTO,
		disabled = false,
		readOnly = false,

		labelClass = 'mb-2',
		descriptionClass = 'mt-3',

		onValueChange,
	}: {
		id?: string
		field: Field<DTO>
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		value?: any
		formData?: DTO
		disabled?: boolean
		readOnly?: boolean

		labelClass?: string
		descriptionClass?: string

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		onValueChange?: (value?: any) => void
	} = $props()

	const colors = $derived(
		(field.props as { colors?: { [index: string | number | symbol]: string } }).colors ?? undefined,
	)
	const color = (option: { value: unknown }, colors: { [index: string]: string } | undefined) => {
		if (colors) {
			const color = colors[option.value as string]
			if (color) return `checked:!bg-[${color}]`
		}

		return ''
	}

	function hasAddon(props: unknown): props is Properties<DTO> & FieldAddOns<DTO> {
		return (props as FieldAddOns<DTO>).addonLeft !== undefined || (props as FieldAddOns<DTO>).addonRight !== undefined
	}

	async function resolveSearchAutocompleteOptions(options: AutocompleteProperties<DTO>['options']) {
		if (typeof options !== 'function') return options

		const originalOptions = await Promise.resolve(options)

		if (typeof originalOptions !== 'function') return originalOptions

		return (search: string) => originalOptions(search, formData)
	}
</script>

{#if field.type === FieldType.BREAK}
	<div class={field.props.classes ?? 'space-y-3'}>
		<hr class="hr" />

		{#if field.props.label}
			<svelte:element this={field.props.size ?? 'h5'} class={field.props.size ?? 'h5'}
				>{field.props.label}</svelte:element
			>
		{/if}
		{#if field.props.description}
			<p class="text-xs font-light whitespace-pre-line {descriptionClass}">{field.props.description}</p>
		{/if}
	</div>
{:else if field.type === FieldType.HIDDEN}
	<input {...field.props} type="hidden" name={field.key} {value} />
{:else}
	<!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
	<!-- svelte-ignore a11y_click_events_have_key_events -->
	<label
		{id}
		class={isInput(field) && (field.props.type === 'checkbox' || field.props.type === 'radio')
			? 'flex items-center space-x-2 py-2'
			: 'label'}
		onclick={(e) => e.preventDefault()}
	>
		{#if field.props.label && !(isInput(field) && (field.props.type === 'checkbox' || field.props.type === 'radio'))}
			<span class="label-text {labelClass}">{field.props.label}{field.props.required ? ' *' : ''}</span>
		{/if}

		<FormFieldWrapper properties={field.props} {value} {formData}>
			{#if isAutocomplete(field)}
				{#await resolveSearchAutocompleteOptions(field.props.options) then options}
					<SearchAutocomplete
						{...field.props}
						bind:value
						{options}
						{onValueChange}
						disabled={field.props.disabled || disabled}
						readOnly={field.props.readOnly || readOnly}
					/>
				{/await}
			{:else if isInput(field)}
				{#if field.props.type === 'checkbox'}
					<input
						bind:checked={value}
						class="checkbox"
						{...field.props}
						type="checkbox"
						name={field.key}
						onchange={() => onValueChange?.(value)}
						disabled={field.props.disabled || disabled}
						readonly={field.props.readOnly || readOnly}
					/>
					<p>{field.props.label}</p>
				{:else if field.props.type === 'radio'}
					<input
						bind:value
						class="radio"
						{...field.props}
						name={field.key}
						onchange={() => onValueChange?.(value)}
						disabled={field.props.disabled || disabled}
						readonly={field.props.readOnly || readOnly}
					/>
					<p>{field.props.label}</p>
				{:else}
					<input
						bind:value
						class={hasAddon(field.props) ? 'ig-input' : 'input'}
						{...field.props}
						name={field.key}
						onchange={() => onValueChange?.(value)}
						disabled={field.props.disabled || disabled}
						readonly={field.props.readOnly || readOnly}
					/>
				{/if}
			{:else if isTextArea(field)}
				<textarea
					class="textarea"
					{...field.props}
					bind:value
					name={field.key}
					onchange={() => onValueChange?.(value)}
					disabled={field.props.disabled || disabled}
					readonly={field.props.readOnly || readOnly}
				></textarea>
			{:else if isRadio(field)}
				{#await field.props.options}
					<div class="placeholder h-10 animate-pulse rounded-sm"></div>
				{:then options}
					{#if options}
						<Segment
							{...field.props}
							{value}
							onValueChange={(e) => {
								value = e.value
								onValueChange?.(e.value)
							}}
							indicatorClasses="preset-tonal-secondary border border-secondary-500"
							disabled={field.props.disabled || disabled || field.props.readOnly || readOnly}
						>
							{#each options as option (option.label)}
								<Segment.Item
									value={option.value as string}
									classes="hover:preset-tonal-secondary {color(option, colors)}">{option.label}</Segment.Item
								>
							{/each}
						</Segment>
					{:else}
						<div class="placeholder h-10 animate-pulse rounded-sm"></div>
					{/if}
				{/await}
			{:else if isSelect(field)}
				{#await field.props.options}
					<div class="placeholder h-10 animate-pulse rounded-sm"></div>
				{:then options}
					{#if options?.length}
						<select
							class={hasAddon(field.props) ? 'ig-select' : 'select'}
							{...field.props}
							bind:value
							name={field.key}
							onchange={() => onValueChange?.(value)}
							disabled={field.props.disabled || disabled || field.props.readOnly || readOnly}
						>
							{#each options as option (option.label)}
								<option value={option.value as string} title={option.title} class={color(option, colors)}
									>{option.label}</option
								>
							{/each}
						</select>
					{:else}
						<div class="placeholder h-10 animate-pulse rounded-sm"></div>
					{/if}
				{/await}
			{:else if isList(field)}
				{#await field.props.options}
					<div class="placeholder h-10 animate-pulse rounded-sm"></div>
				{:then options}
					{#if options}
						<ToggleGroup
							{...field.props}
							{value}
							onValueChange={(e) => {
								value = e.value
								onValueChange?.(e.value)
							}}
							disabled={field.props.disabled || disabled || field.props.readOnly || readOnly}
						>
							{#each options as option (option.label)}
								<ToggleGroup.Item
									value={option.value as string}
									classes="hover:preset-tonal-secondary {color(option, colors)}"
									statePressed={field.props.active ?? 'preset-filled-primary-500'}>{option.label}</ToggleGroup.Item
								>
							{/each}
						</ToggleGroup>
					{/if}
				{/await}
			{:else if isInputTags(field)}
				<TagsInput
					{...field.props}
					{value}
					onValueChange={(e) => {
						value = e.value
						onValueChange?.(e.value)
					}}
					disabled={field.props.disabled || disabled}
				/>
			{:else if isDatePicker(field)}
				<DatePicker {field} bind:value {onValueChange} disabled={field.props.disabled || disabled} />
			{:else if isFileUpload(field)}
				<FileUpload
					{...field.props}
					onFileChange={() => onValueChange?.(value)}
					disabled={field.props.disabled || disabled}
				/>
			{:else if isCustom(field)}
				<!-- {@render field.props.snippet({ value, disabled, required: field.props.required, onValueChange })} -->
				<field.props.component
					{...field.props.props}
					{value}
					disabled={field.props.disabled || disabled}
					readonly={field.props.readOnly || readOnly}
					onValueChange={async (...args: unknown[]) => {
						if (onValueChange && field.props.props?.onValueChange) {
							value = await field.props.props.onValueChange(...args)
							return onValueChange?.(value)
						}
						onValueChange?.(...args)
					}}
				/>
			{/if}
		</FormFieldWrapper>

		{#if field.props.description}
			<p class="text-xs font-light whitespace-pre-line {descriptionClass}">{field.props.description}</p>
		{/if}
	</label>
{/if}
