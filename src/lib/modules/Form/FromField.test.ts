// https://github.com/testing-library/jest-dom/issues/515
/// <reference types="@testing-library/jest-dom" />

import { render } from '@testing-library/svelte'
import { tick } from 'svelte'
import { test, expect } from 'vitest'

import { FieldType } from './form.interface'
import <PERSON><PERSON><PERSON> from './FormField.svelte'

test('handle empty options for Autocomplete, Select, Radio, and List components', async () => {
	const { getByPlaceholderText } = render(FormField, {
		props: {
			field: {
				key: 'test',
				type: FieldType.AUTOCOMPLETE,
				props: {
					label: 'Test Autocomplete',
					placeholder: 'Search...',
					options: [],
					multiple: false,
				},
			},
			value: undefined,
			formData: {},
			disabled: false,
		},
	})

	await tick()
	await tick()

	expect(getByPlaceholderText('Search...')).toBeInTheDocument()
})
