<script lang="ts" generics="DTO extends DocumentNode">
	import { ModalComponent, type ModalInstance } from '$lib/modules/Modal'

	import FormBuilder from './FormBuilder.svelte'

	import type { DocumentNode } from '$lib/utils'
	import type { FormModalConfig } from './form.interface'

	const {
		fields,
		data = {} as Partial<DTO>,
		onValueChange,
		reset,
		onReset,

		protect,

		closeLabel,
		submitLabel,
		onSubmit,
		onClose,
		...rest
	}: ModalInstance & FormModalConfig<DTO> = $props()

	let isLoading = $state(false)
	let value = $derived(data)
	let _fields = $derived(fields)

	const styles = $derived({
		titleSize: 'h5' as const,

		groupClass:
			'card bg-noise bg-surface-50-950 space-y-8 not-first:mt-8 border border-surface-100 dark:border-surface-800 p-4',

		descriptionFont: 'text-xs font-light whitespace-pre-line text-surface-700 dark:text-surface-300',
	})
</script>

<ModalComponent {...rest} headerClasses="sticky top-0" {onClose} closeLabel={false}>
	<FormBuilder
		bind:value
		bind:fields={_fields}
		bind:isLoading
		submit={submitLabel || 'Submit'}
		{onSubmit}
		close={closeLabel ?? true}
		{onClose}
		reset={reset ?? false}
		{onReset}
		{...styles}
		{onValueChange}
		onDirtyChange={protect}
	/>
</ModalComponent>
