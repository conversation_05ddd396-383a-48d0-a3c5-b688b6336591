<script lang="ts" generics="DTO">
	import type { FieldAddOns, IconAddOn, ImageAddOn, TextAddOn, Properties, AddOn } from './form.interface'
	import type { Snippet } from 'svelte'

	const {
		children,
		properties,
		value,
		formData,
	}: { children: Snippet; properties: Properties<DTO>; value: unknown; formData: DTO } = $props()

	function hasAddon(props: unknown): props is Properties<DTO> & FieldAddOns<DTO> {
		return (props as FieldAddOns<DTO>).addonLeft !== undefined || (props as FieldAddOns<DTO>).addonRight !== undefined
	}

	function isImageAddOn(addon: AddOn<DTO>): addon is ImageAddOn {
		return (addon as ImageAddOn).img !== undefined
	}

	function isIconAddOn(addon: AddOn<DTO>): addon is IconAddOn {
		return (addon as IconAddOn).icon !== undefined
	}

	function isTextAddOn(addon: AddOn<DTO>): addon is TextAddOn {
		return (addon as TextAddOn).text !== undefined
	}

	function hasIconSet(addon: IconAddOn) {
		return addon.iconSet !== undefined
	}

	function isButton(addon: ImageAddOn | IconAddOn | TextAddOn) {
		return addon?.onclick !== undefined
	}

	function resolveGrid(properties: FieldAddOns<DTO>) {
		if (properties.addonLeft && properties.addonRight) return 'grid-cols-[auto_1fr_auto]'
		if (properties.addonLeft) return 'grid-cols-[auto_1fr]'
		return 'grid-cols-[1fr_auto]'
	}

	function validUrl(url?: unknown) {
		return typeof url === 'string' && /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/.test(url ?? '')
	}

	async function resolveAddon(addon: AddOn<DTO> | undefined) {
		if (!addon) return
		if (typeof addon === 'function') {
			return addon(formData)
		}

		return addon
	}
</script>

{#if hasAddon(properties)}
	<div class="input-group {resolveGrid(properties)}" class:input-group-divider={properties.addonDivider}>
		{#await resolveAddon(properties.addonLeft) then addon}
			{#if addon}
				<!-- svelte-ignore a11y_no_static_element_interactions -->
				<svelte:element
					this={isButton(addon) ? 'button' : 'div'}
					class:input-group-shim={addon.shim}
					onclick={(e: MouseEvent) => {
						e.preventDefault()
						addon.onclick?.(e)
					}}
					class="{isButton(addon) ? 'ig-btn preset-filled-secondary-500' : 'ig-cell preset-tonal'}  {isImageAddOn(addon)
						? 'w-12 px-0! py-0.5!'
						: ''}"
				>
					{#if isImageAddOn(addon)}
						{#if validUrl(value)}
							<img
								src={addon.img === 'key' && typeof value === 'string' ? value : addon.img}
								alt=""
								class={`${addon.rounded === true ? 'rounded-full' : (addon.rounded ?? '')} mx-auto max-h-8`}
							/>
						{/if}
					{:else if isIconAddOn(addon)}
						<iconify-icon
							icon={`${hasIconSet(addon) ? addon.iconSet + ':' : ''}${addon.icon === 'key' && typeof value === 'string' ? value : addon.icon}`}
							inline
						></iconify-icon>
					{:else if isTextAddOn(addon)}
						{addon.text === 'key' && typeof value === 'string' ? value : addon.text}
					{/if}
				</svelte:element>
			{/if}
		{/await}

		{@render children()}

		{#await resolveAddon(properties.addonRight) then addon}
			{#if addon}
				<!-- svelte-ignore a11y_no_static_element_interactions -->
				<svelte:element
					this={isButton(addon) ? 'button' : 'div'}
					class:input-group-shim={addon.shim}
					onclick={(e: MouseEvent) => {
						e.preventDefault()
						addon.onclick?.(e)
					}}
					class="{isButton(addon) ? 'ig-btn preset-filled-secondary-500' : 'ig-cell preset-tonal'}  {isImageAddOn(addon)
						? 'w-12 px-0! py-0.5!'
						: ''}"
				>
					{#if isImageAddOn(addon)}
						{#if validUrl(value)}
							<img
								src={addon.img === 'key' && typeof value === 'string' ? value : addon.img}
								alt=""
								class={`${addon.rounded === true ? 'rounded-full' : (addon.rounded ?? '')} mx-auto max-h-8`}
							/>
						{/if}
					{:else if isIconAddOn(addon)}
						<iconify-icon
							icon={`${hasIconSet(addon) ? addon.iconSet + ':' : ''}${addon.icon === 'key' && typeof value === 'string' ? value : addon.icon}`}
							inline
						></iconify-icon>
					{:else if isTextAddOn(addon)}
						{addon.text === 'key' && typeof value === 'string' ? value : addon.text}
					{/if}
				</svelte:element>
			{/if}
		{/await}
	</div>
{:else}
	{@render children()}
{/if}

<style>
	iconify-icon {
		display: inline-block;
		min-width: 1rem;
		min-height: 1rem;
	}
</style>
