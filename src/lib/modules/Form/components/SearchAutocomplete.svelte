<script lang="ts" module>
	export interface SearchAutocompleteProps {
		id?: string

		options:
			| Promise<SearchAutocompleteItem[]>
			| SearchAutocompleteItem[]
			| ((search: string) => Promise<SearchAutocompleteItem[]>)
		debounce?: number

		value?: string[] | string
		onValueChange?: (value?: string[], selected?: SearchAutocompleteItem[]) => void
		multiple?: boolean

		input?: string
		onInputChange?: (value?: string) => void

		placeholder?: string
		required?: boolean
		disabled?: boolean
		autoFocus?: boolean
		readOnly?: boolean
		inputBehavior?: 'autohighlight' | 'autocomplete' | 'none'
		selectionBehavior?: 'clear' | 'replace' | 'preserve'

		option?: Snippet<[SearchAutocompleteItem]>

		tagListClasses?: string
		tagBackground?: string
		tagClasses?: string
		tagDelete?: Snippet
		tagDeleteClasses?: string
	}

	export interface SearchAutocompleteItem<Meta = unknown> {
		/** Provide a unique display label per option. Supports HTML. */
		label: string
		/** Provide an emoji to display next to the label. */
		emoji?: string | null
		/** Provide an image to display next to the label. */
		image?: string | null
		/** Provide a unique option value. */
		value: string
		/** Provide a comma separated list of keywords. */
		keywords?: string
		/** Pass arbitrary data per option. */
		meta?: Meta
	}
</script>

<script lang="ts">
	import { Combobox, ProgressRing } from '@skeletonlabs/skeleton-svelte'
	import { resource } from 'runed'
	import { type Snippet } from 'svelte'

	const uid = 'search-autocomplete' + Math.random().toString(36)

	let {
		id = uid,

		options,
		debounce = 300,

		value = $bindable(),
		onValueChange,
		multiple,

		input = $bindable(),
		onInputChange,

		placeholder = 'Search users',
		required = false,
		disabled = false,
		autoFocus = false,
		readOnly = false,
		inputBehavior = 'none',
		selectionBehavior = 'replace',

		option,

		tagListClasses = '',
		tagBackground = 'preset-filled-surface-500',
		tagClasses = '',
		tagDelete,
		tagDeleteClasses = '',
	}: SearchAutocompleteProps = $props()

	let _value = $derived(value !== undefined && !Array.isArray(value) ? [value] : value)

	const query = resource(
		() => input,
		async (input, prevInput, { data }) => {
			if (typeof options === 'function') {
				if (!data) {
					return options(input ?? '')
				}

				if (
					typeof input !== 'undefined' &&
					input !== prevInput &&
					!data.some((item: SearchAutocompleteItem) => item.label === input && value?.includes(item.value))
				) {
					return options(input)
				}
			}

			return options as Promise<SearchAutocompleteItem[]>
		},
		{
			debounce,
			// lazy: Skip initial fetch when true
			// once: Only fetch once when true
			once: !(typeof options === 'function' && !(options instanceof Promise)),
			// initialValue: Provides an initial value for the resource
			// debounce: Debounce rapid changes
			// throttle: Throttle rapid changes
		},
	)

	function onChipRemove(index: number) {
		if (Array.isArray(value) && index > -1) {
			value.splice(index, 1)

			const selected = query.current?.filter((item) => value?.includes(item.value))
			onValueChange?.(value, selected)
		}
	}

	function _onValueChange(v?: string[]) {
		if (v && value !== undefined && (!Array.isArray(value) || !multiple)) {
			value = v[0]
		} else {
			value = v
		}

		if (v) {
			const selected = query.current?.filter((item) => v.includes(item.value))
			onValueChange?.(v, selected)
		} else {
			onValueChange?.(undefined, undefined)
		}
	}

	const selected = $derived(query.current?.filter((item) => _value?.includes(item.value)) || [])
</script>

<div {id} class="flex flex-col">
	<Combobox
		data={query.current}
		inputValue={!multiple && (disabled || readOnly) && selected?.length ? selected?.[0]?.label : input}
		onInputValueChange={(e) => {
			input = e.inputValue
			onInputChange?.(input)
		}}
		value={_value}
		onValueChange={(e) => {
			_value = e.value
			_onValueChange?.(_value)
		}}
		{placeholder}
		{multiple}
		{required}
		{disabled}
		{readOnly}
		{inputBehavior}
		{selectionBehavior}
		openOnClick={true}
		{autoFocus}
		onclick={(e) => {
			if (query.loading || disabled || readOnly) {
				e.stopPropagation()
				e.preventDefault()
			} else if (_value?.length) {
				e.stopPropagation()
				e.preventDefault()

				_value = undefined
				input = ''

				_onValueChange?.(_value)
				onInputChange?.(input)
			}
		}}
		zIndex="1"
	>
		<!-- This is optional. Combobox will render label by default -->
		{#snippet item(i)}
			{#if option}
				{@render option(i)}
			{:else if i.emoji}
				<div class="flex w-full justify-between space-x-2">
					<span>{i.label}</span>
					<span>{i.emoji}</span>
				</div>
			{:else if i.image}
				<div class="flex w-full justify-between space-x-2">
					<span>{i.label}</span>
					<img src={i.image} alt="" class="max-h-10 max-w-20 object-contain" />
				</div>
			{:else}
				{i.label}
			{/if}
		{/snippet}

		{#snippet arrow()}
			{#if query.loading}
				<ProgressRing
					classes="absolute! top-1.5 right-2.5 z-20"
					value={null}
					strokeWidth="100px"
					meterStroke="stroke-secondary-500"
					trackStroke="stroke-secondary-500/30"
					size="size-7"
				/>
			{:else if _value?.length && !disabled && !readOnly}
				<strong>×</strong>
			{:else}
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					stroke-width="2"
					stroke-linecap="round"
					stroke-linejoin="round"
					style="opacity: 0.5"
				>
					<path d="m6 9 6 6 6-6" />
				</svg>
			{/if}
		{/snippet}
	</Combobox>

	{#if multiple && Array.isArray(selected) && selected.length > 0}
		<!-- Tag List -->
		<div class={`grid !h-auto gap-2 p-3 ${disabled ? 'disabled' : ''}`}>
			<div class="flex flex-wrap gap-1 {tagListClasses}">
				{#each selected as item, index (item)}
					<!-- Tag -->
					<div>
						<!-- Display -->
						<div class="chip {tagBackground} {tagClasses}">
							<span>{item.label}</span>
							<!-- Delete Button -->
							{#if !disabled && !readOnly}
								<button
									onclick={(e) => {
										e.stopPropagation()
										onChipRemove(index)
									}}
									class={tagDeleteClasses}
								>
									{#if tagDelete}
										{@render tagDelete()}
									{:else}
										<strong>×</strong>
									{/if}
								</button>
							{/if}
						</div>
					</div>
				{/each}
			</div>
		</div>
	{/if}
</div>
