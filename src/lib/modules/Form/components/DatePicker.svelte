<script lang="ts" generics="DTO extends DocumentNode">
	import { DatePicker } from '@svelte-plugins/datepicker'

	import type { DocumentNode } from '$lib/utils'
	import type { FieldType, DateProperties, DateRangeProperties, Field } from '../form.interface'

	type F = Omit<Field<DTO>, 'isLoading' | 'hide' | 'resetOn' | 'dependent'> & {
		type: FieldType.DATEPICKER
		props: DateProperties<DTO> | DateRangeProperties<DTO>
	}

	type DateRange =
		| {
				[index: string]: Date
		  }
		| undefined

	let {
		field,
		value = $bindable(),
		onValueChange,
		disabled = false,
	}: {
		field: F
		value: DateRange | Date
		onValueChange?: (value: unknown) => void
		disabled?: boolean
	} = $props()

	function isRange(props: DateProperties<DTO> | DateRangeProperties<DTO>): props is DateRangeProperties<DTO> {
		return 'isRange' in props && props.isRange === true
	}

	let startDate = $state<Date | undefined>(
		isRange(field.props) ? (value as DateRange)?.[field.props.start] || undefined : (value as Date) || undefined,
	)
	let endDate = $state<Date | undefined>(
		isRange(field.props) ? (field.props.end ? (value as DateRange)?.[field.props.end] : undefined) : undefined,
	)
	let isOpen = $state(false)

	const toggleDatePicker = () => (isOpen = !isOpen)
	const openDatePicker = () => (isOpen = true)

	const formatter = new Intl.DateTimeFormat(undefined, {
		dateStyle: 'medium',
		timeStyle: field.props.showTimePicker ? 'short' : undefined,
	})

	const formatDate = (dateString: string | number | Date | undefined) => {
		return (dateString && formatter.format(new Date(dateString))) || ''
	}

	const onChange = () => {
		if (isRange(field.props)) {
			value = Object.assign(value || {}, {
				...(field.props.start ? { [field.props.start]: startDate ? new Date(startDate) : undefined } : {}),
				...(field.props.end ? { [field.props.end]: endDate ? new Date(endDate) : undefined } : {}),
			})
		} else {
			value = startDate ? new Date(startDate) : undefined
		}

		onValueChange?.(value)
	}

	const onClearDates = () => {
		startDate = undefined
		endDate = undefined

		onChange()
	}

	const formattedStartDate = $derived(formatDate(startDate))
	const formattedEndDate = $derived(formatDate(endDate))
	const formattedRange = $derived(
		formattedStartDate || formattedEndDate ? `${formattedStartDate} - ${formattedEndDate}` : '',
	)
</script>

<DatePicker
	{...field.props}
	bind:isOpen
	bind:startDate
	bind:endDate
	enableFutureDates={field.props.enableFutureDates === false ? false : true}
	theme="custom-datepicker"
	onDateChange={onChange}
	{disabled}
>
	<div class="input-group grid-cols-[auto_1fr_auto]">
		<button
			class="ig-btn preset-tonal"
			onclick={(e) => {
				e.preventDefault()
				toggleDatePicker()
			}}
			aria-label="Open datepicker"
			><iconify-icon icon="radix-icons:calendar" width="16" height="16" inline></iconify-icon></button
		>
		{#if field.props.isRange}
			<input
				type="text"
				class="ig-input"
				placeholder="Pick a date range"
				value={formattedRange}
				onfocus={openDatePicker}
				onkeypress={(e) => e.preventDefault()}
			/>
		{:else}
			<input
				type="text"
				class="ig-input"
				placeholder="Pick a date"
				value={formattedStartDate}
				onfocus={openDatePicker}
				onkeypress={(e) => e.preventDefault()}
			/>
		{/if}

		{#if startDate || endDate}
			<button
				class="ig-btn preset-tonal"
				onclick={(e) => {
					e.preventDefault()
					onClearDates()
				}}
				aria-label="Clear datepicker"><strong>×</strong></button
			>
		{/if}
	</div>
</DatePicker>

<style>
	:global(.datepicker[data-picker-theme='custom-datepicker'] input[type='time']) {
		color: var(--datepicker-calendar-day-color);
	}
</style>
