<script lang="ts">
	import { ProgressRing } from '@skeletonlabs/skeleton-svelte'
	import { useDebounce } from 'runed'
	import { onMount } from 'svelte'

	const uid = 'search' + Math.random().toString(36)

	let {
		class: klass,
		id = uid,
		/**
		 * Specify the input value
		 */
		value = $bindable(undefined),
		/**
		 * Set to `true` to auto focus the input on mount
		 */
		autofocus = false,
		/**
		 * Specify the debounce value in milliseconds (ms)
		 */
		debounce = 0,
		/**
		 * Set to `true` to omit the form `role="search"` attribute
		 */
		removeFormAriaAttributes = false,
		/**
		 * Specify the input reference
		 */
		ref = $bindable(null),
		isLoading = $bindable(false),
		onValueChange,
		...rest
	}: {
		class?: string
		id?: string
		value?: string | undefined
		autofocus?: boolean
		debounce?: number
		removeFormAriaAttributes?: boolean
		ref?: HTMLElement | null
		isLoading?: boolean
		onValueChange?: (value: string) => void
	} = $props()

	let prevValue = value

	onMount(() => {
		if (autofocus) window.requestAnimationFrame(() => ref?.focus())
	})

	$effect(() => {
		if (typeof value === 'undefined') return

		if (value.length > 0 && value !== prevValue) {
			if (debounce > 0) {
				useDebounce(
					() => onValueChange?.(value as string),
					() => debounce,
				)
			} else {
				onValueChange?.(value)
			}
		}

		prevValue = value
	})
</script>

<form
	class="relative"
	data-svelte-search
	role={removeFormAriaAttributes ? null : 'search'}
	aria-labelledby={removeFormAriaAttributes ? null : id}
	onsubmit={(e) => e.preventDefault()}
>
	<input
		bind:this={ref}
		class="input {klass}"
		name="search"
		type="search"
		placeholder="Search"
		autocomplete="off"
		spellcheck="false"
		{...rest}
		{id}
		bind:value
	/>

	{#if isLoading}
		<ProgressRing
			classes="absolute! top-1.5 right-2.5 z-20"
			value={null}
			strokeWidth="100px"
			meterStroke="stroke-secondary-500"
			trackStroke="stroke-secondary-500/30"
			size="size-7"
		/>
	{/if}
</form>
