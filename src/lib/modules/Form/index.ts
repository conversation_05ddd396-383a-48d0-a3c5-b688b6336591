import FormBuilder from './FormBuilder.svelte'
import Form<PERSON>ield from './FormField.svelte'
import FormFieldWrapper from './FormFieldWrapper.svelte'
import { drawer, generic, getModal, type ModalSettings } from '../Modal'
import FormModal from './FormModal.svelte'

import type { DocumentNode } from '$lib/utils'
import type { FormModalConfig } from './form.interface'

export * from './form.interface'

export * from './components'
export { FormBuilder, FormField, FormModal, FormFieldWrapper }

export function openFormDrawer<DTO extends DocumentNode>(
	config: FormModalConfig<DTO> & {
		position?: 'top' | 'bottom' | 'left' | 'right'
	} & Omit<ModalSettings, 'type' | 'onSubmit' | 'onClose' | 'closeClasses' | 'submitClasses' | 'position'>,
) {
	const modal = getModal()

	return modal.open({
		type: {
			component: FormModal,
			props: {
				fields: config.fields,
				data: config.data,

				onValueChange: config.onValueChange,
				onSubmit: config.onSubmit,
				onClose: config.onClose,

				reset: config.reset,
				onReset: config.onReset,
			},
		},
		...drawer(config.position ?? 'right'),
		...config,
		protect: true,
		width: config.width ?? 'w-full md:w-[700px]',
		rounded: config.rounded ?? 'rounded-xl',
		margin: config.margin ?? 'p-4',
	})
}

export function openFormModal<DTO extends DocumentNode>(
	config: FormModalConfig<DTO> & {
		position?:
			| 'top'
			| 'bottom'
			| 'left'
			| 'right'
			| 'top-left'
			| 'top-right'
			| 'bottom-left'
			| 'bottom-right'
			| 'center'
	} & Omit<ModalSettings, 'type' | 'onSubmit' | 'onClose' | 'closeClasses' | 'submitClasses' | 'position'>,
) {
	const modal = getModal()
	return modal.open({
		type: {
			component: FormModal,
			props: {
				fields: config.fields,
				data: config.data,

				onValueChange: config.onValueChange,
				onSubmit: config.onSubmit,
				onClose: config.onClose,

				reset: config.reset,
				onReset: config.onReset,
			},
		},
		...generic(config.position ?? 'center'),
		...config,
		protect: true,
	})
}
