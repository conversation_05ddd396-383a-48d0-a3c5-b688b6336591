import { tick } from 'svelte'
import { SvelteMap, SvelteSet } from 'svelte/reactivity'

import { browser } from '$app/environment'

import type { ModalComponentConfig, ModalPresetProps, ModalSettings, ModalInstance } from './types'

class Modal {
	registry = new SvelteMap<string, ModalPresetProps & { type: ModalComponentConfig }>()
	state = new SvelteSet<ModalInstance>()

	constructor(components: Record<string, ModalPresetProps & ModalComponentConfig> = {}) {
		this.register(components)

		// if (browser) {
		// 	$effect(() => {
		// 		if (!this.state.open && page.url.searchParams.get('drawer')) {
		// 			const newUrl = new URL(page.url)
		// 			newUrl.searchParams.delete('drawer')
		// 			goto(newUrl)
		// 		} else if (this.state.open && !page.url.searchParams.get('drawer') && this.state.id) {
		// 			if (components[this.state.id] === null) {
		// 				return
		// 			}

		// 			const newUrl = new URL(page.url)
		// 			newUrl.searchParams.set('drawer', this.state.id)
		// 			goto(newUrl)
		// 		}
		// 	})
		// }
	}

	open = <T extends ModalSettings>(modal: T | string) => {
		const config = typeof modal === 'string' ? ({ type: modal } as T) : modal

		if (
			typeof config.type === 'string' &&
			!['alert', 'confirm', 'prompt'].includes(config.type) &&
			!this.registry.has(config.type)
		) {
			throw new Error(`Modal ${config.type} is not registered.`)
		}

		const modalFromRegistry =
			typeof config.type === 'string' && !['alert', 'confirm', 'prompt'].includes(config.type)
				? this.registry.get(config.type)
				: undefined

		const modalInstance: ModalInstance = {
			...modalFromRegistry,
			...config,
			type: modalFromRegistry?.type || config.type,
			isProtected: config.protect ?? false,
			close: (type?: 'backdrop' | 'escape') => {
				if (modalInstance.isProtected && (type === 'backdrop' || type === 'escape')) {
					return
				}

				this.state.delete(modalInstance)
				modalInstance.close = undefined
			},
			protect: (protect: boolean) => {
				modalInstance.isProtected = protect
			},
		}

		this.state.add(modalInstance)

		return modalInstance
	}

	close = (type?: 'backdrop' | 'escape') => {
		if (!this.state.size) return

		this.state.forEach((modal) => modal.close?.(type))
	}

	register = async (components: Record<string, ModalPresetProps & ModalComponentConfig>) => {
		for (const id in components) {
			const component = {
				...components[id],
				type: { component: components[id].component, props: components[id].props },
			}

			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			delete (component as any).component
			delete component.props

			this.registry.set(id, component)
		}

		await tick()

		// if (page.url.searchParams.has('modal')) {
		// 	const conf = this.components.get(page.url.searchParams.get('modal') as string)
		// 	if (conf) {
		// 		this.open(conf)
		// 	}
		// }

		// if (page.url.searchParams.has('drawer')) {
		// 	const conf = this.components.get(page.url.searchParams.get('drawer') as string)
		// 	if (conf) {
		// 		this.open(conf)
		// 	}
		// }
	}
}

let instance: Modal | undefined = undefined
export function getModal() {
	if (!instance) {
		throw new Error('Modal is not initialized.')
	}

	return instance
}
export function initModal(components: Record<string, ModalPresetProps & ModalComponentConfig> = {}) {
	if (browser && instance && process.env.NODE_ENV !== 'test') {
		console.warn('Modal is already initialized.')
		return instance
	}

	return (instance = new Modal(components))
}
