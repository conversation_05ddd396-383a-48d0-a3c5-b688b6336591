import { fly } from 'svelte/transition'

import type { ModalPresetProps } from './types'

const positionResolver = {
	top: 'items-start justify-center',
	'top-left': 'items-start justify-start',
	'top-right': 'items-start justify-end',
	bottom: 'items-end justify-center',
	'bottom-left': 'items-end justify-start',
	'bottom-right': 'items-end justify-end',
	left: 'items-center justify-start',
	right: 'items-center justify-end',
	center: 'items-center justify-center',
}

const widthResolver = {
	top: {
		full: 'w-full',
		default: 'w-fit',
	},
	'top-left': {
		full: 'w-fit',
		default: 'w-fit',
	},
	'top-right': {
		full: 'w-fit',
		default: 'w-fit',
	},
	bottom: {
		full: 'w-full',
		default: 'w-fit',
	},
	'bottom-left': {
		full: 'w-fit',
		default: 'w-fit',
	},
	'bottom-right': {
		full: 'w-fit',
		default: 'w-fit',
	},
	left: {
		full: 'w-fit',
		default: 'w-fit',
	},
	right: {
		full: 'w-fit',
		default: 'w-fit',
	},
	center: {
		full: 'w-fit',
		default: 'w-fit',
	},
}

const heightResolver = {
	top: {
		full: 'h-fit',
		default: 'h-fit',
	},
	'top-left': {
		full: 'h-fit',
		default: 'h-fit',
	},
	'top-right': {
		full: 'h-fit',
		default: 'h-fit',
	},
	bottom: {
		full: 'h-fit',
		default: 'h-fit',
	},
	'bottom-left': {
		full: 'h-fit',
		default: 'h-fit',
	},
	'bottom-right': {
		full: 'h-fit',
		default: 'h-fit',
	},
	left: {
		full: 'h-full',
		default: 'h-fit',
	},
	right: {
		full: 'h-full',
		default: 'h-fit',
	},
	center: {
		full: 'h-fit',
		default: 'h-fit',
	},
}

const roundedResolver = {
	top: 'rounded-bl-container rounded-br-container',
	'top-left': 'rounded-br-container',
	'top-right': 'rounded-bl-container',
	bottom: 'rounded-tl-container rounded-tr-container',
	'bottom-left': 'rounded-tr-container',
	'bottom-right': 'rounded-tl-container',
	left: 'rounded-tr-container rounded-br-container',
	right: 'rounded-tl-container rounded-bl-container',
	center: 'rounded-container',
}

export function generic(
	position:
		| 'top'
		| 'bottom'
		| 'left'
		| 'right'
		| 'top-left'
		| 'top-right'
		| 'bottom-left'
		| 'bottom-right'
		| 'center' = 'center',
	full = false,
) {
	return {
		position: positionResolver[position],
		width: widthResolver[position][full ? 'full' : 'default'],
		height: heightResolver[position][full ? 'full' : 'default'],
		rounded: roundedResolver[position],
		transitionIn: fly,
		transitionInParams: { duration: 100, opacity: 0, x: 0, y: 100 },
		transitionOut: fly,
		transitionOutParams: { duration: 100, opacity: 0, x: 0, y: 100 },
	} satisfies ModalPresetProps
}

const drawerTransitions = {
	top: {
		transitionIn: fly,
		transitionInParams: { duration: 100, opacity: 0, x: 0, y: -500 },
		transitionOut: fly,
		transitionOutParams: { duration: 100, opacity: 0, x: 0, y: -500 },
	},
	bottom: {
		transitionIn: fly,
		transitionInParams: { duration: 100, opacity: 0, x: 0, y: 500 },
		transitionOut: fly,
		transitionOutParams: { duration: 100, opacity: 0, x: 0, y: 500 },
	},
	left: {
		transitionIn: fly,
		transitionInParams: { duration: 100, opacity: 0, x: -720, y: 0 },
		transitionOut: fly,
		transitionOutParams: { duration: 100, opacity: 0, x: -720, y: 0 },
	},
	right: {
		transitionIn: fly,
		transitionInParams: { duration: 100, opacity: 0, x: 720, y: 0 },
		transitionOut: fly,
		transitionOutParams: { duration: 100, opacity: 0, x: 720, y: 0 },
	},
}

export function drawer(position: 'top' | 'bottom' | 'left' | 'right', full = true) {
	return {
		position: positionResolver[position],
		width: widthResolver[position][full ? 'full' : 'default'],
		height: heightResolver[position][full ? 'full' : 'default'],
		rounded: roundedResolver[position],
		...drawerTransitions[position],
	} satisfies ModalPresetProps
}
