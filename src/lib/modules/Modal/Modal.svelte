<script lang="ts" generics="TransitionBack extends Transition = FadeTransition">
	import { MediaQuery } from 'svelte/reactivity'
	import { fade } from 'svelte/transition'

	import { focusTrap } from '$lib/actions/focusTrap.svelte'

	import ModalWrapper from './ModalWrapper.svelte'
	import { generic } from './presets'
	import { initModal } from './state.svelte.js'
	import { dynamicTransition, type Transition, type TransitionParams } from './transitions'

	import type {
		FadeTransition,
		ModalBaseProps,
		ModalComponentConfig,
		ModalComponentProps,
		ModalPresetProps,
	} from './types'

	const prefersReducedMotion = new MediaQuery('prefers-reduced-motion: reduce')

	const generics = generic('center')

	let {
		// Props (components)
		/** Register a list of reusable component modals. */
		components = {},

		// Props (backdrop)
		/** Set the modal position within the backdrop container */
		position = generics.position,
		/** Provide classes to style the modal margin. */
		margin = '',

		// Props (modal)
		/** Provide classes to style the modal background. */
		background = 'bg-surface-50 dark:bg-surface-950',
		/** Provide classes to style the modal width. */
		width = generics.width,
		/** Provide classes to style the modal height. */
		height = generics.height,
		/** Provide classes to style the modal padding. */
		padding = 4,
		/** Provide classes to style the modal spacing. */
		spacing = 'space-y-4',
		/** Provide classes to style the modal border radius. */
		rounded = generics.rounded,
		/** Provide classes to style modal box shadow. */
		shadow = 'shadow-xl',
		/** Provide a class to override the z-index */
		zIndex = 'z-[999]',

		/** Provide arbitrary classes to the modal window. */
		modalClasses = '',

		// Props (buttons)
		/** Provide classes for neutral buttons, such as Cancel. */
		closeClasses = 'preset-tonal',
		/** Provide classes for positive actions, such as Confirm or Submit. */
		submitClasses = 'preset-filled',
		/** Override the text for the Cancel button. */
		closeLabel = 'Cancel',
		/** Override the text for the Submit button. */
		submitLabel = 'Submit',

		// Props (regions)
		/** Provide arbitrary classes to the backdrop region. */
		backdropClasses = 'bg-surface-200/75 dark:bg-surface-800/75 backdrop-blur-xs',
		/** Provide arbitrary classes to the header region. */
		headerClasses = 'sticky top-0',
		/** Provide arbitrary classes to modal header region. */
		titleClasses = '',
		/** Provide arbitrary classes to modal header region. */
		titleSize = 'h4' as const,
		/** Provide arbitrary classes to modal body region. */
		bodyClasses = '',
		/** Provide arbitrary classes to modal footer region. */
		footerClasses = 'flex justify-end gap-4',

		// Props (transition)
		/**
		 * Enable/Disable transitions
		 * @type {boolean}
		 */
		transitions = !prefersReducedMotion.current,
		/**
		 * Provide the transition used on entry.
		 * @type {ModalTransitionIn}
		 */
		transitionIn = generics.transitionIn,
		/**
		 * Transition params provided to `TransitionIn`.
		 * @type {TransitionParams}
		 */
		transitionInParams = generics.transitionInParams,
		/**
		 * Provide the transition used on exit.
		 * @type {TransitionOut}
		 */
		transitionOut = generics.transitionOut,
		/**
		 * Transition params provided to `TransitionOut`.
		 * @type {TransitionParams}
		 */
		transitionOutParams = generics.transitionOutParams,
		/** Provide the transition used on backdrop
		 * @type {TransitionBack}
		 */
		transitionBack = fade as TransitionBack,
		/**
		 * Transition params provided to `TransitionBack`.
		 * @type {TransitionParams}
		 */
		transitionBackParams = { duration: 100 },

		// Callbacks
		backdrop,

		class: klass,
	}: {
		components?: Record<string, ModalComponentConfig>
		backdropClasses?: string
		transitionBack?: TransitionBack
		transitionBackParams?: TransitionParams<TransitionBack>

		backdrop?: (event: MouseEvent) => void

		class?: string
	} & ModalBaseProps &
		ModalComponentProps &
		ModalPresetProps = $props()

	const modal = initModal(components)

	// Base Styles
	const cBackdrop = 'fixed top-0 left-0 right-0 bottom-0'

	// Local
	const defaults = $state<
		Required<ModalPresetProps> & {
			background: string
			padding: number

			closeLabel: string | false
			closeClasses: string
			submitLabel: string | false
			submitClasses: string
			titleSize: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
			titleClasses: string
			headerClasses: string
			bodyClasses: string
			footerClasses: string
		}
	>({
		protect: false,
		background,
		position,
		margin,

		modalClasses,

		width,
		height,
		padding,
		spacing,
		rounded,
		shadow,

		transitions,
		transitionIn,
		transitionInParams,
		transitionOut,
		transitionOutParams,

		closeLabel,
		closeClasses,
		submitLabel,
		submitClasses,
		titleSize,
		titleClasses,
		headerClasses,
		bodyClasses,
		footerClasses,
	})

	let registeredInteractionWithBackdrop = $state(false)
	// let modalElement = $state<HTMLDivElement>()
	// let windowHeight = $state<number>(0)
	// let backdropOverflow = $state('overflow-y-hidden')

	// // Modal Store Subscription
	// $effect(() => {
	// 	if (modalElement) {
	// 		onModalHeightChange(modalElement)
	// 	}
	// })

	// function onModalHeightChange(el: HTMLDivElement) {
	// 	let modalHeight = el?.clientHeight
	// 	if (!modalHeight) modalHeight = (el?.firstChild as HTMLElement)?.clientHeight

	// 	// modal is closed
	// 	if (!modalHeight) return

	// 	if (modalHeight > windowHeight) {
	// 		backdropOverflow = 'overflow-y-auto'
	// 	} else {
	// 		backdropOverflow = 'overflow-y-hidden'
	// 	}
	// }

	// Event Handlers ---
	function onBackdropInteractionBegin(event: MouseEvent): void {
		if (!(event.target instanceof Element)) return
		const classList = event.target.classList
		if (classList.contains('modal-backdrop') || classList.contains('modal-transition')) {
			registeredInteractionWithBackdrop = true
		}
	}
	function onBackdropInteractionEnd(event: MouseEvent): void {
		if (!(event.target instanceof Element)) return
		const classList = event.target.classList
		if (
			(classList.contains('modal-backdrop') || classList.contains('modal-transition')) &&
			registeredInteractionWithBackdrop
		) {
			modal.close('backdrop')
			/** @event {{ event }} backdrop - Fires on backdrop interaction.  */
			backdrop?.(event)
		}
		registeredInteractionWithBackdrop = false
	}

	function onKeyDown(event: KeyboardEvent): void {
		if (!modal) return
		if (event.code === 'Escape') modal.close('escape')
	}

	const classesBackdrop = $derived(`${cBackdrop} ${backdropClasses} ${zIndex} ${klass ?? ''}`)
</script>

<!-- <svelte:window bind:innerHeight={windowHeight} on:keydown={onKeyDown} /> -->
<svelte:window on:keydown={onKeyDown} />

{#if modal.state.size}
	{#key modal.state}
		<!-- Backdrop -->
		<!-- TODO: resolve a11y warnings -->
		<!-- svelte-ignore a11y_no_static_element_interactions -->
		<div
			class="modal-backdrop {classesBackdrop} backdropOverflow"
			data-testid="modal-backdrop"
			onmousedown={onBackdropInteractionBegin}
			onmouseup={onBackdropInteractionEnd}
			transition:dynamicTransition={{
				transition: transitionBack,
				params: transitionBackParams,
				enabled: transitions,
			}}
			use:focusTrap={true}
		>
			{#each modal.state as m, index (m)}
				<ModalWrapper {index} modal={m} {defaults} />
			{/each}
		</div>
	{/key}
{/if}
