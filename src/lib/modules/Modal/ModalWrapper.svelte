<script lang="ts">
	import ModalComponent from './ModalComponent.svelte'
	import { dynamicTransition } from './transitions'

	import type { ModalComponentConfig, ModalPresetProps, ModalInstance } from './types'

	const {
		index,
		modal,
		defaults,
	}: {
		index: number
		modal: ModalInstance
		defaults: Required<ModalPresetProps> & {
			background: string
			padding: number

			closeLabel: string | false
			closeClasses: string
			submitLabel: string | false
			submitClasses: string
			titleSize: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
			titleClasses: string
			headerClasses: string
			bodyClasses: string
			footerClasses: string
		}
	} = $props()

	const uid = $props.id()

	// Base Styles
	const cTransitionLayer = 'absolute w-full h-full overflow-y-auto flex'
	const cModal = 'block overflow-y-auto overflow-x-hidden'

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	async function onClose(response?: any) {
		modal?.onClose?.(response)
		modal.close?.()
	}

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	async function onSubmit(response?: any) {
		modal?.onSubmit?.(response)
		modal.close?.()
	}

	function onPromptSubmit(event: SubmitEvent): void {
		event.preventDefault()
		if (modal?.onSubmit) {
			if (modal.valueAttr !== undefined && 'type' in modal.valueAttr && modal.valueAttr.type === 'number')
				modal.onSubmit?.(parseInt(promptValue))
			else modal.onSubmit?.(promptValue)
		}
		modal.close?.()
	}

	// Local
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	let promptValue = $state<any>()

	// Set Prompt input value and type
	if (modal.type === 'prompt') promptValue = modal.value

	// Override button text per instance, if available
	const closeLabel = modal.closeLabel ?? defaults.closeLabel
	const closeClasses = modal.closeClasses ?? defaults.closeClasses
	const submitLabel = modal.submitLabel ?? (modal.type === 'confirm' ? 'Confirm' : defaults.submitLabel)
	const submitClasses = modal.submitClasses ?? defaults.submitClasses
	const titleSize = modal.titleSize ?? defaults.titleSize
	const titleClasses = modal.titleClasses ?? defaults.titleClasses
	const headerClasses = modal.headerClasses ?? defaults.headerClasses
	const bodyClasses = modal.bodyClasses ?? defaults.bodyClasses
	const footerClasses = modal.footerClasses ?? defaults.footerClasses

	// Presets
	const background = modal.background ?? defaults.background
	const padding = modal.padding ?? defaults.padding

	const modalClasses = modal.modalClasses ?? defaults.modalClasses ?? ''
	const position = modal.position ?? defaults.position
	const margin = modal.margin ?? defaults.margin
	const width = modal.width ?? defaults.width
	const height = modal.height ?? defaults.height
	const spacing = modal.spacing ?? defaults.spacing
	const rounded = modal.rounded ?? defaults.rounded
	const shadow = modal.shadow ?? defaults.shadow

	const transitions = modal.transitions ?? defaults.transitions
	const transitionIn = modal.transitionIn ?? defaults.transitionIn
	const transitionInParams = modal.transitionInParams ?? defaults.transitionInParams
	const transitionOut = modal.transitionOut ?? defaults.transitionOut
	const transitionOutParams = modal.transitionOutParams ?? defaults.transitionOutParams

	const classesTransitionLayer = `${cTransitionLayer} ${position} ${margin} z-[${index}]`
	const classesModal = `${cModal} ${background} ${width} ${height} ${spacing} ${rounded} ${shadow} ${modalClasses}`

	let modalElement = $state<HTMLDivElement>()

	// Modal Store Subscription
	$effect(() => {
		if (modalElement) {
			onModalHeightChange(modalElement)
		}
	})

	function onModalHeightChange(el: HTMLDivElement) {
		let modalHeight = el?.clientHeight
		if (!modalHeight) modalHeight = (el?.firstChild as HTMLElement)?.clientHeight

		// modal is closed
		if (!modalHeight) return
	}

	function hasCustomComponent(modal: ModalInstance): modal is ModalInstance & {
		type: ModalComponentConfig
	} {
		return typeof modal.type === 'object'
	}

	// IMPORTANT: add values to pass to the children templates.
	const componentProps: ModalComponentConfig = {
		...(modal.type as ModalComponentConfig)?.props,
		...modal,
		...defaults,
		// ---
		closeLabel,
		closeClasses,
		submitLabel,
		submitClasses,
		titleSize,
		titleClasses,
		headerClasses,
		bodyClasses,
		footerClasses,
		// ---
		onSubmit,
		onClose,
		protect: modal.protect,
		// ---
		background,
		padding,
	}
</script>

<!-- Transition Layer -->
<div
	class="modal-transition {classesTransitionLayer}"
	in:dynamicTransition|global={{ transition: transitionIn, params: transitionInParams, enabled: transitions }}
	out:dynamicTransition|global={{ transition: transitionOut, params: transitionOutParams, enabled: transitions }}
>
	<div
		class="modal {classesModal}"
		bind:this={modalElement}
		data-testid="modal"
		role="dialog"
		aria-modal="true"
		aria-label={modal.title ?? ''}
	>
		{#if hasCustomComponent(modal)}
			<modal.type.component {...componentProps} />
		{:else}
			<ModalComponent {...componentProps}>
				{#if modal.type === 'prompt'}
					<!-- Template: Prompt -->
					<form class="space-y-4" id="prompt-form-{uid}" onsubmit={onPromptSubmit}>
						<input
							class="modal-prompt-input input"
							name="prompt"
							type="text"
							bind:value={promptValue}
							{...modal.valueAttr}
						/>
					</form>
				{/if}

				{#snippet footer()}
					{#if modal!.type === 'alert'}
						<!-- Template: Alert -->
						<button type="button" class="btn {closeClasses}" onclick={onClose}>{closeLabel}</button>
					{:else if modal!.type === 'confirm'}
						<!-- Template: Confirm -->
						<button type="button" class="btn {closeClasses}" onclick={onClose}>{closeLabel}</button>
						<button type="button" class="btn {submitClasses}" onclick={onSubmit}>{submitLabel}</button>
					{:else if modal!.type === 'prompt'}
						<!-- Template: Prompt -->
						<button type="button" class="btn {closeClasses}" onclick={onClose}>{closeLabel}</button>
						<button type="submit" form="prompt-form-{uid}" class="btn {submitClasses}">{submitLabel}</button>
					{/if}
				{/snippet}
			</ModalComponent>
		{/if}
	</div>
</div>
