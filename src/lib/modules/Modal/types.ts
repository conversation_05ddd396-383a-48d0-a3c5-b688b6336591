import type { Transition, TransitionParams } from './transitions'
import type { Component, ComponentProps } from 'svelte'
import type { fly, fade } from 'svelte/transition'

export type FlyTransition = typeof fly
export type FadeTransition = typeof fade

export interface ModalBaseProps {
	/** Provide arbitrary classes to the backdrop. */
	backdropClasses?: string
	/** Provide a class to override the z-index */
	zIndex?: string
}

export interface ModalComponentProps {
	/** Provide classes to style the modal background. */
	background?: string
	/** Provide value to style the modal padding. */
	padding?: number

	/** Provide the modal title content.. */
	title?: string
	/** Provide a URL to display an image within the modal. */
	image?: string

	/** Provide the modal body content. Accepts HTML. */
	body?: string

	/** Override the Cancel button label. */
	closeLabel?: string | false
	/** Override the Submit button label. */
	submitLabel?: string | false

	/** Provide arbitrary classes to the header region. */
	headerClasses?: string
	/** Provide arbitrary classes to the title region. */
	titleClasses?: string
	/** Provide the title element size. */
	titleSize?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
	/** Provide arbitrary classes to the image region. */
	imageClasses?: string

	/** Provide arbitrary classes to the body region. */
	bodyClasses?: string

	/** Provide arbitrary classes to the footer region. */
	footerClasses?: string
	/** Provide classes for neutral buttons, such as Cancel. */
	closeClasses?: string
	/** Provide classes for positive actions, such as Confirm or Submit. */
	submitClasses?: string

	/** Provide a function. Returns the onSubmit value. */
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	onSubmit?: (response?: any) => Promise<void> | void
	/** Provide a function. Returns the onClose value. */
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	onClose?: (response?: any) => Promise<void> | void

	/** Pass arbitrary data per modal instance. */
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	meta?: any

	/** Provide input attributes as key/value pairs. */
	valueAttr?: object
}

export interface ModalPresetProps<
	TransitionIn extends Transition = FlyTransition,
	TransitionOut extends Transition = FlyTransition,
> {
	/** Protect the modal from being closed by backdrop or escape. */
	protect?: boolean

	/** Provide arbitrary classes to the modal window. */
	modalClasses?: string

	/** Set the modal position within the backdrop container */
	position?: string
	/** Provide classes to style the modal margin. */
	margin?: string
	/** Provide classes to style the modal width. */
	width?: string
	/** Provide classes to style the modal height. */
	height?: string
	/** Provide classes to style the modal spacing. */
	spacing?: string
	/** Provide classes to style the modal border radius. */
	rounded?: string
	/** Provide classes to style modal box shadow. */
	shadow?: string

	transitions?: boolean
	transitionIn?: TransitionIn
	transitionInParams?: TransitionParams<TransitionIn>
	transitionOut?: TransitionOut
	transitionOutParams?: TransitionParams<TransitionOut>
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export interface ModalComponentConfig<C extends Component<any> = Component<any>> {
	component: C
	props?: ComponentProps<C>
}

export interface ModalWithComponent {
	type: ModalComponentConfig | string
	value?: undefined
}

export interface ModalWithPrompt {
	type: 'prompt'
	/** By default, used to provide a prompt value. */
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	value?: any
}

export interface ModalWithPreset {
	type: 'alert' | 'confirm'
	value?: undefined
}

export type ModalSettings = ModalComponentProps &
	ModalPresetProps &
	(ModalWithPreset | ModalWithPrompt | ModalWithComponent)

export type ModalInstance = Omit<ModalSettings, 'protect'> & {
	close?: (type?: 'backdrop' | 'escape') => void

	isProtected: boolean
	protect: (protect: boolean) => void
}
