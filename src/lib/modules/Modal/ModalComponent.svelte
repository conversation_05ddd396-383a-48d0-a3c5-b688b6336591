<script lang="ts">
	import { AppBar } from '@skeletonlabs/skeleton-svelte'

	import type { ModalComponentProps } from './types'
	import type { Snippet } from 'svelte'

	const {
		background,
		padding,

		headerClasses,

		body,
		bodyClasses,
		footerClasses,

		image,
		imageClasses,

		title,
		titleClasses,
		titleSize,

		closeLabel,
		closeClasses,

		submitLabel,
		submitClasses,

		onSubmit,
		onClose,

		header,
		headerClose,
		article,
		children,
		footer,
	}: {
		header?: Snippet
		headerClose?: Snippet
		article?: Snippet
		children?: Snippet
		footer?: Snippet
	} & ModalComponentProps = $props()
</script>

{#if header}
	<header class="modal-header {headerClasses} {padding ? `pt-${padding} px-${padding}` : ''}">
		{@render header()}
	</header>

	<hr class="hr" />
{:else if title}
	<AppBar classes="modal-header {headerClasses}" {background} padding={padding ? `pt-${padding} px-${padding}` : ''}>
		{#snippet lead()}
			<svelte:element this={titleSize} class="modal-header {titleSize} {titleClasses}">{title}</svelte:element>
		{/snippet}

		{#snippet trail()}
			{#if onClose}
				<button class="btn-icon hover:preset-tonal-primary cursor-pointer" onclick={onClose} aria-label="Close">
					{#if headerClose}
						{@render headerClose()}
					{:else}
						<iconify-icon icon="radix-icons:cross-1" width="20" height="20"></iconify-icon>
					{/if}
				</button>
			{/if}
		{/snippet}
	</AppBar>

	<hr class="hr" />
{/if}

{#if article}
	<article class="modal-body {bodyClasses} {padding ? `px-${padding}` : ''}">
		{@render article()}
	</article>
{:else if body}
	<article class="modal-body {bodyClasses} {padding ? `px-${padding}` : ''}">
		{@html body}
	</article>
{/if}

{#if image && typeof image === 'string'}
	<img class="modal-image {imageClasses ?? 'h-auto w-full'}" src={image} alt="Modal" />
{/if}

{#if children}
	<article class="modal-body {bodyClasses} {padding ? `px-${padding}` : ''}">
		{@render children()}
	</article>
{/if}

{#if footer}
	<hr class="hr" />

	<footer class="modal-footer {footerClasses} {padding ? `pb-${padding} px-${padding}` : ''}">
		{@render footer()}
	</footer>
{:else if (onSubmit && submitLabel) || (onClose && closeLabel)}
	<hr class="hr" />

	<footer class="modal-footer {footerClasses} {padding ? `pb-${padding} px-${padding}` : ''}">
		<button type="button" class="btn {closeClasses}" onclick={onClose}>{closeLabel}</button>

		{#if onSubmit && submitLabel}
			<button type="button" class="btn {submitClasses}" onclick={onSubmit}>{submitLabel}</button>
		{/if}
	</footer>
{/if}

<style>
	.btn-icon iconify-icon {
		display: inline-block;
		min-width: 1rem;
		min-height: 1rem;
	}
</style>
