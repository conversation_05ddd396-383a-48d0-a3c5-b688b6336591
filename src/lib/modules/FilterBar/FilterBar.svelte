<script lang="ts">
	import Search from '$lib/modules/Form/components/Search.svelte'

	import type { Snippet } from 'svelte'

	let {
		value = $bindable(),
		isLoading = $bindable(false),
		actions,
		extra,
		padding = '',
		onValueChange,
	}: {
		padding?: string
		value?: string
		isLoading?: boolean
		actions?: Snippet
		extra?: Snippet
		onValueChange?: (value: string) => void
	} = $props()
</script>

<div class={`flex flex-row justify-between ${padding}`}>
	<div class="w-2/5">
		<Search bind:value bind:isLoading debounce={250} {onValueChange} />
	</div>

	{#if actions}
		<div class="flex flex-row gap-4">
			{@render actions?.()}
		</div>
	{/if}
</div>

{#if extra}
	<div class={`flex flex-row gap-4 ${padding} pt-0`}>
		{@render extra?.()}
	</div>
{/if}
