<script lang="ts" module>
	import {
		type NestedFields,
		type DocumentNode,
		type PropertyType,
		type StrictPropertyType,
		getLocaleForDateFormat,
	} from '$lib/utils'

	import {
		ColumnType,
		type BaseDataTableColumn,
		type DataTableConfig,
		type DataTableData,
		type DataTableColumn,
		type DataTableColumnWithProperty,
		type DataTableCustomColumn,
		type DataTableFilter,
		type DataTableAction,
		type Chip,
		isBoolean,
		isChips,
		isCurrency,
		isCustom,
		isDataChips,
		isDate,
		isFormField,
		isImage,
		isInterval,
		isProfile,
		isAction,
	} from './data-table.interface'

	function onImgError(e: Event) {
		const target = e.target as HTMLImageElement
		target.src = '/icons/<EMAIL>'
	}

	const units: Array<{ unit: Intl.RelativeTimeFormatUnit; ms: number }> = [
		{ unit: 'year', ms: 31536000000 },
		{ unit: 'month', ms: 2628000000 },
		{ unit: 'day', ms: 86400000 },
		{ unit: 'hour', ms: 3600000 },
		{ unit: 'minute', ms: 60000 },
		{ unit: 'second', ms: 1000 },
	]

	const chipColors = {
		primary: 'preset-filled-primary-500',
		secondary: 'preset-filled-secondary-500',
		tertiary: 'preset-filled-tertiary-500',
		success: 'preset-filled-success-500',
		warning: 'preset-filled-warning-500',
		error: 'preset-filled-error-500',
		surface: 'preset-filled-surface-500',
	}

	function getChipColor(color?: keyof typeof chipColors | string) {
		return color ? (chipColors[color as keyof typeof chipColors] ?? color) : undefined
	}

	function calculateRelativeDate(date: Date, options: Intl.RelativeTimeFormatOptions = { numeric: 'auto' }) {
		const rtf = new Intl.RelativeTimeFormat('en', options)
		const elapsed = new Date(date).getTime() - new Date().getTime()

		for (const { unit, ms } of units) {
			if (Math.abs(elapsed) >= ms || unit === 'second') {
				return rtf.format(Math.round(elapsed / ms), unit)
			}
		}
		return ''
	}
</script>

<script lang="ts" generics="DTO extends DocumentNode">
	import { Pagination, Popover } from '@skeletonlabs/skeleton-svelte'
	import { twMerge } from 'tailwind-merge'

	import { getTheme } from '$lib/services/theme.svelte'

	import FilterBar from '../FilterBar'
	import { FormField, type Field } from '../Form'
	import Table from '../Table'

	import type { Snippet } from 'svelte'

	const uid = 'datatable' + Math.random().toString(36)

	const theme = getTheme()

	let {
		id = uid,
		config,
		data,
		filter = $bindable({
			search: '',
			page: config.pagination?.initialPage ?? 0,
			limit: config.pagination?.pageSize ?? 25,
		}),
		filterActions,
		filterExtra,
		isLoading = $bindable(false),
	}: {
		id?: string
		config: DataTableConfig<DTO>
		data: DataTableData<DTO>
		filter?: DataTableFilter
		filterActions?: Snippet
		filterExtra?: Snippet
		isLoading?: boolean
	} = $props()

	const pagination = $derived(
		Object.assign(
			{},
			{
				initialPage: 0,
				pageSize: 25,
				totalItems: data?.total || 0,
				amounts: [5, 15, 25, 50],
			},
			config.pagination,
		),
	)

	let sortKey: string | undefined = $state()
	let sortDirection = $state(true)

	type ConstructorType<DTO> = DTO extends NumberConstructor
		? number
		: DTO extends StringConstructor
			? string
			: DTO extends BooleanConstructor
				? boolean
				: DTO extends DateConstructor
					? Date
					: never

	// Function definition with improved type inference
	function rowValue<C extends NumberConstructor | DateConstructor | StringConstructor | BooleanConstructor>(
		obj: DTO,
		key: NestedFields<DTO> | string,
		constructor?: C,
	): C extends undefined ? StrictPropertyType<DTO> : ConstructorType<C> {
		const result = key.split('.').reduce((acc, curr) => acc?.[curr], obj) as PropertyType<DTO, typeof key>

		if (result && constructor) {
			return new constructor(result) as C extends undefined ? StrictPropertyType<DTO> : ConstructorType<C>
		}

		return result as C extends undefined ? StrictPropertyType<DTO> : ConstructorType<C>
	}

	function resolveHref(row: DTO, column: DataTableColumn<DTO>) {
		const key = (column as BaseDataTableColumn<DTO>).href

		if (typeof key === 'function') {
			const href = key(row)
			if (typeof href === 'string') {
				return {
					href,
					target: (column as BaseDataTableColumn<DTO>).target,
				}
			}
		} else {
			const href = key !== undefined ? rowValue(row, key) : undefined
			if (typeof href === 'string' || href === undefined) {
				return {
					href,
					target: (column as BaseDataTableColumn<DTO>).target,
				}
			}
		}
	}

	function hasProperty(column: DataTableColumn<DTO>): column is DataTableColumnWithProperty<DTO> & {
		type: never
	} {
		return typeof (column as DataTableColumnWithProperty<DTO>).property !== 'undefined'
	}

	function fitCell(column: DataTableColumn<DTO>): boolean {
		return column.type === ColumnType.Image || column.type === ColumnType.Boolean
	}

	function onActionClick(action: DataTableAction<DTO>, row: DTO[]) {
		if (isAction(action)) {
			action.action(row)
		}
	}

	function resolveHideExpression(
		filter: DataTableFilter,
		hide: string | ((filter: DataTableFilter, row?: DTO) => boolean) | boolean,
		row?: DTO,
	) {
		if (!filter) return true

		switch (typeof hide) {
			case 'undefined':
				return false
			case 'boolean':
				return hide
			case 'string':
				if (row) {
					const expression = Function('filter', 'item', `return ${hide};`)
					return expression(filter, row)
				}
				// eslint-disable-next-line no-case-declarations
				const expression = Function('filter', `return ${hide};`)
				return expression(filter)
			case 'function':
				if (row) {
					return hide(filter, row)
				}
				return hide(filter, row)
			default:
				return false
		}
	}

	function onSortChange(column: DataTableColumn<DTO> | DataTableCustomColumn) {
		if (column.sortable === undefined) return

		if (sortKey === column.sortable) {
			sortDirection = !sortDirection
		} else {
			sortKey = column.sortable
			sortDirection = true
		}

		filter.sort = { key: sortKey, direction: sortDirection ? 'asc' : 'desc' }
	}

	function sortingClass(
		column: DataTableColumn<DTO> | DataTableCustomColumn,
		key: string | undefined,
		direction: boolean,
	) {
		if (column.sortable === undefined) return ''

		if (key === column.sortable) {
			return direction ? 'table-sort-asc' : 'table-sort-dsc'
		}

		return ''
	}

	function onclick(row: DTO, column: DataTableColumn<DTO>, item?: StrictPropertyType<DTO> | Chip<DTO>) {
		if (isChips(column) && column.onclick !== undefined && item) {
			return () => column.onclick!(row, item as Chip<DTO>)
		} else if (isDataChips(column) && column.onclick !== undefined && item) {
			return () => column.onclick!(row, item as StrictPropertyType<DTO>)
		} else if (hasProperty(column) && column.onclick !== undefined) {
			return () => column.onclick!(row, column.property)
		} else if ((column as BaseDataTableColumn<DTO>).onclick !== undefined) {
			return () => (column as BaseDataTableColumn<DTO>).onclick!(row)
		}
		return () => {
			// do nothing
		}
	}

	function getChipsFromData(row: DTO, property: NestedFields<DTO>) {
		const value = rowValue(row, property)

		if (Array.isArray(value)) {
			return value
		}

		return []
	}

	function _resolveChipColor(row: DTO, column: DataTableColumn<DTO>, item?: StrictPropertyType<DTO> | Chip<DTO>) {
		if (!isChips(column) && !isDataChips(column)) {
			return
		}
		if (!column.color) {
			return
		}

		if (typeof column.color === 'function') {
			return column.color(row, item as never)
		}

		if ((item as Chip<DTO>)?.property) {
			const value = rowValue(row, (item as Chip<DTO>).property)
			const res =
				typeof value === 'undefined'
					? 'undefined'
					: value === null
						? 'undefined'
						: typeof value === 'boolean'
							? value.toString()
							: value

			if (typeof res === 'string' || typeof res === 'number' || typeof res === 'symbol') {
				return column.color[res] ?? column.color.undefined
			}

			return column.color.undefined
		}

		return column.color['default'] ?? column.color[Object.keys(column.color)[0]] ?? column.color.undefined
	}

	function resolveChipColor(row: DTO, column: DataTableColumn<DTO>, item?: StrictPropertyType<DTO> | Chip<DTO>) {
		return getChipColor(_resolveChipColor(row, column, item))
	}

	let selected: DTO[] = $state.raw([])

	function selectAll() {
		if (selected.length === data?.rows.length) {
			selected = []
		} else {
			selected = data.rows
		}

		if (config.onSelection) {
			config.onSelection(selected)
		}
	}

	const hideExpressions = $derived(
		(
			filter: DataTableFilter,
			hide: string | ((filter: DataTableFilter, row?: DTO) => boolean) | boolean,
			row?: DTO,
		) => {
			return resolveHideExpression(filter, hide, row)
		},
	)

	let visibleColumns = $derived(
		config.columns.filter((column) => !hideExpressions(filter, column.hide ?? false)).map((column) => column.label),
	)

	const multipleActions = $derived((config.actions ?? []).filter((action) => isAction(action) && action.multiple))
</script>

<FilterBar bind:isLoading bind:value={filter.search} padding="p-6">
	{#snippet actions()}
		{@render filterActions?.()}
	{/snippet}
	{#snippet extra()}
		{@render filterExtra?.()}
	{/snippet}
</FilterBar>

<div class="p-6">
	<Table>
		<Table.Head class="relative z-2 text-left font-bold capitalize">
			<Table.Row>
				{#if multipleActions.length || config.onSelection}
					<Table.Row.Header class="table-cell-fit sticky left-0 z-1">
						<input
							type="checkbox"
							class="checkbox ml-2 {selected.length && selected.length !== data?.rows.length ? 'partial' : ''}"
							onclick={selectAll}
							checked={!!data?.rows?.length && selected.length === data?.rows.length}
						/>
					</Table.Row.Header>
				{/if}

				{#each config.columns as column (column.label)}
					{#if visibleColumns.includes(column.label)}
						<Table.Row.Data
							class={`${sortingClass(column, sortKey, sortDirection)} ${column.sortable ? 'cursor-pointer' : ''}`}
							onclick={() => onSortChange(column)}>{column.label}</Table.Row.Data
						>
					{/if}
				{/each}

				<Table.Row.Header class="sticky right-0 z-1">
					<div class="flex justify-end space-x-2">
						{#if multipleActions.length && selected.length}
							{#each multipleActions as action (action)}
								{#if !hideExpressions(filter, action.hide ?? false)}
									<button
										type="button"
										class="{action.icon ? 'btn-icon' : 'btn'} {action.classes}"
										onclick={() => onActionClick(action, selected)}
									>
										{#if action.icon}
											<iconify-icon icon={action.icon} width="24" height="24"></iconify-icon>
										{:else}
											{action.label}
										{/if}
									</button>
								{/if}
							{/each}
						{/if}

						<Popover
							positioning={{ placement: 'bottom-end' }}
							triggerBase="btn-icon"
							contentBase="card bg-noise bg-surface-800 dark:bg-surface-100 text-surface-contrast-800 dark:text-surface-contrast-100 shadow-xl max-w-[320px] overflow-hidden p-2 space-y-2"
							arrowBackground={theme.mode === 'Light' ? 'var(--color-surface-800)' : 'var(--color-surface-100)'}
							arrow
							zIndex="2"
						>
							{#snippet trigger()}
								<iconify-icon icon="radix-icons:gear" width="24" height="24"></iconify-icon>
							{/snippet}

							{#snippet content()}
								{#each config.columns as column (column.label)}
									<label class="flex items-center space-x-2">
										<input
											bind:group={visibleColumns}
											class="checkbox bg-surface-700-300"
											type="checkbox"
											value={column.label}
											name={`${id}-selected-columns`}
											onchange={(e) => (column.hide = e.currentTarget?.checked)}
										/>
										<p>{column.label}</p>
									</label>
								{/each}
							{/snippet}
						</Popover>
					</div>
				</Table.Row.Header>
			</Table.Row>
		</Table.Head>

		{#if data?.rows?.length}
			<Table.Body class="divide-y">
				{#each data.rows as row (row)}
					<Table.Row>
						{#if multipleActions.length || config.onSelection}
							<Table.Row.Header class="table-cell-fit sticky left-0 z-1">
								<input
									bind:group={selected}
									type="checkbox"
									class="checkbox ml-2"
									value={row}
									onchange={() => config.onSelection?.(selected)}
								/>
							</Table.Row.Header>
						{/if}

						{#each config.columns as column, c (column.label)}
							{#if visibleColumns.includes(column.label)}
								<Table.Row.Data
									class={twMerge(column.class, fitCell(column) ? 'table-cell-fit space-y-0.5' : 'space-y-0.5')}
									{...resolveHref(row, column)}
									onclick={isChips(column) || isDataChips(column) ? undefined : onclick(row, column)}
								>
									{#if isImage(column)}
										{#if rowValue(row, column.property)}
											<img src={rowValue(row, column.property, String)} class="max-h-10" alt="" onerror={onImgError} />
										{/if}
									{:else if isDate(column)}
										{#if rowValue(row, column.property)}
											<Popover
												positioning={{ placement: c < config.columns.length - 2 ? 'right' : 'left', flip: true }}
												triggerBase="btn p-0"
												contentBase="card bg-noise bg-surface-800 dark:bg-surface-100 text-surface-contrast-800 dark:text-surface-contrast-100 shadow-xl max-w-[320px] overflow-hidden p-2 space-y-2"
												arrowBackground={theme.mode === 'Light'
													? 'var(--color-surface-800)'
													: 'var(--color-surface-100)'}
												arrow
												zIndex="2"
											>
												{#snippet trigger()}
													{column.relative
														? calculateRelativeDate(
																rowValue(row, column.property, Date) ?? column.fallback,
																column.relativeOptions,
															)
														: (rowValue(row, column.property, Date) ?? column.fallback).toLocaleString(
																getLocaleForDateFormat(),
																column.options,
															)}
												{/snippet}
												{#snippet content()}
													<p>
														{column.relative
															? (rowValue(row, column.property, Date) ?? column.fallback).toLocaleString(
																	getLocaleForDateFormat(),
																	column.options,
																)
															: calculateRelativeDate(
																	rowValue(row, column.property, Date) ?? column.fallback,
																	column.relativeOptions,
																)}
													</p>
												{/snippet}
											</Popover>
										{/if}
									{:else if isInterval(column)}
										<span class="chip preset-outlined-surface-500"
											>{(column.start &&
												rowValue(row, column.start, Date)?.toLocaleString(getLocaleForDateFormat(), column.options)) ??
												(row.createdAt &&
													new Date(row.createdAt).toLocaleString(getLocaleForDateFormat(), column.options)) ??
												'Not Specified'}</span
										>
										to
										<span class="chip preset-outlined-surface-500"
											>{(column.end &&
												rowValue(row, column.end, Date)?.toLocaleString(getLocaleForDateFormat(), column.options)) ??
												'Not Specified'}</span
										>
									{:else if isBoolean(column)}
										{#if rowValue(row, column.property)}
											{#if column.reversed}
												<iconify-icon icon="radix-icons:cross-2" class="text-error-500" width="24" height="24"
												></iconify-icon>
											{:else}
												<iconify-icon icon="radix-icons:check" class="text-success-700" width="24" height="24"
												></iconify-icon>
											{/if}
										{:else if column.reversed}
											<iconify-icon icon="radix-icons:check" class="text-success-700" width="24" height="24"
											></iconify-icon>
										{:else}
											<iconify-icon icon="radix-icons:cross-2" class="text-error-500" width="24" height="24"
											></iconify-icon>
										{/if}
									{:else if isCurrency(column)}
										{rowValue(row, column.property, Number)
											? rowValue(row, column.property, Number).toLocaleString(undefined, {
													style: 'currency',
													currency: column.currency ?? 'EUR',
												})
											: (column.fallback ?? '')}
									{:else if isChips(column)}
										<div class="flex space-x-2">
											{#each column.chips ?? [] as chip (chip.label)}
												<button
													class="chip-active chip {resolveChipColor(row, column, chip) ?? 'preset-filled-surface-500'}"
													onclick={onclick(row, column, chip)}>{chip.label}</button
												>
											{/each}
										</div>
									{:else if isDataChips(column)}
										<div class="flex space-x-2">
											{#each getChipsFromData(row, column.property) ?? [] as chip (chip)}
												<button
													class="chip-active chip {resolveChipColor(row, column, chip) ?? 'preset-filled-surface-500'}"
													onclick={onclick(row, column, chip)}
													>{column.labelProperty ? chip[column.labelProperty] : chip}</button
												>
											{/each}
										</div>
									{:else if isProfile(column)}
										{#if rowValue(row, column.name)}
											<div class="flex items-center space-x-2">
												{#if column.image}
													<img
														src={rowValue(row, column.image, String) ?? '/icons/<EMAIL>'}
														class="mr-2 max-h-10"
														alt=""
														onerror={onImgError}
													/>
												{/if}
												{rowValue(row, column.name)}
											</div>
										{/if}
									{:else if isFormField(column)}
										{#if rowValue(row, column.property)}
											<div class="flex items-center space-x-2">
												<FormField
													field={column.field as Field<DocumentNode>}
													value={rowValue(row, column.property)}
													onValueChange={column.field.onValueChange as never}
												/>
											</div>
										{/if}
									{:else if hasProperty(column)}
										{rowValue(row, column.property) ?? column.fallback ?? ''}
									{:else if isCustom(column)}
										<column.component {...column.props} {column} {row} />
									{/if}
								</Table.Row.Data>
							{/if}
						{/each}

						<Table.Row.Header class="sticky right-0 z-1">
							<div class="flex flex-nowrap justify-end space-x-2">
								{#if config.actions?.length}
									{#each config.actions as action (action)}
										{#if !hideExpressions(filter, action.hide ?? false, row)}
											{#if isAction(action)}
												<button
													type="button"
													class="{action.icon ? 'btn-icon' : 'btn'} {action.classes}"
													onclick={() => onActionClick(action, [row])}
												>
													{#if action.icon}
														<iconify-icon icon={action.icon} width="24" height="24"></iconify-icon>
													{:else}
														{action.label}
													{/if}
												</button>
											{:else}
												<a
													type="button"
													href={action.url(row)}
													class="{action.icon ? 'btn-icon' : 'btn'} {action.classes}"
												>
													{#if action.icon}
														<iconify-icon icon={action.icon} width="24" height="24"></iconify-icon>
													{:else}
														{action.label}
													{/if}
												</a>
											{/if}
										{/if}
									{/each}
								{/if}

								<button type="button" class="btn-icon cursor-default" aria-label="More">
									<iconify-icon icon="radix-icons:dots-vertical" width="24" height="24"></iconify-icon>
								</button>
							</div>
						</Table.Row.Header>
					</Table.Row>
				{/each}
			</Table.Body>
		{:else}
			<Table.Body class="divide-y">
				<Table.Row>
					<Table.Row.Data class="text-center" colSpan={config.columns.length + 2}
						><h4 class="h4 py-4">{isLoading ? 'Loading...' : 'No data'}</h4></Table.Row.Data
					>
				</Table.Row>
			</Table.Body>
		{/if}
	</Table>

	<footer class="my-6 flex min-h-12 justify-between">
		<select
			name="size"
			id="size"
			class="select max-w-[150px]"
			value={filter.limit}
			onchange={(e) => (filter.limit = Number(e.currentTarget.value))}
			disabled={isLoading}
		>
			{#each pagination.amounts as v (v)}
				<option value={v}>Items {v}</option>
			{/each}
			<option value={data.rows.length}>Show All</option>
		</select>

		<Pagination
			data={data.rows}
			count={data.total || pagination.totalItems}
			page={filter.page + 1}
			pageSize={pagination.pageSize}
			showFirstLastButtons={pagination.showFirstLastButtons}
			siblingCount={pagination.siblingCount}
			onPageChange={(e) => (filter.page = e.page - 1)}
			onPageSizeChange={(e) => (pagination.pageSize = filter.limit = e.pageSize)}
		/>
	</footer>
</div>

<style>
	[type='checkbox']:checked.partial {
		background-image: none;
	}
</style>
