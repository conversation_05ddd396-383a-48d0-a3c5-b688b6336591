import type { DocumentNode, ExtractValues, NestedFields, StrictPropertyType, SwitchNestedIfArray } from '$lib/utils'
import type { Field } from '../Form'
import type { ComponentProps, Component } from 'svelte'

export enum ColumnType {
	String = 'String',
	Date = 'Date',
	Image = 'Image',
	ChipList = 'ChipList',
	Boolean = 'Boolean',
	Currency = 'Currency',
	Interval = 'Interval',
	Profile = 'Profile',
	FormField = 'FormField',
	Custom = 'Custom',
}

export const ColumnTypes: ColumnType[] = [
	ColumnType.String,
	ColumnType.Date,
	ColumnType.Image,
	ColumnType.ChipList,
	ColumnType.Boolean,
	ColumnType.Currency,
	ColumnType.Interval,
	ColumnType.Profile,
	ColumnType.FormField,
	ColumnType.Custom,
]

export interface BaseDataTableColumn<DTO> {
	label: string
	onclick?: (element: DTO) => void
	hide?: string | boolean | ((filter: DataTableFilter) => boolean)
	sortable?: string
	sticky?: boolean
	stickyEnd?: boolean
	href?: NestedFields<DTO> | ((row: DTO) => string | undefined)
	target?: '_blank' | '_self' | '_parent' | '_top'
	class?: string
}

export interface DataTableColumnWithProperty<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends Omit<BaseDataTableColumn<DTO>, 'onclick'> {
	onclick?: (element: DTO, property: P) => void
	property: P
	fallback?: StrictPropertyType<DTO, P>
}

export interface DataTableColumnWithBoolean<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends Omit<DataTableColumnWithProperty<DTO, P>, 'fallback'> {
	reversed?: boolean
	fallback?: boolean
}
export interface DataTableColumnWithDate<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends Omit<DataTableColumnWithProperty<DTO, P>, 'fallback'> {
	options?: Intl.DateTimeFormatOptions
	relative?: boolean
	relativeOptions?: Intl.RelativeTimeFormatOptions
	fallback?: Date
}

export interface Chip<DTO> {
	label: string
	property: NestedFields<DTO>
}

export interface DataTableColumnWithChips<DTO> extends Omit<BaseDataTableColumn<DTO>, 'onclick'> {
	onclick?: (element: DTO, item: Chip<DTO>) => void
	chips?: Array<Chip<DTO>>
	color?: { [index: string | number | symbol]: string } | ((element: DTO, item?: Chip<DTO>) => string)
}

export interface DataTableColumnWithDataChips<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends Omit<BaseDataTableColumn<DTO>, 'onclick'> {
	onclick?: (element: DTO, item: StrictPropertyType<DTO, P>) => void
	property: P
	labelProperty?: NestedFields<StrictPropertyType<DTO, P>>
	color?: { [index: string | number | symbol]: string } | ((element: DTO, item?: StrictPropertyType<DTO, P>) => string)
}

export interface DataTableColumnWithCurrency<DTO, P extends NestedFields<DTO> = NestedFields<DTO>>
	extends Omit<DataTableColumnWithProperty<DTO, P>, 'fallback'> {
	currency: string
	fallback?: string
}

export interface DataTableColumnWithInterval<DTO> extends BaseDataTableColumn<DTO> {
	start?: NestedFields<DTO>
	end?: NestedFields<DTO>
	options?: Intl.DateTimeFormatOptions
}

export interface DataTableColumnWithProfile<DTO> extends BaseDataTableColumn<DTO> {
	image?: NestedFields<DTO>
	name: NestedFields<DTO>
}

export interface DataTableColumnWithFormField<
	DTO,
	P extends NestedFields<DTO> = NestedFields<DTO>,
	N extends SwitchNestedIfArray<DTO, P> = SwitchNestedIfArray<DTO, P>,
> extends Omit<DataTableColumnWithProperty<DTO, P>, 'onclick'> {
	field: Field<N> & {
		onValueChange?: (value: StrictPropertyType<DTO, P>) => void
	}
}

export interface DataTableCustomColumn {
	label: string
	component: Component
	props: ComponentProps<Component>
	sortable?: string
	hide?: boolean
	sticky?: boolean
	stickyEnd?: boolean
	class?: string
}

export type Column<DTO, P extends NestedFields<DTO>, T extends ColumnType> = T extends ColumnType.Boolean
	? DataTableColumnWithBoolean<DTO, P>
	: T extends ColumnType.Date
		? DataTableColumnWithDate<DTO, P>
		: T extends ColumnType.ChipList
			? DataTableColumnWithChips<DTO> | DataTableColumnWithDataChips<DTO, P>
			: T extends ColumnType.Currency
				? DataTableColumnWithCurrency<DTO, P>
				: T extends ColumnType.Interval
					? DataTableColumnWithInterval<DTO>
					: T extends ColumnType.Profile
						? DataTableColumnWithProfile<DTO>
						: T extends ColumnType.FormField
							? DataTableColumnWithFormField<DTO, P>
							: T extends ColumnType.String | ColumnType.Image
								? DataTableColumnWithProperty<DTO, P>
								: DataTableCustomColumn

export type DataTableColumn<DTO> = ExtractValues<{
	[P in NestedFields<DTO>]: ExtractValues<{
		[T in ColumnType]: {
			type: T
		} & Column<DTO, P, T>
	}>
}>

export interface BaseDataTableAction<DTO> {
	hide?: string | boolean | ((filter: DataTableFilter, row?: DTO) => boolean)
	label?: string
	icon?: string
	classes?: string
}

export interface DataTableActionUrl<DTO> extends BaseDataTableAction<DTO> {
	url: (element: DTO) => string
}

export interface DataTableActionAction<DTO> extends BaseDataTableAction<DTO> {
	action: (elements: DTO[]) => void
	multiple?: boolean
}

export type DataTableAction<DTO> = DataTableActionUrl<DTO> | DataTableActionAction<DTO>

export interface DataTableConfig<DTO> {
	columns: Array<DataTableColumn<DTO>>
	actions?: Array<DataTableAction<DTO>>
	pagination?: {
		initialPage?: number
		pageSize?: number
		totalItems?: number
		showFirstLastButtons?: boolean
		siblingCount?: number
		amounts?: number[]
	}
	onSelection?: (element: DTO[]) => void
}

export interface DataTableData<DTO> {
	rows: DTO[]
	total: number
}

export interface DataTableFilter {
	page: number
	limit: number
	search?: string
	sort?: {
		key: string
		direction: 'asc' | 'desc'
	}
}

export function isImage<DTO extends DocumentNode>(
	column: DataTableColumn<DTO>,
): column is DataTableColumnWithProperty<DTO> & { type: ColumnType.Image } {
	return column.type === ColumnType.Image
}
export function isBoolean<DTO extends DocumentNode>(
	column: DataTableColumn<DTO>,
): column is DataTableColumnWithBoolean<DTO> & { type: ColumnType.Boolean } {
	return column.type === ColumnType.Boolean
}
export function isDate<DTO extends DocumentNode>(
	column: DataTableColumn<DTO>,
): column is DataTableColumnWithDate<DTO> & { type: ColumnType.Date } {
	return column.type === ColumnType.Date
}
export function isCurrency<DTO extends DocumentNode>(
	column: DataTableColumn<DTO>,
): column is DataTableColumnWithCurrency<DTO> & { type: ColumnType.Currency } {
	return column.type === ColumnType.Currency
}
export function isInterval<DTO extends DocumentNode>(
	column: DataTableColumn<DTO>,
): column is DataTableColumnWithInterval<DTO> & { type: ColumnType.Interval } {
	return column.type === ColumnType.Interval
}
export function isChips<DTO extends DocumentNode>(
	column: DataTableColumn<DTO>,
): column is DataTableColumnWithChips<DTO> & { type: ColumnType.ChipList } {
	return column.type === ColumnType.ChipList && (column as DataTableColumnWithChips<DTO>).chips !== undefined
}
export function isDataChips<DTO extends DocumentNode>(
	column: DataTableColumn<DTO>,
): column is DataTableColumnWithDataChips<DTO> & { type: ColumnType.ChipList } {
	return column.type === ColumnType.ChipList && (column as DataTableColumnWithDataChips<DTO>).property !== undefined
}
export function isProfile<DTO extends DocumentNode>(
	column: DataTableColumn<DTO>,
): column is DataTableColumnWithProfile<DTO> & { type: ColumnType.Profile } {
	return column.type === ColumnType.Profile
}
export function isFormField<DTO extends DocumentNode>(
	column: DataTableColumn<DTO>,
): column is DataTableColumnWithFormField<DTO> & {
	type: ColumnType.FormField
} {
	return column.type === ColumnType.FormField
}
export function isCustom<DTO extends DocumentNode>(
	column: DataTableColumn<DTO>,
): column is DataTableCustomColumn & { type: ColumnType.Custom } {
	return (
		typeof (column as DataTableCustomColumn).component !== 'undefined' &&
		typeof (column as DataTableCustomColumn).props !== 'undefined'
	)
}
export function isAction<DTO extends DocumentNode>(action: DataTableAction<DTO>): action is DataTableActionAction<DTO> {
	return (action as DataTableActionAction<DTO>).action !== undefined
}
