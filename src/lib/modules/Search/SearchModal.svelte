<script lang="ts">
	import { page } from '$app/state'

	import { menuNavLinks, type List } from '$lib/links'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'

	import { getAuth } from '$lib/services/auth.svelte'

	const auth = getAuth()
	const modal = getModal()

	// Classes
	const cBase = 'card overflow-hidden w-full max-w-[800px] shadow-xl pt-6'
	const cResults = 'overflow-x-auto max-h-[480px] hide-scrollbar p-6'
	const cResultAnchor =
		'flex justify-between hover:preset-tonal focus:!preset-filled-primary-500 outline-0 p-2 items-center'
	const cFooter = 'hidden md:flex items-center gap-4 preset-tonal text-surface-contrast-500/75 p-4 text-xs'

	// Local
	let searchTerm = $state('')
	const submenu = menuNavLinks(auth.profile, page?.params?.orgId ?? page?.data?.organizationId)
	const resultsCopy = [...submenu['/home'], ...submenu['/docs'], ...submenu['/profile'], ...submenu['/backoffice']]
	let results = $state(resultsCopy)

	// Elements
	let elemDocSearch = $state<HTMLElement>()

	function filterList(list: List) {
		return list.filter((rowObj) => {
			const formattedSearchTerm = searchTerm.toLowerCase() || ''
			return Object.values(rowObj).join(' ').toLowerCase().includes(formattedSearchTerm)
		})
	}

	function onInput(): void {
		const resultsDeepCopy = structuredClone(resultsCopy)
		results = resultsDeepCopy.filter((category) => {
			category.list = filterList(category.list)
			if (category.list.length) return category
		})
	}

	function onKeyDown(event: KeyboardEvent): void {
		if (['Enter', 'ArrowDown'].includes(event.code)) {
			const queryFirstAnchorElement = elemDocSearch?.querySelector('a')
			if (queryFirstAnchorElement) queryFirstAnchorElement.focus()
		}
	}
</script>

<div bind:this={elemDocSearch} class="modal-search {cBase}">
	<!-- Header -->
	<header class="modal-search-header px-6">
		<form class="w-full space-y-8">
			<div class="input-group grid-cols-[auto_1fr_auto]">
				<div class="ig-cell preset-tonal">
					<iconify-icon icon="radix-icons:magnifying-glass" width="16" height="16" class="!min-h-auto !min-w-auto"
					></iconify-icon>
				</div>
				<input
					class="ig-input"
					type="search"
					placeholder="Search..."
					bind:value={searchTerm}
					oninput={onInput}
					onkeydown={onKeyDown}
				/>
			</div>
		</form>
	</header>

	<!-- Results -->
	{#if results.length > 0}
		<nav class="list-nav {cResults}" tabindex="-1">
			{#each results as category (category)}
				{#if category.title.length > 0}
					<h6 class="h6 px-2 py-4 text-xs font-bold uppercase opacity-50">{category.title}</h6>
				{/if}
				<ul>
					{#each category.list as link (link.label)}
						<li class="text-lg">
							<a
								class={cResultAnchor}
								href={link.href}
								onclick={() => {
									modal.close()
								}}
							>
								<span class="flex-auto font-bold opacity-75">{link.label}</span>
								<span class="hidden text-sm opacity-50 md:inline">{link.href}</span>
							</a>
						</li>
					{/each}
				</ul>
			{/each}
		</nav>
	{:else}
		<div class="p-4">
			<p>No Results found for <code class="code">{searchTerm}</code>.</p>
		</div>
	{/if}

	<!-- Footer -->
	<footer class="modal-search-footer {cFooter}">
		<div><kbd class="kbd">Esc</kbd> to close</div>
		<div><kbd class="kbd">Tab</kbd> to navigate</div>
		<div><kbd class="kbd">Enter</kbd> to select</div>
	</footer>
</div>
