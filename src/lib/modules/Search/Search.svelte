<script lang="ts">
	import { getModal } from '$lib/modules/Modal/state.svelte.js'

	import SearchModal from './SearchModal.svelte'

	import type { ModalInstance, ModalPresetProps } from '$lib/modules/Modal'

	const { classes, ...rest }: ModalPresetProps & { classes?: string } = $props()

	const isOsMac = navigator.userAgent.search('Mac') !== -1

	const modal = getModal()

	modal.register({
		search: {
			component: SearchModal,
			position: 'items-start justify-center',
			margin: 'p-[12vh]',
			width: 'w-full md:w-[700px]',
			...rest,
		},
	})

	let modalInstance: ModalInstance | undefined
	function openSearch() {
		modalInstance = modal.open('search')
	}

	function onkeydown(e: KeyboardEvent): void {
		if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
			// NOTE: Prevent default browser behavior of focusing URL bar
			e.preventDefault()
			// NOTE: using stopPropagation to override Chrome for Windows search shortcut
			e.stopPropagation()

			// If modal currently open, close modal (allows to open/close search with CTRL/⌘+K)
			if (modalInstance?.close) {
				modalInstance.close()
			} else {
				openSearch()
			}
		}
	}
</script>

<svelte:window {onkeydown} />

<div class="self-center md:ml-4 md:inline {classes}">
	<button class="preset-tonal btn hover:preset-tonal-primary space-x-1 align-middle" onclick={openSearch}>
		<iconify-icon icon="radix-icons:magnifying-glass" width="20" height="20"></iconify-icon>
		<small class="hidden md:inline-block">{isOsMac ? '⌘' : 'Ctrl'}+K</small>
	</button>
</div>
