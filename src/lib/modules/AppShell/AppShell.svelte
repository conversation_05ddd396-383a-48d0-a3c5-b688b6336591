<script lang="ts">
	import type { UnionConcat } from '$lib/utils'
	import type { Snippet } from 'svelte'

	type Regions = 'header' | 'sidebarLeft' | 'sidebarRight' | 'pageHeader' | 'footer'
	type HideRegions = 'header' | 'footer'

	const {
		// Props
		fixed = '' as UnionConcat<Regions, ' '>,
		sticky = '' as UnionConcat<Regions, ' '>,
		hideOnScroll = '' as UnionConcat<HideRegions, ' '>,
		hideSizeThreshold = 640,
		hideDuration = 'duration-300',
		hideOffset = 0,
		hideTolerance = 0,

		// Props (regions)
		shell = '',
		colShell = '',
		rowShell = '',
		headerShell = '',
		sidebarLeftShell = '',
		sidebarRightShell = '',
		pageHeaderShell = '',
		mainShell = '',
		pageFooterShell = '',
		footerShell = '',

		// Snippets
		children,
		header,
		sidebarLeft,
		sidebarRight,
		pageHeader,
		pageFooter,
		footer,
	}: {
		// Props
		fixed?: UnionConcat<Regions, ' '>
		sticky?: UnionConcat<Regions, ' '>
		hideOnScroll?: UnionConcat<HideRegions, ' '>
		hideSizeThreshold?: number
		hideDuration?: string
		hideOffset?: number
		hideTolerance?: number

		// Props (regions)
		shell?: string
		colShell?: string
		rowShell?: string
		headerShell?: string
		sidebarLeftShell?: string
		sidebarRightShell?: string
		pageHeaderShell?: string
		mainShell?: string
		pageFooterShell?: string
		footerShell?: string

		// Snippets
		children?: Snippet
		header?: Snippet
		sidebarLeft?: Snippet
		sidebarRight?: Snippet
		pageHeader?: Snippet
		pageFooter?: Snippet
		footer?: Snippet
	} = $props()

	let headerHeight = $state(0)
	let pageHeaderHeight = $state(0)
	let pageFooterHeight = $state(0)
	let footerHeight = $state(0)

	let scrollY = $state(0)
	let innerWidth = $state(0)
	let innerHeight = $state(0)

	const lastScrollY: { [key in HideRegions]: number } = {
		header: 0,
		footer: 0,
	}

	function calculateHidingRegions(region: HideRegions, y: number) {
		if (hideSizeThreshold <= Math.min(innerWidth, innerHeight) || !hideOnScroll.includes(region)) {
			return region === 'header' ? headerShell : footerShell
		}

		const diff = lastScrollY[region] - y
		lastScrollY[region] = y

		if (y < Math.max(hideOffset, headerHeight) || Math.abs(diff) <= hideTolerance || diff > 0) {
			return `translate-y-0 ${hideDuration} ${region === 'header' ? headerShell : footerShell}`
		}

		return `${region === 'header' ? '-translate-y-full' : 'translate-y-full'} ${hideDuration} ${region === 'header' ? headerShell : footerShell}`
	}

	const rows = $derived(
		header && footer
			? 'grid-rows-[auto_1fr_auto]'
			: header
				? 'grid-rows-[auto_1fr_auto]'
				: footer
					? 'grid-rows-[1fr_auto]'
					: 'grid-rows-[1fr]',
	)

	const cols = $derived(
		sidebarLeft && sidebarRight
			? 'md:grid-cols-[auto_1fr_auto]'
			: sidebarLeft
				? 'md:grid-cols-[auto_1fr]'
				: sidebarRight
					? 'md:grid-cols-[1fr_auto]'
					: 'md:grid-cols-[1fr]',
	)

	const innerRows = $derived(
		pageHeader && pageFooter
			? 'grid-rows-[auto_1fr_auto]'
			: pageHeader
				? 'grid-rows-[auto_1fr_auto]'
				: pageFooter
					? 'grid-rows-[1fr_auto]'
					: 'grid-rows-[1fr]',
	)

	const fixedOrSticky = $derived(`${fixed} ${sticky}`)

	const stickyHeader = $derived(fixed.includes('header') ? 'fixed' : 'sticky')
	const stickyFooter = $derived(fixed.includes('footer') ? 'fixed' : 'sticky')
	const stickySidebarLeft = $derived(fixed.includes('sidebarLeft') ? 'fixed' : 'sticky')
	const stickySidebarRight = $derived(fixed.includes('sidebarRight') ? 'fixed' : 'sticky')
	const stickyPageHeader = $derived(fixed.includes('pageHeader') ? 'fixed' : 'sticky')

	const headerClass = $derived(calculateHidingRegions('header', scrollY))
	const footerClass = $derived(calculateHidingRegions('footer', scrollY))
</script>

<svelte:window bind:scrollY bind:innerWidth bind:innerHeight />

<div
	id="app-shell"
	class="grid min-h-dvh {rows} {shell}"
	style="--header-height: {headerHeight}px; --page-header-height: {pageHeaderHeight}px; --page-footer-height: {pageFooterHeight}px; --footer-height: {footerHeight}px;"
>
	<!-- Header -->
	{#if header}
		<header
			bind:clientHeight={headerHeight}
			id="shell-header"
			class={fixedOrSticky.includes('header')
				? `${stickyHeader} top-0 right-0 left-0 z-30 ${headerClass}`
				: headerClass}
		>
			{@render header?.()}
		</header>
	{/if}

	<!-- Grid Column -->
	<div class="grid grid-cols-1 {cols} {colShell}">
		<!-- Sidebar (left) -->
		{#if sidebarLeft}
			<aside
				id="shell-sidebarLeft"
				class="{fixedOrSticky.includes('sidebarLeft') &&
					`${stickySidebarLeft} z-10 ${fixedOrSticky.includes('header') ? 'top-(--header-height) h-[calc(100dvh-var(--header-height)-var(--footer-height))]' : 'top-0 h-dvh'}`} {sidebarLeftShell}"
			>
				{@render sidebarLeft?.()}
			</aside>
		{/if}

		<!-- Grid Row -->
		<div class="col-span-1 grid {innerRows} {rowShell}">
			<!-- Page Header -->
			{#if pageHeader}
				<header
					bind:clientHeight={pageHeaderHeight}
					id="page-header"
					class="{fixedOrSticky.includes('pageHeader') &&
						`${stickyPageHeader} z-20 ${fixedOrSticky.includes('header') ? 'top-(--header-height)' : 'top-0'}`} {pageHeaderShell}"
				>
					{@render pageHeader?.()}
				</header>
			{/if}

			<!-- Main -->
			<main id="page-content" class={mainShell}>{@render children?.()}</main>

			<!-- Page Footer -->
			{#if pageFooter}
				<footer bind:clientHeight={pageFooterHeight} id="page-footer" class={pageFooterShell}>
					{@render pageFooter?.()}
				</footer>
			{/if}
		</div>

		<!-- Sidebar (right) -->
		{#if sidebarRight}
			<aside
				id="shell-sidebarRight"
				class="{fixedOrSticky.includes('sidebarRight') &&
					`${stickySidebarRight} z-10 ${fixedOrSticky.includes('header') ? 'top-(--header-height) h-[calc(100dvh-var(--header-height)-var(--footer-height))]' : 'top-0 h-dvh'}`} {sidebarRightShell}"
			>
				{@render sidebarRight?.()}
			</aside>
		{/if}
	</div>

	<!-- footer -->
	{#if footer}
		<footer
			bind:clientHeight={footerHeight}
			id="shell-footer"
			class={fixedOrSticky.includes('footer')
				? `${stickyFooter} bottom-0 z-10 backdrop-blur-lg ${footerClass}`
				: footerClass}
		>
			{@render footer?.()}
		</footer>
	{/if}
</div>

<style>
	:root {
		--header-height: 0;
		--page-header-height: 0;
		--page-footer-height: 0;
		--footer-height: 0;
	}

	:global(html),
	:global(body) {
		height: 100%;
	}
</style>
