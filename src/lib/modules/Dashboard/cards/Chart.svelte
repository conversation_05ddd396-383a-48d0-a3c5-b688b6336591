<script lang="ts">
	import CardHeader from './CardHeader.svelte'
	import {
		ChartType,
		type ChartData,
		type HeaderOptions,
		type ChartOptions,
		type CardData,
		resolveGetter,
	} from '../dashboard.interface'
	import Bar<PERSON>hart from './charts/BarChart.svelte'
	import BoxPlot from './charts/BoxPlot.svelte'
	import LineChart from './charts/LineChart.svelte'
	import PieChart from './charts/PieChart.svelte'

	const {
		type,
		getter,
		options = undefined,
		group = undefined,
		header = undefined,

		background = 'bg-noise bg-surface-50 dark:bg-surface-900 border border-surface-100 dark:border-surface-900',
		rounded = 'rounded-md',
		padding = '',
	}: {
		type: ChartType
		getter: CardData<ChartData<typeof type>> | Promise<CardData<ChartData<typeof type>>>
		options?: ChartOptions
		group?: string
		header?: HeaderOptions

		background?: string
		rounded?: string
		padding?: string
	} = $props()

	function isLineChart(
		type: ChartType,
		data: ChartData<ChartType> | undefined,
	): data is ChartData<ChartType.LINE> | undefined {
		return type === ChartType.LINE
	}

	function isBarChart(
		type: ChartType,
		data: ChartData<ChartType> | undefined,
	): data is ChartData<ChartType.BAR> | undefined {
		return type === ChartType.BAR
	}

	function isPieChart(
		type: ChartType,
		data: ChartData<ChartType> | undefined,
	): data is ChartData<ChartType.PIE> | undefined {
		return type === ChartType.PIE
	}

	function isBoxplotChart(
		type: ChartType,
		data: ChartData<ChartType> | undefined,
	): data is ChartData<ChartType.BOXPLOT> | undefined {
		return type === ChartType.BOXPLOT
	}
</script>

<div class="card flex h-full flex-col {background} {rounded} {padding}">
	{#await resolveGetter<ChartData<typeof type>>(getter)}
		<section class="flex h-full items-center justify-center self-center p-4 text-3xl">
			<span class="text-surface-300 dark:text-surface-500 text-3xl" style="margin-top: -2rem;">Loading...</span>
		</section>
	{:then getter}
		{#if header}
			<CardHeader
				class="px-8 pt-7 pb-2"
				title={header.title}
				description={header.description}
				prefix={header.prefix}
				avatarUrl={header.avatarUrl}
				avatarName={header.avatarName}
				avatarIcon={header.avatarIcon}
				isLoading={getter.isLoading}
			/>
		{/if}

		{#await getter.data}
			<section class="flex h-full items-center justify-center self-center p-4 text-3xl">
				<span class="text-surface-300 dark:text-surface-500 text-3xl" style="margin-top: -2rem;">Loading...</span>
			</section>
		{:then data}
			{#if !data?.series?.length}
				<section class="flex h-full items-center justify-center self-center p-4 text-3xl">
					<span class="text-surface-300 dark:text-surface-500 text-3xl" style="margin-top: -2rem;"
						>No Data Available</span
					>
				</section>
			{:else if isLineChart(type, data)}
				<LineChart class="pt-0 pr-2 pb-2 pl-3" {options} {data} {group} />
			{:else if isBarChart(type, data)}
				<BarChart class="pt-0 pr-2 pb-2 pl-3" {options} {data} {group} />
			{:else if isPieChart(type, data)}
				<PieChart class="pt-0 pr-2 pb-2 pl-3" {options} {data} {group} />
			{:else if isBoxplotChart(type, data)}
				<BoxPlot class="pt-0 pr-2 pb-2 pl-3" {options} {data} {group} />
			{/if}
		{/await}
	{/await}
</div>
