<script lang="ts" module>
	import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'echarts/charts'
	import {
		TitleComponent,
		TooltipComponent,
		GridComponent,
		DatasetComponent,
		TransformComponent,
		BrushComponent,
		LegendComponent,
		Too<PERSON><PERSON>Component,
		Mark<PERSON>ointComponent,
		Mark<PERSON><PERSON><PERSON>omponent,
		MarkLineComponent,
	} from 'echarts/components'
	import { getInstanceByDom, type EChartsCoreOption, type ComposeOption } from 'echarts/core'
	import * as echarts from 'echarts/core'
	import { LabelLayout, UniversalTransition } from 'echarts/features'
	import { CanvasRenderer } from 'echarts/renderers'

	import { addChart } from './echart.state.svelte.js'

	import type { ChartType } from '../../dashboard.interface'
	import type { BoxplotSeriesOption } from 'echarts'
	import type { BarSeriesOption, LineSeriesOption, PieSeriesOption } from 'echarts/charts'
	import type {
		TitleComponentOption,
		Too<PERSON><PERSON><PERSON>omponentO<PERSON>,
		Grid<PERSON>omponentOption,
		DatasetComponentOption,
	} from 'echarts/components'
	import type { Action } from 'svelte/action'

	export type EChartsOptions = ComposeOption<
		| BarSeriesOption
		| LineSeriesOption
		| BoxplotSeriesOption
		| PieSeriesOption
		| TitleComponentOption
		| TooltipComponentOption
		| GridComponentOption
		| DatasetComponentOption
	>

	echarts.use([
		TitleComponent,
		TooltipComponent,
		ToolboxComponent,
		BrushComponent,
		LegendComponent,
		GridComponent,
		DatasetComponent,
		TransformComponent,
		UniversalTransition,
		LabelLayout,
		CanvasRenderer,

		BarChart,
		LineChart,
		BoxplotChart,
		PieChart,
		ScatterChart,

		MarkPointComponent,
		MarkLineComponent,
		MarkAreaComponent,
	])

	export type EChartsTheme = string | object
	export type EChartsRenderer = 'canvas' | 'svg'

	export type EChartOptions = {
		theme?: EChartsTheme
		renderer?: EChartsRenderer
		options: EChartsOptions
		notMerge: boolean
		lazyUpdate: boolean
		type: ChartType
		group?: string
	}

	function checkDiff(oldOptions: EChartsOptions, newOptions: EChartsOptions) {
		const oldSeries = oldOptions.series
		const newSeries = newOptions.series

		if (oldSeries && newSeries) {
			if (!Array.isArray(oldSeries) || !Array.isArray(newSeries)) {
				return true
			}

			if (oldSeries.length !== newSeries.length) {
				return true
			}

			for (let i = 0; i < oldSeries.length; i++) {
				if (oldSeries[i].name !== newSeries[i].name) {
					return true
				}
			}
		}

		return false
	}

	export const chartable: Action<HTMLElement, EChartOptions> = (element, args) => {
		const { theme, renderer, options, notMerge, lazyUpdate, type, group } = {
			...args,
		}
		const echartsInstance = echarts.init(element, theme, { renderer })

		addChart(echartsInstance, type, group)

		echartsInstance.setOption(options)

		let oldOptions = args.options

		$effect(() => {
			echartsInstance.setOption(
				{
					...args.options,
				},
				{
					notMerge,
					lazyUpdate,
					replaceMerge: checkDiff(oldOptions, args.options)
						? [
								// 'xAxis',
								// 'yAxis',
								'series',
							]
						: [],
				},
			)

			oldOptions = args.options

			return () => {
				echartsInstance.dispose()
			}
		})
	}
</script>

<script lang="ts">
	import { onMount } from 'svelte'

	import { getTheme } from '$lib/services/theme.svelte.js'

	let {
		class: klass,
		options,
		theme = undefined,
		renderer = 'canvas',
		chart = $bindable(undefined),
		chartInstance = $bindable(undefined),
		type,
		group = undefined,
		notMerge = false,
		lazyUpdate = false,
	}: {
		class?: string
		options: EChartsCoreOption
		theme?: EChartsTheme
		renderer?: EChartsRenderer
		chart?: HTMLElement
		chartInstance?: echarts.ECharts
		type: ChartType
		group?: string
		// See https://echarts.apache.org/en/api.html#echartsInstance.setOption.
		notMerge?: boolean
		// See https://echarts.apache.org/en/api.html#echartsInstance.setOption.
		lazyUpdate?: boolean
	} = $props()

	const globalTheme = getTheme()

	onMount(() => {
		if (chart !== undefined) {
			chartInstance = getInstanceByDom(chart)

			const resizeObserver = new ResizeObserver(() => {
				chartInstance?.resize()
			})

			resizeObserver.observe(chart)

			// This callback cleans up the observer
			return () => resizeObserver.unobserve(chart as HTMLElement)
		}
	})
</script>

{#key theme}
	<div
		class={`chart ${klass ?? ''}`}
		bind:this={chart}
		use:chartable={{
			renderer,
			theme: theme || globalTheme.mode.toLowerCase(),
			options,
			notMerge,
			lazyUpdate,
			type,
			group,
		}}
	></div>
{/key}

<style>
	.chart {
		height: 100%;
		width: 100%;
	}
</style>
