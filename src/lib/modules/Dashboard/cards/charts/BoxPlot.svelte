<script lang="ts">
	import { commonOptions, resolveCommonOptions } from './charts.data'
	import EChart from './EChart.svelte'
	import { ChartType, type ChartData, type ChartOptions } from '../../dashboard.interface'
	import { toolbox } from '../../toolbox.state.svelte.js'

	const {
		class: klass,
		data,
		group = undefined,
		options = undefined,
	}: {
		class?: string
		data: ChartData<ChartType.BOXPLOT>
		group?: string
		options?: ChartOptions
	} = $props()

	const echartsData = $derived.by(() => {
		const common = {
			...commonOptions,
			...resolveCommonOptions(options || {}),
		}

		return {
			...common,
			tooltip: {
				trigger: 'axis',
				appendToBody: true,
			},
			xAxis: {
				type: 'value',
				nameLocation: 'middle',
				splitArea: {
					show: true,
				},
			},
			yAxis: {
				type: 'category',
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				data: data?.series?.[0]?.data?.map((item: any) => item.name),
				splitLine: {
					show: toolbox.state('ShowChartGridLines') || options?.splitLine,
					// lineStyle: {
					// 	color: [$modeCurrent ? '#ccc' : '#ccc'],
					// 	opacity: $modeCurrent ? 0.5 : 0.1
					// }
				},
			},
			series: data?.series,
		}
	})
</script>

<EChart class={`size-full ${klass ?? ''}`} type={ChartType.BOXPLOT} {group} options={echartsData} />
