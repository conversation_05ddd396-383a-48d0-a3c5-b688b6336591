<script lang="ts">
	import { getLocaleForDateFormat } from '$lib/utils'

	import { commonOptions, resolveCommonOptions } from './charts.data'
	import EChart from './EChart.svelte'
	import { ChartType, type ChartData, type ChartOptions } from '../../dashboard.interface'
	import { toolbox } from '../../toolbox.state.svelte.js'

	const {
		class: klass,
		data,
		group = undefined,
		options = undefined,
	}: {
		class?: string
		data?: ChartData<ChartType.BAR>
		group?: string
		options?: ChartOptions
	} = $props()

	const AxisColor = 'rgba(128, 128, 128, 0.6)'

	const echartsData = $derived.by(() => {
		const common = {
			...commonOptions,
			...resolveCommonOptions(options || {}),
		}

		const minYY = options?.minY
			? options?.minY
			: options?.calculateMinY || options?.hideAxis
				? Math.min(
						// eslint-disable-next-line @typescript-eslint/no-explicit-any
						...[...((data?.series as any) || [])].sort((a, b) => {
							return Math.min(...a.data) - Math.min(...b.data)
						})[0].data,
					)
				: 0

		const maxYY = options?.maxY ? options?.maxY : 0

		return {
			...data,
			...common,
			legend: {
				...common.legend,
				data: data?.series?.map((s) => s.name as string),
				...data?.legend,
			},
			tooltip: {
				trigger: 'axis',
				appendToBody: true,
				axisPointer: {
					type: 'shadow-sm',
				},
				// ...(await this.resolveTooltip()),
				...data?.tooltip,
			},
			xAxis: {
				type: 'category',
				splitLine: {
					show: options?.splitLine || false,
					// lineStyle: {
					// 	color: [$modeCurrent ? '#ccc' : '#ccc'],
					// 	opacity: $modeCurrent ? 0.5 : 0.2
					// }
				},
				// axisLabel: {
				// 	// showMinLabel: false,
				// 	// interval: 0,
				// },
				...data?.xAxis,
			},
			yAxis: {
				...data?.yAxis,
				...(minYY && { min: options?.minY ? options?.minY : Math.floor(minYY / 1.05) }),
				...(maxYY && { max: maxYY }),
				splitLine: {
					show: toolbox.state('ShowChartGridLines') || false,
					// lineStyle: {
					// 	color: [$modeCurrent ? '#ccc' : '#ccc'],
					// 	opacity: $modeCurrent ? 0.5 : 0.1
					// }
				},
				axisLabel: {
					show: options?.hideAxis ? false : true,
					color: AxisColor,
					formatter: (value: number) =>
						new Number(value).toLocaleString(getLocaleForDateFormat(), {
							notation: 'compact',
							compactDisplay: 'short',
						}),
				} as never,
			},
			series: data?.series?.map((s) => ({
				...s,
				data: options?.roundDecimals ? s.data?.map((d) => Number(d).toFixed(options?.roundDecimals)) : s.data,
			})),
		}
	})

	// function resolveStackHorizontalLineOptions() {
	//   const common = {
	//     ...commonOptions,
	//     ...resolveCommonOptions(),
	//   }

	//   return {
	//     ...common,
	//     legend: {
	//       ...common.legend,
	//       data: data.series?.map((s) => s.name),
	//     },
	//     tooltip: {
	//       trigger: 'axis',
	//       axisPointer: {
	//         type: 'shadow-sm',
	//       },
	//       ...(await this.resolveTooltip()),
	//     },
	//     xAxis: {
	//       type: 'value',
	//       showMinLabel: false,
	//       ...data.xAxis,
	//     },
	//     yAxis: {
	//       type: 'category',
	//       ...data.yAxis,
	//     },
	//     series: data.series,
	//   }
	// }
</script>

<EChart class={`size-full ${klass ?? ''}`} type={ChartType.BAR} {group} options={echartsData} />
