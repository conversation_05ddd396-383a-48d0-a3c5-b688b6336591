<script lang="ts">
	import { commonOptions, resolveCommonOptions } from './charts.data'
	import EChart from './EChart.svelte'
	import { ChartType, type ChartData, type ChartOptions } from '../../dashboard.interface'
	import { toolbox } from '../../toolbox.state.svelte.js'

	const {
		class: klass,
		data,
		group = undefined,
		options = undefined,
	}: {
		class?: string
		data?: ChartData<ChartType.PIE>
		group?: string
		options?: ChartOptions
	} = $props()

	const echartsData = $derived.by(() => {
		const common = {
			...commonOptions,
			...resolveCommonOptions(options || {}),
		}

		let legend = {}
		if (options?.legendShow) {
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			const propertyNamesArr = data?.series?.map((series) => series.data?.map((s: any) => s.name))
			const propertyNames = propertyNamesArr?.reduce((acc, value) => acc?.concat(value), [])

			legend = {
				legend: {
					...common.legend,
					data: [...new Set(propertyNames || [])],
				},
			}
		}

		return {
			...common,
			...legend,
			tooltip: {
				trigger: 'item',
				appendToBody: true,
				// ...(await this.resolveTooltip()),
			},
			// ...(await this.resolveAxis()),
			title: data?.title,
			series: data?.series?.map((s) => {
				if (s.label) {
					s.label.show = toolbox.state('ShowChartGridLines')
				}
				return s
			}),
		}
	})
</script>

<EChart class={`size-full ${klass ?? ''}`} type={ChartType.PIE} {group} options={echartsData} />
