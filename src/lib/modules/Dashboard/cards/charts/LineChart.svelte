<script lang="ts">
	import echartsStats from 'echarts-stat'

	import { getLocaleForDateFormat } from '$lib/utils'

	import { commonOptions, resolveCommonOptions, resolveTooltip } from './charts.data'
	import EChart from './EChart.svelte'
	import { ChartType, type ChartData, type ChartOptions } from '../../dashboard.interface'
	import { toolbox } from '../../toolbox.state.svelte.js'

	import type { SelectedAreas, Statistics } from '../../toolbox.interface'
	import type { YAXisOption } from 'echarts/types/dist/shared.js'

	const {
		class: klass,
		data,
		group = undefined,
		options = undefined,
	}: {
		class?: string
		data?: ChartData<ChartType.LINE | ChartType.BAR>
		group?: string
		options?: ChartOptions
	} = $props()

	const AxisColor = 'rgba(128, 128, 128, 0.6)'

	const echartsData = $derived.by(() => {
		const series = calcStatisticsForLineChart(
			data?.series?.map((series) => ({
				...series,
				emphasis: {
					lineStyle: {
						width: 2,
					},
				},
				data: series.data?.map((d, i, arr) => {
					if (options?.roundDecimals !== undefined && d) {
						d = Number(Number(d).toFixed(options?.roundDecimals))
					}

					if (i === arr.length - 1 && options?.opacityLast !== false) {
						return {
							value: d ? d : '-',
							itemStyle: {
								opacity: 0.2,
							},
						}
					}

					return d
				}),
				...(series.markPoint && {
					markPoint: {
						...series.markPoint,
						itemStyle: {
							color: '#fff',
						},
						label: {
							color: '#000',
						},
					},
				}),
			})) as never,
			toolbox.state('AnalysisTools'),
		)

		const common = {
			...commonOptions,
			...resolveCommonOptions(options || {}),
		}

		return {
			...common,
			legend: {
				...common.legend,
				data: data?.series?.map((s) => s.name as string),
				...resolveLineLegend(toolbox.state('ChartSelectAreas'), data),
			},
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					animation: false,
				},
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				position: (pos: number[], _params: any, _el: any, _elRect: any, size: { viewSize: number[] }) => {
					const obj = { top: 20, [['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]]: 30 }
					return obj
				},
				...resolveTooltip(options || {}, toolbox.state('AnalysisTools'), data),
			},
			...resolveAxis(toolbox.state('AnalysisTools'), toolbox.state('ShowChartGridLines'), data),
			series,
		}
	})

	function resolveAxis(
		$statistics: Statistics,
		$showGrid: boolean,
		$data?: ChartData<ChartType.LINE | ChartType.BAR | ChartType.SCATTER>,
	): ChartData<ChartType.LINE | ChartType.BAR | ChartType.SCATTER> {
		const minY = $data?.isPercent ? 0 : options?.minY
		const maxY = $data?.isPercent ? 100 : options?.maxY
		const isPercent = $data?.isPercent !== undefined ? $data?.isPercent : options?.isPercent

		const seriesByAxisIndex = $data?.series?.reduce(
			(acc, cur) => {
				const index = cur.yAxisIndex || 0
				if (!acc[index]) {
					acc[index] = []
				}
				acc[index]?.push(cur)
				return acc
			},
			[] as Array<ChartData<ChartType.LINE | ChartType.BAR | ChartType.SCATTER>['series']>,
		)

		const isMixed = seriesByAxisIndex && Object.keys(seriesByAxisIndex).length > 1

		const leftSeries = isMixed ? seriesByAxisIndex[0] : $data?.series

		const minYYLeft = minY
			? minY
			: options?.calculateMinY || options?.hideAxis
				? Math.min(
						// eslint-disable-next-line @typescript-eslint/no-explicit-any
						...[...((leftSeries as any) || [])].sort((a, b) => {
							return Math.min(...a.data) - Math.min(...b.data)
						})[0].data,
					)
				: undefined

		const maxYYLeft = maxY ? maxY : undefined

		return {
			xAxis: {
				// type: 'value',
				...(!options?.boundaryGap && { boundaryGap: false }),
				// data: data.xAxisData ? data.xAxisData : dates,
				show: options?.hideAxis ? false : true,
				// interval: 8,
				axisLine: {
					lineStyle: {
						color: AxisColor,
					},
				},
				axisLabel: {
					show: options?.hideAxis ? false : true,
					// formatter: (w) => {
					//   if (isQuartersView) return w.replace(' ', '\n')
					//   else
					//     return format(w,
					//       isWeeksView ? 'DD MMM[\n]YYYY' : isQuartersView ? 'Qo[\n]YYYY' : isYearsView ? 'YYYY' : 'MMM[\n]YYYY'
					//     )
					// },
					color: AxisColor,
				},
				// showMinLabel: true,
				// showMaxLabel: true,
				axisTick: {
					show: options?.hideAxis ? false : true,
					lineStyle: {
						color: AxisColor,
					},
				},
				...$data?.xAxis,
			},
			yAxis: [
				{
					type: $statistics?.logScale ? 'log' : 'value',
					position: 'left',
					axisTick: {
						show: options?.hideAxis ? false : true,
						lineStyle: {
							color: AxisColor,
						},
					},
					axisLine: {
						show: options?.hideAxis ? false : true,
						lineStyle: {
							color: AxisColor,
						},
					},
					axisLabel: {
						show: options?.hideAxis ? false : true,
						color: AxisColor,
						formatter: (value: number) =>
							`${new Number(value).toLocaleString(getLocaleForDateFormat(), { notation: 'compact', compactDisplay: 'short' })}${isPercent ? ' %' : ''}`,
					} as never,
					...(minYYLeft !== undefined && { min: options?.minY ? options?.minY : Math.floor(minYYLeft / 1.05) }),
					...(maxYYLeft && { max: maxYYLeft }),
					splitLine: {
						show: $showGrid || options?.splitLine,
						// lineStyle: {
						// 	color: [$modeCurrent ? '#ccc' : '#ccc'],
						// 	opacity: $modeCurrent ? 0.5 : 0.1
						// }
					},
				},
				...(isMixed
					? seriesByAxisIndex.slice(1, seriesByAxisIndex.length).map((series) => {
							const minYYRight =
								options?.calculateMinY || options?.hideAxis
									? Math.min(
											// eslint-disable-next-line @typescript-eslint/no-explicit-any
											...[...((series as any) || [])].sort((a, b) => {
												return Math.min(...a.data) - Math.min(...b.data)
											})[0].data,
										)
									: 0

							return {
								type: $statistics?.logScale ? 'log' : 'value',
								position: 'right',
								axisTick: {
									show: options?.hideAxis ? false : true,
									lineStyle: {
										color: AxisColor,
									},
								},
								axisLine: {
									show: options?.hideAxis ? false : true,
									lineStyle: {
										color: AxisColor,
									},
								},
								axisLabel: {
									show: options?.hideAxis ? false : true,
									color: AxisColor,
									formatter: (value: number) =>
										`${new Number(value).toLocaleString(getLocaleForDateFormat(), {
											notation: 'compact',
											compactDisplay: 'short',
										})}${options?.isPercentSecondary ? ' %' : ''}`,
								} as never,
								...(minYYRight && { min: Math.floor(minYYRight / 1.05) }),
							} satisfies YAXisOption
						})
					: []),
			],
		}
	}

	function resolveLineLegend($selectAreas: SelectedAreas, $data?: ChartData<ChartType.LINE | ChartType.BAR>) {
		if ($selectAreas?.areas.length) {
			const variationMap: { [index: string]: number } = {}

			// const data = data?.series?.reduce((acc, cur) => ({ ...acc, [cur.name as string | number]: cur }), {})

			// for (const key in data) {
			//   const initialValue = getClosestValueFromArray(
			//     data[key].data,
			//     selectAreas[0].coordRange[0],
			//     selectAreas[0].coordRange[1]
			//   )
			//   const finalValue = getClosestValueFromArray(
			//     data[key].data,
			//     selectAreas[0].coordRange[0],
			//     selectAreas[0].coordRange[1],
			//     true
			//   )
			//   variationMap[key] = Number(((finalValue - initialValue) / finalValue).toFixed(2))
			// }

			for (const series of $data?.series || []) {
				const initialValue = getClosestValueFromArray(
					series.data || [],
					$selectAreas.areas[0].coordRange[0],
					$selectAreas.areas[0].coordRange[1],
				)
				const finalValue = getClosestValueFromArray(
					series.data || [],
					$selectAreas.areas[0].coordRange[0],
					$selectAreas.areas[0].coordRange[1],
					true,
				)
				variationMap[series.name as never] = Number(((finalValue - initialValue) / finalValue).toFixed(2))
			}

			return {
				formatter: (name: string) => {
					if (typeof variationMap[name] !== 'undefined' && !isNaN(variationMap[name])) {
						return `${name} ({${variationMap[name] > 0 ? 'positive' : 'negative'}|${variationMap[name]}})`
					}

					return `${name} (x)`
				},
				textStyle: {
					rich: {
						positive: {
							color: '#008000',
						},
						negative: {
							color: '#ff0000',
						},
					},
				},
			}
		}

		return {}
	}

	function calcStatisticsForLineChart(
		series: ChartData<ChartType.LINE | ChartType.BAR>['series'],
		$statistics: Statistics,
	): ChartData<ChartType.LINE | ChartType.BAR | ChartType.SCATTER>['series'] {
		if (!$statistics?.trendLine) {
			return series
		}

		const result: ChartData<ChartType.LINE | ChartType.BAR | ChartType.SCATTER>['series'] = []
		for (const s of series || []) {
			if ($statistics.showOriginalData) {
				result.push({ ...s, type: 'scatter' as never })
			}

			const stat = echartsStats.regression(
				$statistics.regression,
				s.data?.map((val, i) =>
					// eslint-disable-next-line @typescript-eslint/no-explicit-any
					typeof (val as any)?.value === 'undefined' ? [i + 1, val] : [i + 1, (val as any)?.value],
				) || [],
				$statistics.polynomialDegree || 2,
			)

			const points = []
			for (const point of stat.points) {
				points[point[0] - 1] = Math.round(point[1])
			}

			result.push({
				...s,
				name: $statistics.showOriginalData ? `${s.name}-stat` : s.name,
				type: ChartType.LINE,
				smooth: true,
				showSymbol: false,
				data: points,
			} as never)
		}

		return result
	}

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	function getClosestValueFromArray(array: any[], start: number, stop: number, invert = false) {
		if (invert) {
			if (array[stop]) {
				return array[stop]
			}

			for (let i = stop; i > Math.max(start, 0); i--) {
				if (array[i]) {
					return array[i]
				}
			}

			return undefined
		}

		if (array[start]) {
			return array[start]
		}

		for (let i = start; i < Math.min(stop, array.length); i++) {
			if (array[i]) {
				return array[i]
			}
		}

		return undefined
	}
</script>

<EChart class={`size-full ${klass ?? ''}`} type={ChartType.LINE} {group} options={echartsData} />
