import type { ChartData, ChartOptions, ChartType } from '../../dashboard.interface'
import type { Statistics } from '../../toolbox.interface'

export const commonOptions: ChartData<ChartType> = {
	backgroundColor: 'transparent',
	toolbox: {
		right: -66,
		itemGap: 20,
		feature: {
			restore: {
				show: false,
				title: 'Restore',
			},
			saveAsImage: {},
			dataView: {
				title: 'Dataview',
				readOnly: true,
				lang: ['Dataview', 'Close'],
			},
			brush: {
				show: false,
				type: ['lineX', 'clear'],
			},
			//   magicType: {
			//     type: ['line', 'bar', 'stack']
			// },
			dataZoom: {
				title: {
					zoom: 'Zoom',
				},
				yAxisIndex: false,
			},
		},
	},
	brush: {
		xAxisIndex: 'all',
		brushLink: 'all',
		brushType: 'lineX',
		outOfBrush: {
			colorAlpha: 0.1,
		},
	},
}

export function resolveCommonOptions(options: ChartOptions) {
	return {
		legend: {
			show: options.legendShow !== false,
			type: options.legendType,
			padding: [5, 76, 5, 5],
			pageIconColor: '#aaa',
			pageIconInactiveColor: '#42424200',
		},
		grid: {
			left: '0',
			right: '3%',
			bottom: options.hideAxis ? '0' : 0,
			...(options.legendShow === false && { top: '4%' }),
			containLabel: true,
			show: false,
		},
	}
}

export function resolveTooltip(
	options: ChartOptions,
	statistics?: Statistics,
	$data?: ChartData<ChartType.LINE | ChartType.BAR | ChartType.SCATTER>,
): ChartData<ChartType>['tooltip'] {
	const isPercent = $data?.isPercent !== undefined ? $data.isPercent : options.isPercent

	if (statistics?.trendLine && statistics?.showOriginalData) {
		return {
			formatter(
				params: Array<{
					seriesName: string
					value: string
					color?: string
					name: string
					marker: string
				}>,
			) {
				const stats: { [index: string]: string } = {}
				for (const param of params) {
					if (param.seriesName.indexOf('-stat') !== -1) {
						stats[param.seriesName.replace('-stat', '')] = param.value?.toLocaleString()
					}
				}

				let lines = ''
				for (const param of params) {
					if (param.seriesName.indexOf('-stat') === -1) {
						const stat =
							typeof stats[param.seriesName] !== 'undefined'
								? ` (<b style="color:${param.color};">${stats[param.seriesName]}</b>)`
								: ''
						lines += `${param.marker} <span style="display:inline-block;margin-right:18px;margin-bottom:4px;">${
							param.seriesName
						}</span> <span style="display:inline-block;float: right;"><b>${param.value?.toLocaleString()}</b>${stat}</span><br />`
					}
				}
				return `<span style="margin-bottom:4px;">${params[0].name}</span><br />${lines}`
			},
		} as ChartData<ChartType>['tooltip']
	} else
		return {
			valueFormatter: (value) => (value === undefined ? '-' : `${value}${isPercent ? '%' : ''}`),
		} as ChartData<ChartType>['tooltip']
}
