import { watch } from 'runed'

import { ChartType } from '../../dashboard.interface'
import { toolbox } from '../../toolbox.state.svelte.js'

import type { ECharts } from 'echarts/core'

export const groups: { [key: string]: { [key: string]: ECharts } } = {}

export function addChart(chart: ECharts, type: ChartType, group?: string): void {
	if (!group) return

	if (!groups[group]) {
		groups[group] = {}
	}

	chart.group = group

	attachChart(chart, type, group)

	groups[chart.group][chart.id] = chart
}

export function removeChart(chartId: string, group?: string): void {
	if (!group) return

	if (!groups[group]?.[chartId]) {
		return
	}

	clearChart(groups[group][chartId])
	delete groups[group][chartId]
}

function attachChart(chart: ECharts, type: ChartType, group: string) {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	chart.on('legendselectchanged', (params: any) => {
		chart.dispatchAction(
			{
				type: 'legendToggleSelect',
				name: `${params.name}-stat`,
			},
			true,
		)

		for (const key in groups[group]) {
			if (groups[group][key].id !== chart.id) {
				if (groups[group][key].isDisposed()) {
					delete groups[group][key]
					continue
				}

				const toChart = groups[group][key]

				toChart.dispatchAction(
					{
						type: 'legendToggleSelect',
						name: params.name,
					},
					true,
				)
				toChart.dispatchAction(
					{
						type: 'legendToggleSelect',
						name: `${params.name}-stat`,
					},
					true,
				)
			}
		}
	})

	if (type === ChartType.LINE || type === ChartType.BAR) {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		chart.on('updateAxisPointer', (params: any) => {
			if (typeof params.seriesIndex !== 'undefined') {
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				const originalDate = (chart.getOption() as any)?.xAxis[0]?.data?.[params.dataIndex]

				if (!originalDate) return

				for (const key in groups[group]) {
					if (groups[group][key].id !== chart.id) {
						if (groups[group][key].isDisposed()) {
							delete groups[chart.group][key]
							continue
						}

						// eslint-disable-next-line @typescript-eslint/no-explicit-any
						const index = (groups[chart.group][key].getOption() as any).xAxis?.[0]?.data?.findIndex(
							(el: never) => el === originalDate,
						)

						if (typeof index === 'undefined' || index === -1) {
							return
						}

						// eslint-disable-next-line @typescript-eslint/no-explicit-any
						const seriesIndex = (groups[chart.group][key].getOption() as any).series?.findIndex(
							// eslint-disable-next-line @typescript-eslint/no-explicit-any
							(s: any) => s.data[index] !== null,
						)

						groups[chart.group][key].dispatchAction(
							groups[chart.group][key].makeActionFromEvent({
								...params,
								seriesIndex,

								axesInfo: [
									// eslint-disable-next-line @typescript-eslint/no-explicit-any
									...params.axesInfo.map((ax: any) => ({
										...ax,
										value: index,
									})),
								],
								dataIndex: index,
								dataIndexInside: index,
							}),
							true,
						)
					}
				}
			} else {
				for (const key in groups[chart.group]) {
					if (groups[chart.group][key].id !== chart.id) {
						if (groups[chart.group][key].isDisposed()) {
							delete groups[chart.group][key]
							continue
						}

						groups[chart.group][key].dispatchAction(groups[chart.group][key].makeActionFromEvent(params), true)
					}
				}
			}
		})

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		chart.on('dataZoom', (params: any) => {
			toolbox.apply('ChartZoom', {
				chartId: chart.id,
				type: 'dataZoom',
				start: params.start,
				end: params.end,
				startValue: params.batch?.[0].startValue,
				endValue: params.batch?.[0].endValue,
			})
		})

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		chart.on('brush', (params: any) => {
			for (const key in groups[chart.group]) {
				if (groups[chart.group][key].id !== chart.id) {
					if (groups[chart.group][key].isDisposed()) {
						delete groups[chart.group][key]
						continue
					}

					const toChart = groups[chart.group][key]

					toChart.dispatchAction(
						{
							type: 'brush',
							areas: params.areas,
							command: params.command,
						},
						true,
					)
				}
			}
		})

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		chart.on('brushEnd', (params: any) => {
			toolbox.apply('ChartSelectAreas', {
				chartId: chart.id,
				areas: params.areas,
			})

			for (const key in groups[chart.group]) {
				if (groups[chart.group][key].id !== chart.id) {
					if (groups[chart.group][key].isDisposed()) {
						delete groups[chart.group][key]
						continue
					}

					const toChart = groups[chart.group][key]

					toChart.dispatchAction(
						{
							type: 'brushEnd',
						},
						true,
					)
				}
			}
		})
	}
}

function clearChart(chart: ECharts) {
	if (typeof chart !== 'undefined') {
		// chart.updateAxisPointer$?.unsubscribe()

		chart.off('updateAxisPointer')
		chart.off('dataZoom')
		chart.off('legendselectchanged')
		chart.off('brush')
		chart.off('brushEnd')
		chart.off('brushSelected')
		chart.off('restore')
	}
}

$effect.root(() => {
	watch(
		() => toolbox.state('Reset'),
		() => {
			toolbox.apply('ChartSelectAreaBrush', false)
			toolbox.apply('ChartZoomBrush', false)

			for (const key in groups) {
				for (const k in groups[key]) {
					if (groups[key][k].isDisposed()) {
						delete groups[key][k]
						continue
					}

					groups[key][k].dispatchAction(
						{
							type: 'restore',
						},
						true,
					)
				}
			}
		},
	)

	watch(
		() => toolbox.state('ToggleLegend'),
		() => {
			for (const key in groups) {
				for (const k in groups[key]) {
					if (groups[key][k].isDisposed()) {
						delete groups[key][k]
						continue
					}

					// eslint-disable-next-line @typescript-eslint/no-explicit-any
					const selected = (groups[key][k].getOption() as any).legend[0]?.selected

					// eslint-disable-next-line @typescript-eslint/no-explicit-any
					for (const s of (groups[key][k] as any).getModel().getSeries()) {
						if (typeof selected[s.name] === 'undefined' || selected[s.name]) {
							groups[key][k].dispatchAction(
								{
									// type: value ? 'legendSelect' : 'legendUnSelect',
									type: 'legendUnSelect',
									name: s.name,
								},
								true,
							)
						}
					}
				}
			}
		},
	)

	watch(
		() => toolbox.state('ChartSelectAreaBrush'),
		(value) => {
			if (value === 'clear') {
				toolbox.apply('ChartSelectAreas', { areas: [] })
			}

			for (const key in groups) {
				for (const k in groups[key]) {
					if (groups[key][k].isDisposed()) {
						delete groups[key][k]
						continue
					}

					if (value === 'clear') {
						groups[key][k].dispatchAction(
							{
								type: 'axisAreaSelect',
								intervals: [],
							},
							true,
						)

						groups[key][k].dispatchAction(
							{
								type: 'brush',
								command: 'clear',
								areas: [],
							},
							true,
						)

						groups[key][k].setOption({
							legend: {
								formatter: null,
							},
							title: {
								show: false,
							},
						})
					} else {
						// eslint-disable-next-line @typescript-eslint/no-explicit-any
						const brush = (groups[key][k].getOption() as any).brush[0]
						groups[key][k].dispatchAction(
							{
								type: 'takeGlobalCursor',
								key: 'brush',
								brushOption: {
									// brushType: type === 'keep' ? brush.brushType : brush.brushType === type ? false : type,
									brushType: value === true ? brush.brushType : false,
									// brushMode: brush.brushMode,
								},
							},
							true,
						)
					}
				}
			}
		},
	)

	watch(
		() => toolbox.state('ChartZoomBrush'),
		(value) => {
			if (value === 'clear') {
				toolbox.apply('ChartZoom', {
					type: 'dataZoom',
					start: 0,
					end: 100,
				})
			}

			for (const key in groups) {
				for (const k in groups[key]) {
					if (groups[key][k].isDisposed()) {
						delete groups[key][k]
						continue
					}

					if (value === 'clear') {
						groups[key][k].dispatchAction(
							{
								type: 'takeGlobalCursor',
								key: 'dataZoomSelect',
								dataZoomSelectActive: false,
							},
							true,
						)
					} else {
						groups[key][k].dispatchAction(
							{
								type: 'takeGlobalCursor',
								key: 'dataZoomSelect',
								dataZoomSelectActive: value,
							},
							true,
						)
					}
				}
			}
		},
	)

	watch(
		() => toolbox.state('ChartZoom'),
		(value) => {
			if (value === undefined) return

			if (!value.chartId) {
				for (const key in groups) {
					for (const k in groups[key]) {
						if (groups[key][k].isDisposed()) {
							delete groups[key][k]
							continue
						}

						groups[key][k].dispatchAction(
							{
								type: 'dataZoom',
								start: value.start,
								end: value.end,
								startValue: value.startValue,
								endValue: value.endValue,
							},
							true,
						)
					}
				}

				return
			}

			let chart

			outer_loop: for (const key in groups) {
				for (const k in groups[key]) {
					if (groups[key][k].id !== value.chartId) {
						chart = groups[key][k]
						break outer_loop
					}
				}
			}

			if (!chart) {
				return
			}

			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			const xAxis = (chart.getOption() as any).xAxis[0]
			let startValue = value.startValue
			let endValue = value.endValue

			if (typeof startValue === 'undefined') {
				startValue = Math.round((value.start * (xAxis.data.length - 1)) / 100) || 0
			}
			if (typeof endValue === 'undefined') {
				endValue = Math.round((value.end * (xAxis.data.length - 1)) / 100) || xAxis.data.length - 1
			}

			const startDate = xAxis.data[startValue]
			const endDate = xAxis.data[endValue]

			for (const key in groups) {
				for (const k in groups[key]) {
					if (groups[key][k].isDisposed()) {
						delete groups[key][k]
						continue
					}

					groups[key][k].dispatchAction(value, true)
					// if (groups[key][k].id !== value.chartId) {
					// eslint-disable-next-line @typescript-eslint/no-explicit-any
					const toChartXAxis = (groups[key][k].getOption() as any).xAxis[0]

					// eslint-disable-next-line @typescript-eslint/no-explicit-any
					const toChartStartIndex = toChartXAxis.data.findIndex((el: any) => el === startDate)
					// eslint-disable-next-line @typescript-eslint/no-explicit-any
					const toChartEndIndex = toChartXAxis.data.findIndex((el: any) => el === endDate)

					groups[key][k].dispatchAction(
						{
							type: 'dataZoom',
							startValue: toChartStartIndex !== -1 ? toChartStartIndex : 0,
							endValue: toChartEndIndex !== -1 ? toChartEndIndex : toChartXAxis.data.length - 1,
						},
						true,
					)
					// }
				}
			}
		},
	)
})
