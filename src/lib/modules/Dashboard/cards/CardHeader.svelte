<script lang="ts">
	import { Avatar, ProgressRing } from '@skeletonlabs/skeleton-svelte'

	import type { Component } from 'svelte'

	let {
		class: klass,
		title = undefined,
		description = undefined,
		prefix = undefined,
		avatarName = undefined,
		avatarIcon = undefined,
		avatarUrl = undefined,
		isLoading = $bindable(false),
	}: {
		class?: string
		title?: string
		description?: string
		prefix?: string
		avatarName?: string
		avatarIcon?: Component
		avatarUrl?: string
		isLoading: boolean
	} = $props()
</script>

<header class={`flex flex-row justify-between ${klass ?? ''}`}>
	<div class="flex flex-row space-x-2">
		{#if prefix}
			<h3 class="h3">{prefix}</h3>
		{:else if avatarIcon}
			{@const Component = avatarIcon}
			<button type="button" class="preset-filled btn-icon">
				<Component />
			</button>
		{:else if avatarUrl || avatarName}
			<Avatar src={avatarUrl} name={avatarName || ''} size="size-9" />
		{/if}

		<div>
			{#if title}
				<h1 class="text-surface-400 truncate text-sm font-medium capitalize" data-toc-ignore>{title}</h1>
			{/if}

			{#if description}
				<!-- eslint-disable-next-line svelte/no-at-html-tags -->
				<p class="text-surface-400 truncate text-xs font-light uppercase">{@html description}</p>
			{/if}
		</div>
	</div>

	{#if isLoading}
		<ProgressRing
			value={null}
			classes="ml-2"
			size="size-5"
			meterStroke="stroke-secondary-500"
			trackStroke="stroke-secondary-500/30"
		/>
	{/if}
</header>
