<script lang="ts">
	const {
		title,
		subTitle,
		description,

		background = 'bg-noise bg-surface-50 dark:bg-surface-900 border border-surface-100 dark:border-surface-900',
		rounded = 'rounded-md',
		padding = '',
	}: {
		title?: string
		subTitle?: string
		description?: string

		background?: string
		rounded?: string
		padding?: string
	} = $props()
</script>

<div class="card {background} {rounded} {padding}">
	<div class="space-y-4 p-4">
		{#if title}
			<h3 class="h3" data-toc-ignore>{title}</h3>
		{/if}

		{#if subTitle}
			<h6 class="h6" data-toc-ignore>{subTitle}</h6>
		{/if}

		{#if description}
			<p>{@html description}</p>
		{/if}
	</div>
</div>
