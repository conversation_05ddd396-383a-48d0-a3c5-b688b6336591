<script lang="ts">
	import { Progress } from '@skeletonlabs/skeleton-svelte'

	import { getLocaleForDateFormat } from '$lib/utils'

	import CardHeader from './CardHeader.svelte'
	import {
		ChartType,
		commonOptions,
		resolveGetter,
		type CardData,
		type ChartData,
		type HeaderOptions,
		type NumberData,
		type NumberOptions,
	} from '../dashboard.interface'
	import EChart from './charts/EChart.svelte'

	const {
		getter,
		options = undefined,
		header = undefined,

		background = 'bg-noise bg-surface-50 dark:bg-surface-900 border border-surface-100 dark:border-surface-900',
		rounded = 'rounded-md',
		padding = 'px-8 py-7',
	}: {
		getter: CardData<NumberData> | Promise<CardData<NumberData>>
		options?: NumberOptions
		header?: HeaderOptions

		background?: string
		rounded?: string
		padding?: string
	} = $props()

	function formatValue(value?: number | string | null, format = 2) {
		if (!value) return value

		const val = Number(value)

		if (!val) {
			return val
		}

		const incPrefixes = ['k', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y']
		const decPrefixes = ['m', '\u03bc', 'n', 'p', 'f', 'a', 'z', 'y']

		const degree = Math.floor(Math.log10(Math.abs(val)) / 3)
		const scaled = val * Math.pow(1000, -degree)

		let prefix = null
		switch (Math.sign(degree)) {
			case 1:
				prefix = incPrefixes[degree - 1]
				break
			case -1:
				prefix = decPrefixes[-degree - 1]
				break
		}

		return `${scaled.toLocaleString(getLocaleForDateFormat(), { maximumFractionDigits: format })}${prefix || ''}`
	}

	function calcObjective(data: NumberData) {
		return ((data.value || 1) / (data.objective || 1)) * 100
	}

	function parseVariation(variation: number) {
		if (variation === Infinity) {
			return ''
		}

		return Math.round(Math.abs(variation) * 100) / 100
	}

	function parseVariationSymbol(variation: number, inverted = false) {
		if (!variation || variation === Infinity) {
			return '&infin;'
		}

		if (inverted) {
			return variation < 0 ? '&#x25B2; ' : '&#x25BC; '
		}

		return variation > 0 ? '&#x25B2; ' : '&#x25BC; '
	}

	function resolveOptions(options: ChartData<ChartType>): ChartData<ChartType> {
		return {
			...options,
			...commonOptions,
			xAxis: {
				...options.xAxis,
				boundaryGap: false,
				show: false,
			},
			yAxis: {
				...options.yAxis,
				boundaryGap: false,
				show: false,
			},
			series: options.series?.map((s) => ({
				...s,
				// data: [1, 2, 3, 3, 4, 4, 2, 2, 3, 2, 3, 4, 4],
				smooth: true,
				areaStyle: {
					opacity: 0.7,
				},
				...(s.type === 'line'
					? {
							lineStyle: {
								width: 0,
							},
							showSymbol: false,
							emphasis: {
								focus: 'series',
							},
						}
					: {}),
			})) as never,
		}
	}
</script>

<div class="card relative flex flex-col overflow-hidden {background} {rounded} {padding}">
	{#await resolveGetter<NumberData>(getter)}
		<section class="flex h-full items-center justify-center self-center p-4 text-3xl">
			<span class="text-surface-300 dark:text-surface-500 text-3xl" style="margin-top: -2rem;">Loading...</span>
		</section>
	{:then getter}
		{#if header}
			<CardHeader
				class="relative z-10 w-fit"
				title={header.title}
				description={header.description}
				prefix={header.prefix}
				avatarUrl={header.avatarUrl}
				avatarName={header.avatarName}
				avatarIcon={header.avatarIcon}
				isLoading={getter.isLoading}
			/>
		{/if}

		{#await getter.data}
			<section class="flex h-full items-center justify-center self-center p-4 text-3xl">
				<span class="text-surface-300 dark:text-surface-500 text-3xl" style="margin-top: -2rem;">Loading...</span>
			</section>
		{:then data}
			{#if data && data.chart}
				<!-- <div
			echarts
			[options]="options | async"
			class="chart"
			[theme]="theme"
			(chartInit)="onChartInitialized()"
		></div> -->
				<EChart class="absolute inset-0 w-full" type={ChartType.LINE} options={resolveOptions(data.chart)} />
			{/if}

			{#if !data}
				<section class="pt-4">
					<span>No Data Available</span>
				</section>
			{:else}
				<section class="relative z-10 mt-1 w-fit">
					<span class="values">
						<span class="text-3xl font-semibold tracking-tight">
							{formatValue(data.value)}

							{#if data.valueMetric || options?.valueMetric}
								<span>{data.valueMetric || options?.valueMetric}</span>
							{/if}
						</span>

						{#if data.variation}
							<span
								class="ml-2 text-sm font-semibold {(options?.invertVariation ? data.variation > 0 : data.variation < 0)
									? 'text-primary-500'
									: 'text-success-700'}"
							>
								<!-- eslint-disable-next-line svelte/no-at-html-tags -->
								<span>{@html parseVariationSymbol(data.variation, options?.invertVariation)}</span>
								{parseVariation(data.variation)}%
							</span>
						{/if}
					</span>
				</section>

				{#if data.objective}
					<div class="absolute inset-x-0 bottom-0 h-auto">
						<Progress labelText="Objective Progress" value={calcObjective(data)} max={100} />
					</div>
				{/if}
			{/if}
		{/await}
	{/await}
</div>
