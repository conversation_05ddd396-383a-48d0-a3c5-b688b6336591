<script lang="ts">
	import AnalyticsToolbox from './AnalyticsToolbox.svelte'
	import { toolbox } from './toolbox.state.svelte.js'
	import { getSideBar } from '../Dashboard/Dashboard.svelte'

	const sideBar = getSideBar()

	const { ...rest } = $props()

	let open: boolean = $state(false)

	const chartZoomIsDirty = $derived(toolbox.state('ChartZoom')?.start !== 0 || toolbox.state('ChartZoom')?.end !== 100)
	const chartSelectAreaIsDirty = $derived(toolbox.state('ChartSelectAreas')?.areas?.length > 0)

	sideBar.register('analysis-toolbox', AnalyticsToolbox, {})
</script>

<div {...rest}>
	<button class="btn-icon" onclick={() => (open = !open)} title="Toolbox">
		{#if open}
			<iconify-icon icon="ic:round-arrow-forward-ios" width="24" height="24"></iconify-icon>
		{:else}
			<iconify-icon icon="ic:round-construction" width="24" height="24"></iconify-icon>
		{/if}
	</button>

	{#if open}
		<div class="preset-tonal flex flex-row rounded-sm px-2">
			<button class="btn-icon" onclick={() => toolbox.reset()} title="Restore" aria-label="Restore">
				<iconify-icon icon="ic:round-restore" width="24" height="24"></iconify-icon>
			</button>

			<button
				class="btn-icon"
				disabled={!toolbox.canUndo}
				onclick={() => toolbox.undo()}
				title="Undo"
				aria-label="Undo"
			>
				<iconify-icon icon="ic:round-undo" width="24" height="24"></iconify-icon>
			</button>

			<button
				class="btn-icon"
				disabled={!toolbox.canRedo}
				onclick={() => toolbox.redo()}
				title="Redo"
				aria-label="Redo"
			>
				<iconify-icon icon="ic:round-redo" width="24" height="24"></iconify-icon>
			</button>

			<button
				class={`btn-icon ${toolbox.state('ChartZoomBrush') === true ? 'text-secondary-500' : ''}`}
				onclick={() =>
					toolbox.apply(
						'ChartZoomBrush',
						toolbox.state('ChartZoomBrush') === 'clear' ? true : !toolbox.state('ChartZoomBrush'),
					)}
				title="Zoom"
				aria-label="Zoom"
			>
				<iconify-icon icon="ic:round-zoom-out-map" width="24" height="24"></iconify-icon>
			</button>

			{#if chartZoomIsDirty}
				<button
					class="btn-icon text-primary-500"
					onclick={() => toolbox.apply('ChartZoomBrush', 'clear')}
					title="Clear Zoom"
					aria-label="Clear Zoom"
				>
					<iconify-icon icon="ic:round-clear" width="24" height="24"></iconify-icon>
				</button>
			{/if}

			<button
				class={`btn-icon ${toolbox.state('ChartSelectAreaBrush') === true ? 'text-secondary-500' : ''}`}
				onclick={() =>
					toolbox.apply(
						'ChartSelectAreaBrush',
						toolbox.state('ChartSelectAreaBrush') === 'clear' ? true : !toolbox.state('ChartSelectAreaBrush'),
					)}
				title="Select Area"
				aria-label="Select Area"
			>
				<iconify-icon icon="ic:round-crop" width="24" height="24"></iconify-icon>
			</button>

			{#if chartSelectAreaIsDirty}
				<button
					class="btn-icon text-primary-500"
					onclick={() => toolbox.apply('ChartSelectAreaBrush', 'clear')}
					title="Clear Area"
					aria-label="Clear Area"
				>
					<iconify-icon icon="ic:round-clear" width="24" height="24"></iconify-icon>
				</button>
			{/if}

			<!-- <button
			class={`btn-icon ${arranging ? 'text-primary-500' : ''}`}
			onclick={() => toolbox.toggle('Arranging')}
			title="Drag & Drop"
			aria-label="Drag & Drop"
		>
			<iconify-icon icon="ic:round-view-quilt" width="24" height="24" />
		</button> -->

			<button
				class={`btn-icon ${toolbox.state('ShowChartGridLines') ? 'text-primary-500' : ''}`}
				onclick={() => toolbox.toggle('ShowChartGridLines')}
				title="Show Chart Grid Lines"
				aria-label="Show Chart Grid Lines"
			>
				<iconify-icon icon="ic:round-grid-goldenratio" width="24" height="24"></iconify-icon>
			</button>

			<button
				class="btn-icon"
				onclick={() => toolbox.toggle('ToggleLegend')}
				title="Clear Legend Selection"
				aria-label="Clear Legend Selection"
			>
				<iconify-icon icon="ic:round-layers-clear" width="24" height="24"></iconify-icon>
			</button>
		</div>
	{/if}

	{#if toolbox.chartTypes.length}
		<button
			class={`btn-icon ${sideBar.state?.id === 'analysis-toolbox' ? 'text-primary-500' : ''}`}
			onclick={() => sideBar.toggle('analysis-toolbox')}
			title="Analysis Toolbox"
			aria-label="Analysis Toolbox"
		>
			<iconify-icon icon="ic:round-insights" width="24" height="24"></iconify-icon>
		</button>
	{/if}
</div>
