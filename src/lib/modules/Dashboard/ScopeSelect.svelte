<script lang="ts">
	import { scopeMap } from '$lib/utils/entity-stats'

	import type { ScopeOptions } from './dashboard.interface'

	let {
		class: klass,
		options = [
			'currentMonth',
			'currentYear',
			'lastYear',
			'currentQuarter',
			'lastQuarter',
			'lastMonth',
			'last30Days',
			'last14Days',
			'last7Days',
		],
		defaultValue = 'currentMonth',
		selected = $bindable(defaultValue),
		hide = false,
	}: {
		class?: string
		options?: ScopeOptions[]
		defaultValue?: ScopeOptions
		selected?: ScopeOptions
		hide?: boolean
	} = $props()

	const scopes = $derived(
		Object.keys(scopeMap)
			.filter((scope) => options.includes(scope as ScopeOptions))
			.map((scope) => {
				return {
					value: scope,
					title: scopeMap[scope as ScopeOptions],
				}
			}),
	)
</script>

{#if !hide}
	<label class="label flex flex-col">
		<span class="text-xs">Scope</span>

		<select class={`select ${klass ?? ''}`} bind:value={selected}>
			{#each scopes as scope (scope.value)}
				<option value={scope.value}>{scope.title}</option>
			{/each}
		</select>
	</label>
{/if}
