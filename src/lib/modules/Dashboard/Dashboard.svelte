<script lang="ts" module>
	import { type ComponentProps, type Component, type Snippet, getContext, setContext } from 'svelte'

	export interface SideBarComponent<C extends Component = Component> {
		id: string
		component: C
		props: ComponentProps<C>
	}

	class SideBar {
		state = $state<SideBarComponent | undefined>()

		private sidebarRegistry: Record<string, SideBarComponent> = {}

		register<C extends Component>(id: string, component: C, props: ComponentProps<C>) {
			this.sidebarRegistry[id] = {
				id,
				component,
				props,
			}
		}

		open(id: string) {
			if (this.sidebarRegistry[id]) {
				this.state = this.sidebarRegistry[id]
			}
		}

		close() {
			this.state = undefined
		}

		toggle(id: string) {
			if (this.state?.id === id) {
				this.state = undefined
			}

			this.state = this.sidebarRegistry[id]
		}
	}

	const DEFAULT_KEY = Symbol()

	export function initSideBar(key = DEFAULT_KEY) {
		return setContext(key, new SideBar())
	}

	export function getSideBar(key = DEFAULT_KEY) {
		return getContext<SideBar>(key)
	}
</script>

<script lang="ts">
	import Chart from './cards/Chart.svelte'
	import Number from './cards/Number.svelte'
	import Title from './cards/Title.svelte'
	import {
		CardType,
		type ChartCard,
		type ChartType,
		type DashboardCard,
		type DashboardOptions,
		type ScopeOptions,
	} from './dashboard.interface'
	import ScopeSelect from './ScopeSelect.svelte'
	import { toolbox, whitelistChartTypes } from './toolbox.state.svelte.js'
	import Toolbox from './Toolbox.svelte'

	import type { DocumentNode } from '$lib/utils'

	const defaultConfig: DashboardOptions = {
		scope: {
			options: [
				'currentMonth',
				'currentYear',
				'lastYear',
				'currentQuarter',
				'lastQuarter',
				'lastMonth',
				'last30Days',
				'last14Days',
				'last7Days',
			],
			default: 'currentMonth',
		},
		showScopeSelect: false,
		showToolbox: false,
	}

	let {
		cards = [],
		config = defaultConfig,
		filter = $bindable({}),
		scope = $bindable(),

		filterSlot,
		scopeSlot,
		header,
		children,
	}: {
		cards?: DashboardCard[]
		config?: DashboardOptions
		filter?: DocumentNode
		scope?: ScopeOptions

		filterSlot?: Snippet
		scopeSlot?: Snippet
		header?: Snippet
		children?: Snippet
	} = $props()

	initSideBar()

	const dashboardConfig = $derived({
		...defaultConfig,
		...(config ?? {}),
		scope: {
			...defaultConfig.scope,
			...(config ?? {}).scope,
		},
	})

	function hideExpressions(filter: DocumentNode, hide?: string | ((filter: DocumentNode) => boolean) | boolean) {
		if (!filter) return true

		try {
			switch (typeof hide) {
				case 'undefined':
					return false
				case 'boolean':
					return hide
				case 'string':
					return Function('filter', `return ${hide};`)(filter)
				case 'function':
					return hide(filter)
				default:
					return false
			}
		} catch (error) {
			console.error('Error resolving hide expression', error)
			return false
		}
	}

	const foundTypes = $derived(
		cards.filter((card) => card.type === CardType.CHART).map((card) => (card as ChartCard<ChartType>).chartType),
	)

	$effect(() => {
		toolbox.chartTypes = [...new Set(foundTypes)].filter((chartTypes) => whitelistChartTypes.includes(chartTypes))
	})
</script>

<div class="max-w-full">
	{#if dashboardConfig.showToolbox || dashboardConfig.showScopeSelect}
		<div class="flex flex-row justify-between p-4">
			{#if filterSlot}
				<div class="flex flex-row gap-4">
					{@render filterSlot()}
				</div>
			{/if}

			<div class="flex-auto"></div>

			<div class="flex flex-row gap-4">
				{#if dashboardConfig.showToolbox}
					<Toolbox />
				{/if}

				{#if scopeSlot}
					{@render scopeSlot?.()}
				{:else}
					<ScopeSelect
						class="w-auto"
						options={dashboardConfig.scope.options}
						defaultValue={dashboardConfig.scope?.default}
						hide={!dashboardConfig.showScopeSelect}
						bind:selected={scope}
					/>
				{/if}
			</div>
		</div>
	{/if}

	{@render header?.()}
	<div class="grid-layout">
		{#if children}
			{@render children()}
		{:else}
			{#each cards as card (card)}
				{#if !hideExpressions(filter, card.hide)}
					{#if card.type === CardType.BREAK}
						<div
							class={`grid-item basis-full ${card.border ? (card.border === true ? 'border-surface-100 dark:border-surface-800 border-b' : card.border) : 'm-0!'} ${card.preWrap && 'col-wrap'}`}
						></div>
					{:else if card.type === CardType.TITLE}
						<div class={`grid-item ${card.class ?? ''}`}>
							<Title {...card} />
						</div>
					{:else if card.type === CardType.NUMBER}
						<div class={`grid-item ${card.class ?? ''}`}>
							<Number {...card} />
						</div>
					{:else if card.type === CardType.CHART}
						<div class={`grid-item ${card.class ?? ''}`}>
							<Chart
								header={card.header}
								getter={card.getter}
								type={card.chartType}
								options={card.options}
								group={card.group}
							/>
						</div>
					{:else if card.type === CardType.CUSTOM}
						<div class={`grid-item ${card.class ?? ''}`}>
							<card.component {...card}></card.component>
						</div>
					{/if}
				{/if}
			{/each}
		{/if}
	</div>
</div>

<style>
	.grid-layout {
		display: flex;
		flex-wrap: wrap;
		padding: 1rem 1rem 0 1rem;
		column-gap: 1rem;
	}

	.grid-item {
		flex-grow: 1;
		min-width: 450px;
		margin-bottom: 1rem;
	}

	@media (max-width: 920px) {
		.grid-item {
			min-width: max(450px, 33.333%);
		}
	}

	@media (max-width: 540px) {
		.grid-item {
			min-width: calc(100% - 0.5rem);
		}
	}

	.full-width {
		width: 100%;
	}

	.half-width {
		width: calc(50% - 1rem / 2);
	}

	.clean {
		width: 100%;
	}

	.col-10 {
		width: calc(10% - 1rem / 10);
	}

	.col-20 {
		width: calc(20% - 1rem / 5);
	}

	.col-25 {
		width: calc(25% - 1rem / 4);
	}

	.col-30 {
		width: calc(30% - 1rem / 3.333);
	}

	.col-33 {
		width: calc(33.333% - 1rem / 3);
	}

	.col-40 {
		width: calc(40% - 1rem / 2.5);
	}

	.col-50 {
		width: calc(50% - 1rem / 2);
	}

	.col-60 {
		width: calc(60% - 1rem / 1.666);
	}

	.col-70 {
		width: calc(70% - 1rem / 1.4285);
	}

	.col-80 {
		width: calc(80% - 1rem / 1.25);
	}

	.col-90 {
		width: calc(90% - 1rem / 1.111);
	}

	.col-100 {
		width: 100%;
	}

	:has(:global(+ .col-wrap)) {
		flex-grow: 0;
	}

	.row-1 {
		min-height: 75px;
	}

	.row-2 {
		min-height: 150px;
	}

	.row-3 {
		min-height: 300px;
	}

	.row-4 {
		min-height: 450px;
	}

	.row-5 {
		min-height: 600px;
	}

	.row-6 {
		min-height: 850px;
	}

	.empty {
		height: 0 !important;
		min-height: 0 !important;
		visibility: hidden;
		margin-top: 0 !important;
		margin-bottom: 0 !important;
		padding: 0 !important;
	}
</style>
