import { ChartType } from './dashboard.interface'

import type {
	Command,
	DataForCommand,
	Statistics,
	TogglebleCommand,
	ToolboxEvent,
	ToolboxState,
} from './toolbox.interface'

const HISTORY_SIZE = 10
export const whitelistChartTypes = [ChartType.LINE, ChartType.BOXPLOT]

export const defaultTools = {
	showGrid: false,
	toggleLegend: false,
	statistics: {
		trendLine: false,
		regression: 'linear',
		polynomialDegree: 2,
		showOriginalData: true,
		logScale: false,
		boundIQR: 1.5,
		boxPlotShowOutliers: true,
	} as Statistics,
	selectAreas: [],
	zoom: {
		type: 'x',
		start: 0,
		end: 1,
	},
}

export interface CommandData<T extends Command> {
	undoable: boolean
	initialValue?: DataForCommand[T]
	state?: DataForCommand[T]
}

export type CommandsMap<T extends Command> = {
	[index in T]?: CommandData<T>
}

class ToolBox {
	// State
	commands = $state<CommandsMap<Command>>({})

	canUndo = $state(false)
	canRedo = $state(false)

	chartTypes = $state<string[]>([])

	history: ToolboxState = {
		past: [],
		future: [],
	}

	constructor() {
		this.register('Reset', false, false)
		this.register('ShowChartGridLines', false, false)
		this.register('ToggleLegend', false, false)
		this.register('AnalysisTools', false, defaultTools.statistics)
		this.register('ChartSelectAreas', true, { areas: [] })
		this.register('ChartZoom', true, {
			type: 'datazoom',
			start: 0,
			end: 100,
		})

		this.register('CardDrop', false, false)
		this.register('CardDropReset', false, false)

		this.register('ChartZoomBrush', false, false)
		this.register('ChartSelectAreaBrush', false, false)
		this.register('Arranging', false, false)
	}

	register = <T extends Command>(command: T, undoable = true, initialValue?: DataForCommand[T]) => {
		if (this.commands[command] !== undefined) {
			return this.commands[command]!.state as DataForCommand[T]
		}

		this.commands[command] = {
			undoable,
			initialValue,
			state: undefined,
		}

		if (initialValue !== undefined) {
			this.apply(command, initialValue, false)
		}

		return this.commands[command]!.state as DataForCommand[T]
	}

	destroy = (command: Command) => {
		delete this.commands[command]
	}

	apply = <T extends Command>(command: T, event: DataForCommand[T], undoable = true) => {
		if (!this.commands[command]) {
			return
		}

		if (!undoable || (this.commands[command] && !this.commands[command]?.undoable)) {
			this.commands[command].state = event
		}

		this.history.past.push({ command, event })

		if (this.history.past.length > HISTORY_SIZE) {
			this.history.past.shift()
		}

		if (this.history.future.length) {
			this.history.future = []
		}

		this.canUndo = !!this.history.past.length
		this.canRedo = !!this.history.future.length

		this.commands[command].state = event
	}

	state = <T extends Command>(command: T) => {
		return this.commands[command]?.state as DataForCommand[T]
	}

	toggle = (command: TogglebleCommand) => {
		if (!this.commands[command]) {
			return
		}

		this.apply(command, !this.commands[command].state)
	}

	undo = () => {
		if (this.history.past.length) {
			this.history.future.push(this.history.past.pop() as ToolboxEvent<Command>)

			this.canUndo = !!this.history.past.length
			this.canRedo = !!this.history.future.length

			if (typeof this.history.past[this.history.past.length - 1] === 'undefined') {
				this.resetToInitialValues()
				return
			}

			const last = this.history.past[this.history.past.length - 1]

			if (typeof this.commands[last.command] !== 'undefined') {
				this.commands[last.command]!.state = last.event
			}
		}
	}

	redo = () => {
		if (this.history.future.length) {
			this.history.past.push(this.history.future.pop() as ToolboxEvent<Command>)

			this.canUndo = !!this.history.past.length
			this.canRedo = !!this.history.future.length

			const last = this.history.past[this.history.past.length - 1]
			if (typeof this.commands[last.command] !== 'undefined') {
				this.commands[last.command]!.state = last.event
			}
		}
	}

	reset = () => {
		this.history.past = []
		this.history.future = []

		this.canUndo = false
		this.canRedo = false

		this.commands['Reset']!.state = true
		this.resetToInitialValues()
	}

	private resetToInitialValues() {
		for (const key in this.commands) {
			if (this.commands[key as Command]?.undoable && this.commands[key as Command]?.initialValue) {
				this.apply(key as Command, this.commands[key as Command]!.initialValue as never, false)
			}
		}
	}
}

export const toolbox = new ToolBox()
