<script lang="ts">
	import { ChartType } from './dashboard.interface'
	import { toolbox } from './toolbox.state.svelte.js'
	import { FieldType, type Form } from '../Form/form.interface'
	import FormBuilder from '../Form/FormBuilder.svelte'

	import type { Statistics } from './toolbox.interface'

	function onFilter(data: Statistics) {
		if (JSON.stringify(toolbox.state('AnalysisTools')) !== JSON.stringify(data)) {
			toolbox.apply('AnalysisTools', data)
		}
	}

	const {
		trendLine,
		regression,
		polynomialDegree,
		showOriginalData,
		logScale,
		boundIQR,
		boxPlotMinFilter,
		boxPlotMaxFilter,
		boxPlotShowOutliers,
	} = toolbox.state('AnalysisTools')

	const fields = $derived.by(
		() =>
			[
				{
					key: 'break',
					type: FieldType.BREAK,
					props: {
						label: 'Line Chart',
					},
					hide: () => !toolbox.chartTypes.includes(ChartType.LINE),
				},
				{
					key: 'trendLine',
					type: FieldType.INPUT,
					props: {
						type: 'checkbox',
						label: 'Trend Line',
						default: trendLine,
					},
					hide: () => !toolbox.chartTypes.includes(ChartType.LINE),
				},
				{
					key: 'regression',
					type: FieldType.SELECT,
					props: {
						label: 'Type',
						placeholder: 'Select Regression',
						options: [
							{ value: 'linear', label: 'Linear' },
							{ value: 'exponential', label: 'Exponential' },
							{ value: 'polynomial', label: 'Polynomial' },
							{ value: 'logarithmic', label: 'Logarithmic' },
							// { value: 'power-series', label: 'Power Series', disabled: true },
							// { value: 'moving-average', label: 'Moving Average', disabled: true },
						],
						default: regression,
					},
					hide: (filter) => !toolbox.chartTypes.includes(ChartType.LINE) || !filter.trendLine,
				},
				{
					key: 'polynomialDegree',
					type: FieldType.SELECT,
					props: {
						label: 'Polynomial Degree',
						placeholder: 'Select Regression',
						options: [
							{ value: 2, label: '2' },
							{ value: 3, label: '3' },
							{ value: 4, label: '4' },
							{ value: 6, label: '6' },
							{ value: 8, label: '8' },
							{ value: 10, label: '10' },
						],
						default: polynomialDegree,
					},
					hide: (filter) =>
						!toolbox.chartTypes.includes(ChartType.LINE) || !filter.trendLine || filter.regression !== 'polynomial',
				},
				// {
				// 	key: 'showRSquare',
				// 	type: FieldType.INPUT,
				// 	props: {
				// 		type: 'checkbox',
				// 		label: 'Show R2',
				// 		color: 'primary',
				// 		default: showRSquare
				// 	},
				// 	hide: (filter) => !filter.trendLine
				// },
				{
					key: 'showOriginalData',
					type: FieldType.INPUT,
					props: {
						type: 'checkbox',
						label: 'Show Original data',
						default: showOriginalData,
					},
					hide: (filter) => !toolbox.chartTypes.includes(ChartType.LINE) || !filter.trendLine,
				},
				{
					key: 'logScale',
					type: FieldType.INPUT,
					props: {
						type: 'checkbox',
						label: 'Logarithmic Scale',
						default: logScale,
					},
					hide: () => !toolbox.chartTypes.includes(ChartType.LINE),
				},
				{
					key: 'break',
					type: FieldType.BREAK,
					props: {
						label: 'Box Plot',
					},
					hide: () => !toolbox.chartTypes.includes(ChartType.BOXPLOT),
				},
				{
					key: 'boundIQR',
					type: FieldType.INPUT,
					props: {
						type: 'number',
						label: 'BoundIQR',
						description: 'Data less than min bound is outlier. Q1 - 1.5 * (Q3 - Q1)',
						default: boundIQR,
					},
					hide: () => !toolbox.chartTypes.includes(ChartType.BOXPLOT),
				},
				{
					key: 'boxPlotMinFilter',
					type: FieldType.INPUT,
					props: {
						type: 'number',
						label: 'Minimum filter',
						description: 'Filter out all values lower than.',
						default: boxPlotMinFilter,
					},
					hide: () => !toolbox.chartTypes.includes(ChartType.BOXPLOT),
				},
				{
					key: 'boxPlotMaxFilter',
					type: FieldType.INPUT,
					props: {
						type: 'number',
						label: 'Maximum Filter',
						description: 'Filter out all values higher than.',
						default: boxPlotMaxFilter,
					},
					hide: () => !toolbox.chartTypes.includes(ChartType.BOXPLOT),
				},
				{
					key: 'boxPlotShowOutliers',
					type: FieldType.INPUT,
					props: {
						type: 'checkbox',
						label: 'Show Outliers',
						default: boxPlotShowOutliers,
					},
					hide: () => !toolbox.chartTypes.includes(ChartType.BOXPLOT),
				},
			] satisfies Form<Statistics>,
	)
</script>

{#if toolbox.chartTypes.length}
	<div class="mt-1">
		<FormBuilder {fields} onValueChange={onFilter} />
	</div>
{/if}
