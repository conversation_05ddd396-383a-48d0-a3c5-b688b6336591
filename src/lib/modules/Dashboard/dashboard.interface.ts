import type {
	<PERSON><PERSON><PERSON>Option,
	Box<PERSON><PERSON><PERSON>eriesOption,
	EChartsOption,
	FunnelSeriesOption,
	GaugeSeriesOption,
	GraphSeriesOption,
	LegendComponentOption,
	LineSeriesOption,
	MapSeriesOption,
	ParallelSeriesOption,
	PieSeriesOption,
	RadarSeriesOption,
	SankeySeriesOption,
	ScatterSeriesOption,
	TreemapSeriesOption,
	TreeSeriesOption,
} from 'echarts'
import type { ComponentProps, Component } from 'svelte'

// export type CardType = 'number' | 'line' | 'map' | 'empty' | 'pie' | 'box-plot' | 'stack-horizontal-bar' | 'stats' | 'sidenav-stats'
export enum CardType {
	NUMBER = 'number',
	CHART = 'chart',
	BREAK = 'break',
	TITLE = 'title',
	CUSTOM = 'custom',
}

export interface CoreDashboardCard {
	id?: string
	class?: string

	hide?: string | boolean | (() => boolean)
}

export interface HeaderOptions {
	title?: string
	description?: string

	prefix?: string

	avatarUrl?: string
	avatarName?: string
	avatarIcon?: Component
}

export interface CardDataWithLoading<Data> {
	isLoading: boolean
	data: Data | Promise<Data>
}

export type CardData<Data = unknown> = Data | CardDataWithLoading<Data> | Promise<Data | CardDataWithLoading<Data>>

export interface BaseDashboardCard extends CoreDashboardCard {
	group?: string

	header?: HeaderOptions

	background?: string
	rounded?: string
	padding?: string
}

// export interface BaseFilledDashboardCard {
//   changed: Readable<boolean>
//   set: <T extends keyof Omit<FilledDashboardCard, 'changed' | 'set'>>(
//     field: T,
//     value: FilledDashboardCard[T]
//   ) => void
// }

// BREAK

export interface Break extends Omit<CoreDashboardCard, 'header'> {
	type: CardType.BREAK
	border?: boolean | string
	preWrap?: boolean
}

// export interface FilledBreak extends Break {
//   id: string
// }

// TITLE

export interface Title extends Omit<CoreDashboardCard, 'header'> {
	type: CardType.TITLE
	title?: string
	subTitle?: string
	description?: string
}

// export interface FilledTitle extends Title {
//   id: string
// }

// NUMBER

export interface NumberOptions {
	color?: string
	backgroundColor?: string
	objectiveColor?: string
	valueMetric?: string
	invertVariation?: boolean
}

export interface NumberData {
	value?: number | null
	valueMetric?: string
	variation?: number | null
	objective?: number
	chart?: ChartData<ChartType>
}

export interface CardNumber extends BaseDashboardCard {
	type: CardType.NUMBER
	getter: CardData<NumberData>
	options?: NumberOptions
	// getData: (
	//   dashboard: DashboardInstance,
	//   component: CardNumber
	// ) => NumberData | undefined
}
export type CardNumberData = CardData<NumberData>

// export interface FilledCardNumber extends CardNumber, BaseFilledDashboardCard {
//   id: string
//   opts: NumberOptions
//   data: NumberData | undefined
// }

// CUSTOM

export interface CustomDashboardCard<C extends Component> extends BaseDashboardCard {
	type: CardType.CUSTOM
	component: C
	props: ComponentProps<C>
	getter: CardData<unknown>
	options?: unknown | (() => unknown)
	// getData: (
	//   dashboard: DashboardInstance,
	//   component: CustomDashboardCard
	// ) => unknown | undefined
}
export type CustomDashboardCardData<T = unknown> = CardData<T>

// export interface FilledCustomDashboardCard extends CustomDashboardCard, BaseFilledDashboardCard {
//   id: string
//   _component: ComponentPortal<unknown>
//   opts: unknown
//   data?: unknown
// }

// CHART

export enum ChartType {
	LINE = 'line',
	BAR = 'bar',
	SCATTER = 'scatter',
	PIE = 'pie',
	RADAR = 'radar',
	MAP = 'map',
	TREE = 'tree',
	TREEMAP = 'treemap',
	GRAPH = 'graph',
	GAUGE = 'gauge',
	FUNNEL = 'funnel',
	PARALLEL = 'parallel',
	SANKEY = 'sankey',
	BOXPLOT = 'boxplot',
	// CANDLESTICK = 'candlestick',
	// EFFECT_SCATTER = 'effectScatter',
	// LINES = 'lines',
	// HEATMAP = 'heatmap',
	// PICTORIAL_BAR = 'pictorialBar',
	// THEME_RIVER = 'themeRiver',
	// SUNBURST = 'sunburst',
	// CUSTOM = 'custom'
}

export type DashboardCardSubType = ChartType

export interface ChartOptions {
	hideAxis?: boolean
	opacityLast?: boolean
	legendShow?: boolean
	legendType?: LegendComponentOption['type']
	splitLine?: boolean
	maxY?: number
	minY?: number
	calculateMinY?: boolean
	roundDecimals?: number
	isPercent?: boolean
	isPercentSecondary?: boolean
	boundaryGap?: boolean
	series?: string
}

export interface ChartData<T extends ChartType = ChartType> extends EChartsOption {
	series?: Array<ChartDataType[T]>
	isPercent?: boolean
}

// export interface ChartDataType {
// 	line: LineSeriesOption
// 	bar: BarSeriesOption
// 	scatter: ScatterSeriesOption
// 	pie: PieSeriesOption
// 	radar: RadarSeriesOption
// 	map: MapSeriesOption
// 	tree: TreeSeriesOption
// 	treemap: TreemapSeriesOption
// 	graph: GraphSeriesOption
// 	gauge: GaugeSeriesOption
// 	funnel: FunnelSeriesOption
// 	parallel: ParallelSeriesOption
// 	sankey: SankeySeriesOption
// 	boxplot: BoxplotSeriesOption
// 	candlestick: CandlestickSeriesOption
// 	effectScatter: EffectScatterSeriesOption
// 	lines: LinesSeriesOption
// 	heatmap: HeatmapSeriesOption
// 	pictorialBar: PictorialBarSeriesOption
// 	themeRiver: ThemeRiverSeriesOption
// 	sunburst: SunburstSeriesOption
// 	custom: CustomSeriesOption
// }
export interface ChartDataType {
	line: Omit<LineSeriesOption, 'type'> & { type?: ChartType.LINE }
	bar: Omit<BarSeriesOption, 'type'> & { type?: ChartType.BAR }
	scatter: Omit<ScatterSeriesOption, 'type'> & { type?: ChartType.SCATTER }
	pie: Omit<PieSeriesOption, 'type'> & { type?: ChartType.PIE }
	radar: Omit<RadarSeriesOption, 'type'> & { type?: ChartType.RADAR }
	map: Omit<MapSeriesOption, 'type'> & { type?: ChartType.MAP }
	tree: Omit<TreeSeriesOption, 'type'> & { type?: ChartType.TREE }
	treemap: Omit<TreemapSeriesOption, 'type'> & { type?: ChartType.TREEMAP }
	graph: Omit<GraphSeriesOption, 'type'> & { type?: ChartType.GRAPH }
	gauge: Omit<GaugeSeriesOption, 'type'> & { type?: ChartType.GAUGE }
	funnel: Omit<FunnelSeriesOption, 'type'> & { type?: ChartType.FUNNEL }
	parallel: Omit<ParallelSeriesOption, 'type'> & { type?: ChartType.PARALLEL }
	sankey: Omit<SankeySeriesOption, 'type'> & { type?: ChartType.SANKEY }
	boxplot: Omit<BoxplotSeriesOption, 'type'> & { type?: ChartType.BOXPLOT }
	// candlestick: Omit<CandlestickSeriesOption, 'type'> & { type?: ChartType.CANDLESTICK }
	// effectScatter: Omit<EffectScatterSeriesOption, 'type'> & { type?: ChartType.EFFECT_SCATTER }
	// lines: Omit<LinesSeriesOption, 'type'> & { type?: ChartType.LINES }
	// heatmap: Omit<HeatmapSeriesOption, 'type'> & { type?: ChartType.HEATMAP }
	// pictorialBar: Omit<PictorialBarSeriesOption, 'type'> & { type?: ChartType.PICTORIAL_BAR }
	// themeRiver: Omit<ThemeRiverSeriesOption, 'type'> & { type?: ChartType.THEME_RIVER }
	// sunburst: Omit<SunburstSeriesOption, 'type'> & { type?: ChartType.SUNBURST }
	// custom: CustomSeriesOption
}

export interface ChartCard<T extends ChartType> extends BaseDashboardCard {
	type: CardType.CHART
	chartType: T
	getter: CardData<ChartData<T>>
	options?: ChartOptions
	// options?: ChartOptions | (() => ChartOptions)
	// getData: (
	//   dashboard: DashboardInstance,
	//   component: Chart<T>
	// ) => ChartData<T> | undefined
}
export type ChartCardData<T extends ChartType = ChartType> = CardData<ChartData<T>>

// export interface FilledChart<T extends ChartType>
//   extends Chart<T>,
//     BaseFilledDashboardCard {
//   id: string
//   chartType: T
//   opts: ChartOptions
//   data?: ChartData<T>
// }

// GENERAL

export interface DashboardCardOptions {
	break: undefined
	number: NumberOptions
	chart: ChartOptions
	custom: object
}

export interface DashboardCardData {
	break: undefined
	number: NumberData
	chart: ChartData<ChartType>
	custom: unknown
}

export interface DashboardCards {
	break: Break
	title: Title
	number: CardNumber
	chart: ChartCard<ChartType>
	custom: CustomDashboardCard<Component>
}

// export interface FilledDashboardCards {
//   break: FilledBreak
//   title: FilledTitle
//   'number': FilledCardNumber
//   'chart': FilledChart<ChartType>
//   custom: FilledCustomDashboardCard
// }

export type DashboardCard = CardNumber | ChartCard<ChartType> | CustomDashboardCard<Component> | Break | Title

// export type FilledDashboardCard =
//   | FilledCardNumber
//   | FilledChart<ChartType>
//   | FilledCustomDashboardCard

export type ScopeOptions =
	| 'currentMonth'
	| 'lastMonth'
	| 'currentYear'
	| 'lastYear'
	| 'currentQuarter'
	| 'lastQuarter'
	| 'last30Days'
	| 'last14Days'
	| 'last7Days'

export interface DashboardOptions {
	scope?: {
		options?: ScopeOptions[]
		default?: ScopeOptions
	}
	showScopeSelect?: boolean
	showFilter?: boolean
	showToolbox?: boolean
}

export const commonOptions: ChartData<ChartType> = {
	backgroundColor: 'transparent',
	legend: {
		show: false,
	},
	tooltip: {
		trigger: 'axis',
		axisPointer: {
			type: 'none',
		},
		appendTo: (chartContainer) => chartContainer.parentElement?.parentElement,
	},
	grid: {
		left: 0,
		right: 0,
		bottom: 0,
		top: 0,
		containLabel: false,
	},
}

export async function resolveGetter<Data>(
	getter: CardData<Data> | Promise<CardData<Data>>,
): Promise<CardDataWithLoading<Data>> {
	if (getter instanceof Promise) {
		return resolveGetter(await getter)
	}

	if (getter !== null && typeof getter === 'object' && 'isLoading' in getter) {
		return getter
	}

	return {
		isLoading: false,
		data: getter,
	}
}
