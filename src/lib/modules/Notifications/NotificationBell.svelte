<script lang="ts">
	import { onMount } from 'svelte'

	import { UnreadNotificationsCount } from '$lib/graphql/generated/gateway'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'

	import { getAuth } from '$lib/services/auth.svelte'

	const auth = getAuth()
	const modal = getModal()

	const query = () =>
		UnreadNotificationsCount({
			variables: {
				filter: {
					where: {
						userId: auth.session?.user_id,
						viewed: { $ne: true },
					},
				},
			},
			fetchPolicy: 'no-cache',
			nextFetchPolicy: 'no-cache',
			pollInterval: 5 * 60 * 1000,
			context: {
				headers: {
					'cache-control': 'no-cache',
				},
				session: auth.session,
			},
		})

	const result = query()

	const count = $derived($result.data?.notifications?.totalCount ?? 0)

	function onclick() {
		modal.open('notifications')
	}

	onMount(() => {
		const change = () => {
			const state = document.visibilityState
			const visible = state === 'visible'
			const hidden = state === 'hidden'

			if (visible) {
				$result.query.refetch()
				$result.query.startPolling(5 * 60 * 1000)
			} else if (hidden) {
				$result.query.stopPolling()
			}
		}

		document.addEventListener('visibilitychange', change)

		return {
			destroy() {
				document.removeEventListener('visibilitychange', change)
			},
		}
	})
</script>

<button class="btn btn-lg hover:preset-tonal relative cursor-pointer" {onclick} aria-label="Notifications">
	<span class="badge-icon preset-filled-warning-500 absolute -top-1 -right-0 z-10">{count}</span>
	<iconify-icon icon="radix-icons:bell" width="24" height="24" class="ml-0!" noobserver></iconify-icon>
</button>
