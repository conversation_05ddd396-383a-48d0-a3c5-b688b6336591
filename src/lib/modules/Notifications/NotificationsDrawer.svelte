<script lang="ts">
	import { AppBar, ProgressRing, Segment } from '@skeletonlabs/skeleton-svelte'

	import {
		AnswerInvitation,
		MarkAllReadNotifications,
		type Notification,
		Notifications,
		UpdateNotification,
		NotificationType,
	} from '$lib/graphql/generated/gateway'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'
	import { getLocaleForDateFormat } from '$lib/utils'

	import { getAuth } from '$lib/services/auth.svelte'

	const auth = getAuth()
	const modal = getModal()

	const { mode = undefined }: { mode?: 'invitations' } = $props()

	let filter = $state<string>('all')

	const iconMap: { [index: string]: string } = {
		INVITATION: 'radix-icons:envelope-closed',
		REPORT: 'radix-icons:exclamation-triangle',
		NEWSLETTER: 'radix-icons:file-text',
		MESSAGE: 'radix-icons:chat-bubble',
	}

	async function markViewed(notification: Omit<Notification, 'userId'>) {
		await UpdateNotification({
			variables: {
				partialEntity: {
					_id: notification._id,
					userId: auth.profile?._id,
					viewed: notification.viewed ? false : true,
				},
			},
			refetchQueries: ['Notifications', 'UnreadNotificationsCount'],
		})
	}

	async function markAllViewed() {
		await MarkAllReadNotifications({
			refetchQueries: ['Notifications', 'UnreadNotificationsCount'],
		})
	}

	async function acceptInvitation(notification: Omit<Notification, 'userId'>) {
		await AnswerInvitation({
			variables: { filter: { id: notification.parentId }, answer: true },
			refetchQueries: ['Notifications', 'UnreadNotificationsCount'],
		})
	}

	async function rejectInvitation(notification: Omit<Notification, 'userId'>) {
		await AnswerInvitation({
			variables: { filter: { id: notification.parentId }, answer: false },
			refetchQueries: ['Notifications', 'UnreadNotificationsCount'],
		})
	}

	function resolveHref(_notification: Omit<Notification, 'userId'>) {
		// if (notification.type === 'INVITATION' && notification.parentId) {
		// 	return ['/', 'invitation', notification.parentId]
		// }

		return
	}

	async function onclick(notification: Omit<Notification, 'userId'>) {
		if (notification.type === 'INVITATION' && notification.parentId) {
			return
		}

		await markViewed(notification)

		const to = resolveHref(notification)
		if (Array.isArray(to) && typeof window !== 'undefined') {
			window.location.href = to.join('/')
		}
	}

	function timeAgo(secondsElapsed: number) {
		const formatter = new Intl.RelativeTimeFormat('en')
		const ranges = {
			years: 3600 * 24 * 365,
			months: 3600 * 24 * 30,
			weeks: 3600 * 24 * 7,
			days: 3600 * 24,
			hours: 3600,
			minutes: 60,
			seconds: 1,
		}
		for (const key in ranges) {
			if (ranges[key as keyof typeof ranges] < Math.abs(secondsElapsed)) {
				const delta = secondsElapsed / ranges[key as keyof typeof ranges]
				return formatter.format(Math.round(delta), key as keyof typeof ranges)
			}
		}
	}

	function formatDate(input: string | Date) {
		const date = input instanceof Date ? input : new Date(input)
		const secondsElapsed = (date.getTime() - Date.now()) / 1000

		if (secondsElapsed > -7200) {
			// 2 hours
			return timeAgo(secondsElapsed)
		} else if (secondsElapsed > -86400) {
			// 1day
			return date.toLocaleDateString(getLocaleForDateFormat(), {
				year: 'numeric',
				month: 'short',
				day: 'numeric',
				hour: 'numeric',
				minute: 'numeric',
			})
		}

		return date.toLocaleDateString(getLocaleForDateFormat(), {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		})
	}

	function isPointer(notification: Omit<Notification, 'userId'>) {
		return notification?.type !== 'INVITATION'
	}

	const query = () =>
		Notifications({
			variables: {
				filter: {
					where: {
						userId: auth.profile?._id,
						...(filter === 'unread' ? { viewed: { $nin: [true] } } : {}),
						...(mode === 'invitations' ? { type: { $in: ['INVITATION'] } } : {}),
					},
					limit: 20,
					sort: { _id: -1 },
				},
			},
			fetchPolicy: 'network-only',
			nextFetchPolicy: 'cache-first',
		})

	const result = query()

	const notifications = $derived($result.data?.notifications ?? { totalCount: 0, nodes: [] })

	function fetchMore() {
		$result.query.fetchMore({
			variables: {
				filter: {
					...$result.query.variables?.filter,
					after: $result.query.getCurrentResult().data.notifications.nodes?.at(-1)?._id,
				},
			},
		})
	}

	$effect(() => {
		$result.query.refetch({
			filter: {
				where: {
					userId: auth.profile?._id,
					...(filter === 'unread' ? { viewed: { $nin: [true] } } : {}),
					...(mode === 'invitations' ? { type: { $in: ['INVITATION'] } } : {}),
				},
				limit: 20,
				sort: { _id: -1 },
			},
		})
	})
</script>

<div class="sticky top-0">
	<AppBar background="bg-surface-50 dark:bg-surface-950">
		{#snippet lead()}
			<h4 class="h4">{mode === 'invitations' ? 'Invitations' : 'Notifications'}</h4>
		{/snippet}

		{#snippet trail()}
			<button class="btn btn-lg hover:preset-tonal cursor-pointer" onclick={() => modal.close()} aria-label="Close">
				<iconify-icon icon="radix-icons:cross-1" width="24" height="24"></iconify-icon>
			</button>
		{/snippet}
	</AppBar>

	<div class="bg-surface-100 dark:bg-surface-900 flex justify-between px-4 py-2">
		<Segment value={filter} onValueChange={(e) => (filter = e.value ?? 'all')}>
			<Segment.Item value="all">All</Segment.Item>
			<Segment.Item value="unread">Unread</Segment.Item>
		</Segment>

		<button class="btn btn-lg hover:preset-filled" onclick={() => markAllViewed()} aria-label="Mark all as read">
			<iconify-icon icon="radix-icons:eye-open" width="24" height="24"></iconify-icon>
		</button>
	</div>
</div>

{#if !$result.loading && notifications?.nodes?.length}
	<dl class="list-dl text-sm md:text-base">
		{#each notifications.nodes as notification (notification._id)}
			<!-- svelte-ignore a11y_click_events_have_key_events -->
			<!-- svelte-ignore a11y_no_static_element_interactions -->
			<!-- svelte-ignore a11y_missing_attribute -->
			<a
				class="flex grow-0 px-4 pt-4 pb-2 {isPointer(notification) && 'cursor-pointer'}"
				onclick={() => {
					if (isPointer(notification)) onclick(notification)
				}}
			>
				<span class="text-center">
					<dt><iconify-icon icon={iconMap[notification.type]} width="20" height="20"></iconify-icon></dt>
					<dd class="text-primary-600 mt-[0.4rem]">
						{#if !notification.viewed}
							<iconify-icon icon="radix-icons:dot-filled" width="24" height="24"></iconify-icon>
						{/if}
					</dd>
				</span>

				<span class="mx-4 grow">
					<dt class="font-semibold">{notification.title}</dt>
					<dd class="mt-2.5 text-sm">{notification.message}</dd>
				</span>

				<span class="min-w-fit grow-0 text-right">
					<dt>{formatDate(notification.createdAt)}</dt>

					<dd class="mt-1.5">
						{#if notification.type === NotificationType.Invitation}
							{#if !notification.viewed}
								<button
									class="btn btn-lg text-tertiary-700 hover:preset-tonal-primary"
									onclick={() => acceptInvitation(notification)}
									aria-label="Accept"
									><iconify-icon icon="radix-icons:check" width="24" height="24"></iconify-icon></button
								>
								<button
									class="btn btn-lg text-primary-600 hover:preset-tonal"
									onclick={() => rejectInvitation(notification)}
									aria-label="Reject"
									><iconify-icon icon="radix-icons:cross-2" width="24" height="24"></iconify-icon></button
								>
							{/if}
						{:else if notification.viewed}
							<button
								class="btn btn-lg text-tertiary-700 hover:preset-tonal-primary"
								onclick={() => markViewed(notification)}
								aria-label="Mark as unread"
								><iconify-icon icon="radix-icons:eye-none" width="24" height="24"></iconify-icon></button
							>
						{:else}
							<button
								class="btn btn-lg text-primary-600 hover:preset-tonal"
								onclick={() => markViewed(notification)}
								aria-label="Mark as read"
								><iconify-icon icon="radix-icons:eye-open" width="24" height="24"></iconify-icon></button
							>
						{/if}
					</dd>
				</span>
			</a>

			<hr class="hr" />
		{/each}
	</dl>

	{#if $result.loading}
		<ProgressRing
			classes="mx-auto my-4"
			value={null}
			strokeWidth="100px"
			meterStroke="stroke-secondary-500"
			trackStroke="stroke-secondary-500/30"
			size="size-10"
		/>
	{:else if notifications.totalCount > notifications.nodes.length}
		<button class="preset-filled btn mx-auto my-4 block" onclick={() => fetchMore()}>Load More</button>
	{/if}
{:else if !$result.loading}
	<div class="px-2 py-6 text-center">We have no notifications to show you at this time.</div>
{/if}
