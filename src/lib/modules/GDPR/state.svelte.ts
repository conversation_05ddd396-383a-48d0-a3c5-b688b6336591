export interface Choice {
	label: string
	description: string
	value: boolean
}

export interface Choices {
	necessary: Choice
	tracking: Choice
	analytics: Choice
	marketing: Choice
}

export type Chosen = { [key in keyof Choices]: boolean }

class GDPR {
	readonly choices = $state<Chosen>({
		necessary: true,
		tracking: false,
		analytics: false,
		marketing: false,
	})

	readonly opened = $state({
		banner: false,
		settings: false,
	})

	readonly showCookieBanner =
		import.meta.env.VITE_DATADOG_CLIENT_TOKEN !== undefined || import.meta.env.GOOGLE_TAG_ID !== undefined

	update = (choices: <PERSON><PERSON>) => {
		this.choices.tracking = choices.tracking
		this.choices.analytics = choices.analytics
		this.choices.marketing = choices.marketing
	}

	openBanner = (value: boolean = true) => {
		this.opened.banner = value
	}

	closeBanner = () => {
		this.opened.banner = false
	}

	openSettings = (value: boolean = true) => {
		this.opened.settings = value
	}

	closeSettings = () => {
		this.opened.settings = false
	}
}

export const gdpr = new GDPR()
