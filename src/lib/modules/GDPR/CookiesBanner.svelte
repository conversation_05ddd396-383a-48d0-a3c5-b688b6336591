<script lang="ts" module>
	import { type Choices, type <PERSON><PERSON> } from './state.svelte.js'

	function validate(choice: <PERSON><PERSON>, cookieChoices: <PERSON><PERSON>) {
		const choices = Object.keys(choice)
		const chosen = Object.keys(cookieChoices)

		if (chosen.length !== choices.length) {
			return false
		}

		return chosen.every((c) => choices.includes(c))
	}
</script>

<script lang="ts">
	import { set, get, remove, type CookieAttributes } from 'es-cookie'
	import { onMount } from 'svelte'
	import { fade } from 'svelte/transition'

	import Analytics from './Analytics.svelte'
	import { gdpr } from './state.svelte.js'

	const {
		class: klass,

		// Config
		cookieName = 'consent',
		canRejectCookies = false,
		showEditIcon = true,

		// Content
		heading = 'This website uses cookies',
		description = 'We use cookies to improve user experience, personalize content and ads, provide social media features and for analytics. We also share information about your use of our site with our affiliated companies and our social media, advertising, analytics  and other partners, who may combine it with other information that you’ve provided to them or that they’ve collected from your use of the services.<br /><br />By clicking OK you agree to our use of cookies as described above. You can also manage your cookie settings below.',
		cookieConfig = {},
		choices = {},

		// Labels
		acceptAllLabel = 'Accept all',
		acceptSelectedLabel = 'Accept selected',
		rejectLabel = 'Reject all',
		settingsLabel = 'Cookie settings',
		closeLabel = 'Close settings',
		editLabel = 'Edit cookie settings',

		// Whether to show the cookie banner if the user has not yet accepted or rejected your choices.
		visible = true,

		// Events
		onValueChange,
	}: {
		class?: string

		// Config
		cookieName?: string
		canRejectCookies?: boolean
		showEditIcon?: boolean

		// Content
		heading?: string
		description?: string
		cookieConfig?: CookieAttributes
		choices?: Partial<Choices>

		// Labels
		acceptAllLabel?: string
		acceptSelectedLabel?: string
		rejectLabel?: string
		settingsLabel?: string
		closeLabel?: string
		editLabel?: string

		// Whether to show the cookie banner if the user has not yet accepted or rejected your choices.
		visible?: boolean

		// Events
		onValueChange?: (value: Chosen) => void
	} = $props()

	const defaults: CookieAttributes = {
		sameSite: 'strict',
	}

	const choicesDefaults: Choices = {
		necessary: {
			label: 'Necessary cookies',
			description:
				'Necessary cookies help make a website usable by enabling basic functions like page navigation and access to secure areas of the website. The website cannot function properly without these cookies.',
			value: true,
		},
		tracking: {
			label: 'Preferences cookies',
			description:
				'Preference cookies enable a website to remember information that changes the way the website behaves or looks, like your preferred language or the region that you are in.',
			value: false,
		},
		analytics: {
			label: 'Statistics cookies',
			description:
				'Statistic cookies help website owners to understand how visitors interact with websites by collecting and reporting information anonymously.',
			value: false,
		},
		marketing: {
			label: 'Marketing cookies',
			description:
				'Marketing cookies are used to track visitors across websites. The intention is to display ads that are relevant and engaging for the individual user and thereby more valuable for publishers and third party advertisers.',
			value: false,
		},
	}

	const choicesMerged = Object.assign({}, choicesDefaults, choices)

	const choicesArr = $derived(
		Object.keys(choicesMerged).map((choice) => {
			return Object.assign({}, choicesMerged[choice as keyof Choices], { id: choice as keyof Choices })
		}),
	)

	const cookieChoices = $derived(
		choicesArr.reduce((result, item) => {
			result[item.id] = item.value ? item.value : false
			return result
		}, {} as Chosen),
	)

	const necessaryCookieChoices = $derived(
		choicesArr.reduce((result, item) => {
			result[item.id] = item.id === 'necessary'
			return result
		}, {} as Chosen),
	)

	const hasSelectedAllCookieTypes = $derived(Object.values(cookieChoices).every((value) => value === true))

	onMount(() => {
		if (!cookieName) {
			throw new Error('You must set gdpr cookie name')
		}

		const cookie = get(cookieName)
		if (!cookie) {
			gdpr.openBanner(visible)
			return
		}

		try {
			const choices = JSON.parse(cookie) as Chosen
			const valid = validate(cookieChoices, choices)

			if (!valid) {
				throw new Error('cookie consent has changed')
			}

			execute(choices)
		} catch {
			removeCookie()
			gdpr.openBanner(visible)
		}
	})

	function setCookie(choices: Chosen) {
		const expires = new Date()
		expires.setDate(expires.getDate() + 365)

		const options = Object.assign({}, defaults, cookieConfig, { expires })
		set(cookieName, JSON.stringify(choices), options)
	}

	function removeCookie() {
		const { path } = cookieConfig
		remove(cookieName, Object.assign({}, path ? { path } : {}))
	}

	function execute(chosen: Chosen) {
		const types = Object.keys(cookieChoices) as unknown as Array<keyof Choices>

		for (const t of types) {
			const agreed = chosen[t]
			if (choicesMerged[t]) {
				choicesMerged[t].value = agreed
			}

			window.dispatchEvent(new CustomEvent(`consent:${t}`, { detail: { agreed } }))
		}

		onValueChange?.(chosen)

		gdpr.closeBanner()
	}

	function reject() {
		setCookie(necessaryCookieChoices)
		execute(necessaryCookieChoices)
	}

	function accept() {
		setCookie(cookieChoices)
		execute(cookieChoices)
	}
</script>

{#if gdpr.showCookieBanner}
	{#if showEditIcon}
		<button
			class="fixed right-5 bottom-5 z-[99980] h-10 w-10 rounded-full border-0 bg-white p-2.5 opacity-100 shadow-lg transition-opacity duration-200 hover:bg-black hover:text-white"
			aria-label={editLabel}
			onclick={() => {
				gdpr.openBanner(visible)
			}}
			transition:fade
		>
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">
				<path
					d="M510.52 255.82c-69.97-.85-126.47-57.69-126.47-127.86-70.17
					0-127-56.49-127.86-126.45-27.26-4.14-55.13.3-79.72 12.82l-69.13
					35.22a132.221 132.221 0 0 0-57.79 57.81l-35.1 68.88a132.645 132.645 0 0
					0-12.82 80.95l12.08 76.27a132.521 132.521 0 0 0 37.16 72.96l54.77
					54.76a132.036 132.036 0 0 0 72.71 37.06l76.71 12.15c27.51 4.36 55.7-.11
					80.53-12.76l69.13-35.21a132.273 132.273 0 0 0
					57.79-57.81l35.1-68.88c12.56-24.64 17.01-52.58 12.91-79.91zM176
					368c-17.67 0-32-14.33-32-32s14.33-32 32-32 32 14.33 32 32-14.33 32-32
					32zm32-160c-17.67 0-32-14.33-32-32s14.33-32 32-32 32 14.33 32 32-14.33
					32-32 32zm160 128c-17.67 0-32-14.33-32-32s14.33-32 32-32 32 14.33 32
					32-14.33 32-32 32z"
				/>
			</svg>
		</button>
	{/if}
	{#if gdpr.opened.banner}
		<div
			class="bg-surface-800 fixed right-0 bottom-0 left-0 z-[99990] p-8 text-white transition-all duration-200 {klass}"
			transition:fade
		>
			<div class="mx-auto flex flex-row justify-between">
				<div class="mr-8">
					<div>
						<p class="m-0 font-bold">{heading}</p>
						<p class="mt-2 mb-0">
							{@html description}
						</p>
					</div>
				</div>
				<div class="mt-0 flex items-end">
					<button
						type="button"
						class="ml-2 cursor-pointer border-0 bg-white px-10 py-4 text-base whitespace-nowrap text-black transition-opacity duration-200 hover:opacity-60"
						aria-label={settingsLabel}
						onclick={() => {
							gdpr.openSettings()
						}}
					>
						{settingsLabel}
					</button>
					{#if canRejectCookies}
						<button
							type="submit"
							class="ml-2 cursor-pointer border-0 bg-white px-10 py-4 text-base whitespace-nowrap text-black transition-opacity duration-200 hover:opacity-60"
							onclick={() => reject()}
							aria-label={rejectLabel}
						>
							{rejectLabel}
						</button>
					{/if}
					<button
						type="submit"
						class="ml-2 cursor-pointer border-0 bg-white px-10 py-4 text-base font-bold whitespace-nowrap text-black transition-opacity duration-200 hover:opacity-60"
						onclick={() => accept()}
						aria-label={hasSelectedAllCookieTypes ? acceptAllLabel : acceptSelectedLabel}
					>
						{hasSelectedAllCookieTypes ? acceptAllLabel : acceptSelectedLabel}
					</button>
				</div>
			</div>
		</div>
	{/if}

	{#if gdpr.opened.settings}
		<div class="bg-opacity-80 fixed inset-0 z-[99999] flex bg-black transition-all duration-300" transition:fade>
			<div
				class="m-auto box-border max-h-screen max-w-[500px] scale-95 transform overflow-y-auto bg-white p-10 text-black transition-transform duration-200"
			>
				{#each choicesArr as choice (choice.id)}
					{#if Object.hasOwnProperty.call(choicesMerged, choice.id) && choicesMerged[choice.id]}
						<div class="mb-5 block pl-15 {choice.id === 'necessary' ? 'pointer-events-none text-gray-400' : ''}">
							<input
								type="checkbox"
								id={`gdpr-check-${choice.id}`}
								class="hidden"
								bind:checked={choicesMerged[choice.id].value}
								disabled={choice.id === 'necessary'}
							/>
							<label for={`gdpr-check-${choice.id}`} class="relative flex items-center pl-0 text-2xl font-bold">
								<span class="absolute top-1/2 left-[-60px] h-5 w-10 -translate-y-1/2 rounded-full bg-gray-300"></span>
								<span
									class="absolute top-1/2 left-[-58px] h-4 w-4 -translate-y-1/2 rounded-full bg-black transition-transform duration-200 {choicesMerged[
										choice.id
									].value
										? 'translate-x-5'
										: ''}"
								></span>
								{choice.label}
							</label>
							<span class="mt-1 ml-0 block text-base">{choice.description}</span>
						</div>
					{/if}
				{/each}
				<button
					type="submit"
					class="mt-10 ml-15 cursor-pointer bg-black px-15 py-4 text-base text-white transition-opacity duration-200 hover:opacity-60"
					aria-label={closeLabel}
					onclick={() => gdpr.closeSettings()}
				>
					{closeLabel}
				</button>
			</div>
		</div>
	{/if}

	{#if gdpr.choices.marketing && gdpr.choices.analytics}
		<Analytics />
	{/if}
{/if}
