<script lang="ts">
	import { browser } from '$app/environment'
	import { page } from '$app/state'

	const gaTagId = import.meta.env.GOOGLE_TAG_ID

	$effect(() => {
		if (typeof gtag !== 'undefined' && browser) {
			gtag('config', gaTagId, {
				page_title: document.title,
				page_path: page.url.pathname,
			})
		}
	})
</script>

<svelte:head>
	<script async src="https://www.googletagmanager.com/gtag/js?id={gaTagId}"></script>

	<script>
		window.dataLayer = window.dataLayer || []

		function gtag(args) {
			dataLayer.push(args)
		}

		gtag('js', new Date())
		gtag('config', gaTagId)
	</script>
</svelte:head>
