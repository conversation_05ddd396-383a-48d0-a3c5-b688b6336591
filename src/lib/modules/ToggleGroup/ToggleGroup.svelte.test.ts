import { render, screen } from '@testing-library/svelte'
import { describe, expect, it } from 'vitest'

import ToggleGroup from '.'
import ToggleGroupTest from './ToggleGroup.test.svelte'

describe('ToggleGroup', () => {
	const testId = 'toggle-group'

	it('Renders the component', () => {
		render(ToggleGroup, {})
		const component = screen.getByTestId(testId)
		expect(component).toBeInTheDocument()
	})

	for (const prop of ['base', 'background', 'border', 'gap', 'padding', 'rounded', 'width', 'classes']) {
		it(`Correctly applies the root \`${prop}\` prop`, () => {
			const value = 'bg-green-500'
			render(ToggleGroup, { [prop]: value })
			const component = screen.getByTestId(testId)
			expect(component).toHaveClass(value)
		})
	}

	it('should render in the disabled state', () => {
		render(ToggleGroup, { disabled: true })
		const component = screen.getByTestId(testId)
		expect(component.dataset.disabled).toBeDefined()
	})
})

describe('ToggleGroupItem', () => {
	const testIds = {
		root: 'toggle-group-item',
		itemInput: 'toggle-group-item-input',
	}

	it('Renders the item', () => {
		render(ToggleGroupTest, {})
		const component = screen.getAllByTestId(testIds.root)[0]
		expect(component).toBeInTheDocument()
	})

	it('should render custom child content', () => {
		render(ToggleGroupTest, {})
		const component = screen.getAllByTestId(testIds.root)[0]
		expect(component).toHaveTextContent('left')
	})

	it('should render in the checked state', () => {
		render(ToggleGroupTest, {})
		const component = screen.getAllByTestId(testIds.root)[0]
		const attrDataState = component.getAttribute('data-state')
		expect(attrDataState).toBe('on')
	})

	it('should render in the unchecked state', () => {
		render(ToggleGroupTest, {})
		const component = screen.getAllByTestId(testIds.root)[1]
		const attrDataState = component.getAttribute('data-state')
		expect(attrDataState).toBe('off')
	})

	it('should render in the disabled state', () => {
		render(ToggleGroupTest, {})
		const component = screen.getAllByTestId(testIds.root)[3]
		expect(component).toHaveAttribute('disabled')
	})

	for (const prop of ['base', 'classes']) {
		it(`Correctly applies the root \`${prop}\` prop`, () => {
			const value = 'bg-green-500'
			render(ToggleGroupTest, { childProps: { [prop]: value } })
			const component = screen.getAllByTestId(testIds.root)[0]
			expect(component).toHaveClass(value)
		})
	}
})
