<script lang="ts">
	import ToggleGroup from '.'

	interface Props {
		childProps?: Record<string, unknown>
	}
	const { childProps }: Props = $props()
</script>

<ToggleGroup value={['left']}>
	<ToggleGroup.Item value="left" {...childProps}>left</ToggleGroup.Item>
	<ToggleGroup.Item value="center" {...childProps}>center</ToggleGroup.Item>
	<ToggleGroup.Item value="right" {...childProps}>right</ToggleGroup.Item>
	<ToggleGroup.Item value="justify" disabled {...childProps}>justify</ToggleGroup.Item>
</ToggleGroup>
