<script lang="ts">
	import { getToggleGroupContext } from './context'

	import type { ToggleGroupItemProps } from './types'

	const {
		// Root
		base = 'btn cursor-pointer z-[1] transition-colors duration-100',
		classes = '',
		// State
		stateDisabled = 'disabled',
		stateFocused = 'focused',
		statePressed = 'preset-filled',
		// Snippets
		children,
		// Zag
		...zagProps
	}: ToggleGroupItemProps = $props()

	// Context
	const ctx = getToggleGroupContext()

	// Reactive
	const state = $derived(ctx?.api.getItemState(zagProps))
	const rxDisabled = $derived(state?.disabled ? stateDisabled : '')
	const rxPressed = $derived(state?.pressed ? statePressed : '')
	const rxFocused = $derived(state?.focused ? stateFocused : '')
</script>

<!-- @component An individual ToggleGroup option. -->

<button
	{...ctx?.api.getItemProps(zagProps)}
	class="{base} {rxPressed} {rxDisabled} {rxFocused} {classes}"
	data-testid="toggle-group-item"
>
	{@render children?.()}
</button>
