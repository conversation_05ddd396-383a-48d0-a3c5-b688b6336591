import * as toggle from '@zag-js/toggle-group'
import { setContext, getContext } from 'svelte'

import type { ToggleGroupContext } from './types'

const key = Symbol()

export function setToggleGroupContext(
	value: ToggleGroupContext = {
		api: {} as ReturnType<typeof toggle.connect>,
	},
) {
	return setContext(key, value)
}

export function getToggleGroupContext() {
	return getContext<ToggleGroupContext>(key)
}
