<script lang="ts">
	import { useMachine, normalizeProps } from '@zag-js/svelte'
	import * as toggle from '@zag-js/toggle-group'

	import { setToggleGroupContext } from './context'

	import type { ToggleGroupProps } from './types'

	const {
		// Root
		base = ' inline-flex items-stretch overflow-hidden',
		background = 'preset-outlined-surface-200-800',
		border = 'p-2',
		gap = 'gap-2',
		padding = '',
		rounded = 'rounded-container',
		width = '',
		classes = '',
		// States
		orientVertical = 'flex-col items-stretch',
		orientHorizontal = 'flex-row',
		stateDisabled = 'disabled',
		// Label
		labelledby = '',
		// Snippets
		children,
		// Zag
		...zagProps
	}: ToggleGroupProps = $props()

	// Zag
	const id = $props.id()
	const service = useMachine(toggle.machine as never, () => ({
		id,
		orientation: zagProps.orientation ?? 'horizontal',
		...zagProps,
	})) as toggle.Service
	const api = $derived(toggle.connect(service, normalizeProps))

	// Set Context
	setToggleGroupContext({
		get api() {
			return api
		},
	})

	// Reactive
	const rxOrientation = $derived(service.prop('orientation') === 'vertical' ? orientVertical : orientHorizontal)
	const rxDisabled = $derived(service.prop('disabled') ? stateDisabled : '')
</script>

<!-- @component Capture input for a limited set of options. -->

<nav
	{...api.getRootProps()}
	class="{base} {rxOrientation} {background} {border} {padding} {gap} {rounded} {width} {rxDisabled} {classes}"
	aria-labelledby={labelledby}
	data-testid="toggle-group"
>
	<!-- Items -->
	{@render children?.()}
</nav>
