import * as toggle from '@zag-js/toggle-group'

import type { Snippet } from 'svelte'

// Context ---

export interface ToggleGroupContext {
	api: ReturnType<typeof toggle.connect>
}

// Components ---

export interface ToggleGroupProps extends Omit<toggle.Props, 'id'> {
	// Root ---
	/** Sets base classes. */
	base?: string
	/** Set background classes. */
	background?: string
	/** Set border classes. */
	border?: string
	/** Set flex direction classes. */
	flexDirection?: string
	/** Set gap classes. */
	gap?: string
	/** Set padding classes. */
	padding?: string
	/** Set rounded classes. */
	rounded?: string
	/** Set width classes. */
	width?: string
	/** Provide arbitrary CSS classes. */
	classes?: string

	// Orientation---
	/** Set classes to provide a vertical layout. */
	orientVertical?: string
	/** Set classes to provide a horizontal layout. */
	orientHorizontal?: string

	// States ---
	/** Set classes for the disabled state. */
	stateDisabled?: string
	/** Set classes for the read-only state. */
	stateReadOnly?: string

	// Label ---
	/** Set aria-labelledby for element */
	labelledby?: string

	// Snippets ---
	/** The default child slot. */
	children?: Snippet
}

export interface ToggleGroupItemProps extends Omit<toggle.ItemProps, 'invalid'> {
	// Root ---
	/** Sets base classes. */
	base?: string
	/** Provide arbitrary CSS classes. */
	classes?: string

	/** Set classes for the disabled state. */
	stateDisabled?: string
	/** Set classes for the focus state. */
	stateFocused?: string
	/** Set classes for the pressed state. */
	statePressed?: string

	// Snippets ---
	/** The default child slot. */
	children?: Snippet
}
