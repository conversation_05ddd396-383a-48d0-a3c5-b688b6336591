import Body from './Body.svelte'
import Data from './Data.svelte'
import Foot from './Foot.svelte'
import Head from './Head.svelte'
import Header from './Header.svelte'
import Row from './Row.svelte'
import Table from './Table.svelte'

export * from './interface'

export default /* @__PURE__ */ Object.assign(Table, {
	Body: Object.assign(Body, {
		Row: Object.assign(Row, { Header, Data }),
	}),
	Head: Object.assign(Head, {
		Row: Object.assign(Row, { Header, Data }),
	}),
	Row: Object.assign(Row, { Header, Data }),
	Foot: Object.assign(Foot, {
		Row: Object.assign(Row, { Header, Data }),
	}),
	Header,
	Data,
})
