<script lang="ts">
	import { setContext, type Snippet } from 'svelte'
	import { twMerge } from 'tailwind-merge'

	const {
		class: klass,
		children,
		wrapperClass = 'relative overflow-x-auto max-w-full',
		mode = 'hover',
		noBorder = true,
		radius = 'rounded-xl',
		...rest
	}: {
		class?: string
		children: Snippet
		wrapperClass?: string
		mode?: 'interactive' | 'hover'
		noBorder?: boolean
		radius?: string
	} = $props()

	setContext('noBorder', noBorder)
	setContext('radius', radius)
</script>

<div {...rest} class={wrapperClass}>
	<table
		class={twMerge('data-table table', `table-${mode}`, 'min-w-full overflow-auto border-r-0 bg-transparent!', klass)}
	>
		{@render children()}
	</table>
</div>
