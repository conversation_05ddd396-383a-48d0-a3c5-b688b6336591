<script lang="ts">
	import { getContext } from 'svelte'
	import { twMerge } from 'tailwind-merge'

	import type { CellProps } from './interface'

	const {
		class: klass,
		type = 'td',
		align = 'align-middle',
		children,
		href,
		target,
		onclick,
		...rest
	}: CellProps = $props()

	const tdClassFinal = $derived(twMerge(type, align, getContext('radius'), klass))
</script>

<svelte:element this={type} {...rest} class={tdClassFinal}>
	{#if href}
		<a {href} {target}>
			{@render children?.()}
		</a>
	{:else if onclick}
		<button class="bg-initial btn p-0" type="button" {onclick}>
			{@render children?.()}
		</button>
	{:else}
		{@render children?.()}
	{/if}
</svelte:element>

<style lang="postcss">
	@reference "tailwindcss";

	:global(.data-table.table-interactive tr) {
		&:hover > td {
			background-color: rgb(var(--color-primary-500) / 0.1) !important;
		}
	}

	:global(.data-table > *:first-child:last-child tr) {
		&:not(:last-child):not(:first-child) > * {
			border-radius: 0;
		}

		&:first-child > *:not(:first-child):not(:last-child) {
			border-radius: 0;
		}
		&:first-child > *:first-child {
			border-top-right-radius: 0;
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;
		}
		&:first-child > *:last-child {
			border-top-left-radius: 0;
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;
		}

		&:last-child > *:not(:first-child):not(:last-child) {
			border-radius: 0;
		}
		&:last-child > *:first-child {
			border-bottom-right-radius: 0;
			border-top-left-radius: 0;
			border-top-right-radius: 0;
		}
		&:last-child > *:last-child {
			border-bottom-left-radius: 0;
			border-top-left-radius: 0;
			border-top-right-radius: 0;
		}
	}
	:global(.data-table > *:not(:first-child):not(:last-child) tr) {
		th {
			border-radius: 0;
		}
	}
	:global(.data-table > *:first-child:not(:last-child) tr) {
		&:not(:first-child) > * {
			border-radius: 0;
		}
		&:first-child > *:not(:first-child):not(:last-child) {
			border-radius: 0;
		}
		&:first-child > *:first-child:not(:last-child) {
			border-top-right-radius: 0;
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;
		}
		&:first-child > *:last-child:not(:first-child) {
			border-top-left-radius: 0;
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;
		}
		&:first-child > *:last-child:first-child {
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;
		}
	}
	:global(.data-table > *:last-child:not(:first-child) tr) {
		&:not(:last-child) > * {
			border-radius: 0;
		}
		&:last-child > *:not(:first-child):not(:last-child) {
			border-radius: 0;
		}
		&:last-child > *:first-child:not(:last-child) {
			border-bottom-right-radius: 0;
			border-top-left-radius: 0;
			border-top-right-radius: 0;
		}
		&:last-child > *:last-child:not(:first-child) {
			border-bottom-left-radius: 0;
			border-top-left-radius: 0;
			border-top-right-radius: 0;
		}
		&:last-child > *:first-child:last-child {
			border-top-left-radius: 0;
			border-top-right-radius: 0;
		}
	}

	:global(.data-table tbody tr) {
		th {
			background-color: transparent;
		}
	}

	:global(.data-table tbody tr) {
		&:nth-child(even) {
			background-color: transparent;
		}
		&:nth-child(odd) > * {
			background-color: rgb(from var(--color-surface-500) r g b / 0.05);
		}
		&:nth-child(even) > * {
			background-color: rgb(from var(--color-surface-500) r g b / 0.1);
		}
	}

	:global([data-mode='dark'] .data-table.table-interactive tr) {
		&:hover > * {
			background-color: rgb(from var(--color-primary-500) r g b / 0.1) !important;
		}
	}

	:global([data-mode='dark'] .data-table tbody tr) {
		&:nth-child(odd) > * {
			background-color: rgb(from var(--color-surface-700) r g b / 0.3);
		}
		&:nth-child(even) > * {
			background-color: rgb(from var(--color-surface-500) r g b / 0.05);
		}
	}

	/* Cells Specific */

	/* Source: https://stackoverflow.com/questions/11267154/fit-cell-width-to-content */
	.table-cell-fit {
		@apply w-[1%] whitespace-nowrap;
	}

	/* === Sort Styles ==== */

	.table-sort-asc {
		@apply after:ml-1 after:opacity-50 after:!content-['↑'];
	}

	.table-sort-dsc {
		@apply after:ml-1 after:opacity-50 after:!content-['↓'];
	}
</style>
