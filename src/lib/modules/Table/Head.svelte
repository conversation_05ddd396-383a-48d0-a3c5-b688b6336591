<script lang="ts">
	import { twMerge } from 'tailwind-merge'

	import type { Snippet } from 'svelte'

	const {
		class: klass,
		background = 'bg-surface-200 dark:bg-surface-700',
		children,
		defaultRow,
		...rest
	}: { class: string; children: Snippet; background?: string; defaultRow?: boolean } = $props()
</script>

<thead {...rest} class={twMerge(background, klass)}>
	{#if defaultRow}
		<tr>
			{@render children()}
		</tr>
	{:else}
		{@render children()}
	{/if}
</thead>
