import type { Snippet } from 'svelte'
import type { MouseEventHandler } from 'svelte/elements'

export interface CellProps {
	type: 'td' | 'th'
	class?: string
	align?: 'align-middle' | 'align-top' | 'align-bottom'
	children?: Snippet
	href?: string
	target?: '_blank' | '_self' | '_parent' | '_top'
	onclick?: MouseEventHandler<HTMLButtonElement>
	colSpan?: number
	rowSpan?: number
}

export type DataProps = Omit<CellProps, 'type'>

export type HeaderProps = Omit<CellProps, 'type'>
