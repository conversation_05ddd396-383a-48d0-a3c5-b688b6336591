<script lang="ts">
	import { gdpr } from '$lib/modules/GDPR/state.svelte.js'

	import Logo from '$lib/components/Logo.svelte'
	import { getApp } from '$lib/services/app.svelte'

	// Base Classes
	const cBase = 'bg-surface-50 dark:bg-surface-950 border-t border-surface-500/10 text-xs md:text-base'
	const cRowOne = 'flex flex-col md:flex-row justify-between items-center md:items-start space-y-5 md:space-y-0'
	const cRowTwo = 'flex flex-col md:flex-row justify-between items-center md:items-start space-y-4 md:space-y-0'

	const app = getApp()
</script>

<div class="page-footer {cBase}">
	<div class="mx-auto w-full max-w-7xl space-y-10 p-4 py-16 md:py-24">
		<!-- Row 1 -->
		<section class={cRowOne}>
			<div class="grid grid-cols-1 place-content-center place-items-center gap-2 md:place-items-start">
				<a rel="prefetch" class="brand flex items-center" href={app.homePath}>
					<Logo class="mx-auto h-10 w-auto fill-[#15171f] dark:fill-white" type="logo" />
				</a>

				<br />

				<p class="text-sm! opacity-80">Highly Customized Data Extraction,</p>
				<p class="text-sm! opacity-80">Grooming and Analysis.</p>
			</div>

			<div class="hidden grid-cols-3 gap-8 md:grid">
				<div class="space-y-6">
					<h6 class="h6">Product</h6>
					<ul class="space-y-3">
						<!-- <li><a class="anchor" href="/features">Features</a></li> -->
						<li><a class="anchor" href="/pricing" aria-label="Pricing">Pricing</a></li>
						<!-- <li><a class="anchor" href="/docs">Documentation</a></li> -->
						<li><a class="anchor" href="https://status.daptapgo.io/" target="_blank">Status</a></li>
					</ul>
				</div>

				<div class="space-y-6">
					<h6 class="h6">Company</h6>
					<ul class="space-y-3">
						<li><a class="anchor" href="/about" aria-label="About">About</a></li>
						<!-- <li><a class="anchor" href="/blog">Blog</a></li> -->
						<li><a class="anchor" href="/faq" aria-label="FAQ">FAQ</a></li>
					</ul>
				</div>

				<div class="space-y-6">
					<h6 class="h6">Legal</h6>
					<ul class="space-y-3">
						<li>
							<a class="anchor" href="/terms_and_conditions" aria-label="Terms of Service">Terms of Service</a>
						</li>
						<li>
							<a class="anchor" href="/privacy_policy" aria-label="Privacy Policy">Privacy Policy</a>
						</li>
						<!-- <li>
							<a class="anchor" href="/gdpr">GDPR Compliance</a>
						</li> -->
						<!-- <li>
							<a class="anchor" href="data_processing_agreement">Data Processing Agreement</a>
						</li> -->
						{#if gdpr.showCookieBanner}
							<li>
								<!-- svelte-ignore a11y_click_events_have_key_events -->
								<!-- svelte-ignore a11y_missing_attribute -->
								<a
									class="anchor flex items-center"
									onclick={() => {
										gdpr.openBanner()
									}}
									tabindex="0"
									role="button"
									aria-label="Cookie Settings"
									>Cookie Settings <iconify-icon icon="ic:outline-cookie" width="20" height="20" class="ml-1" noobserver
									></iconify-icon></a
								>
							</li>
						{/if}
					</ul>
				</div>
			</div>
		</section>

		<hr class="opacity-20" />

		<!-- Row 2 -->
		<section class={cRowTwo}>
			<p>
				<span class="opacity-50">{new Date().getFullYear()} © Phigital Loyalty, Inc.</span>
			</p>

			<div class="flex gap-6">
				<a class="anchor" href="mailto:<EMAIL>?subject=Meet the Team">Meet the Team</a>
				<a class="anchor" href="mailto:<EMAIL>">Contact</a>
				<a href="https://www.linkedin.com/company/data-gatherers" target="_blank" aria-label="LinkedIn">
					<iconify-icon icon="radix-icons:linkedin-logo" width="24" height="24" noobserver></iconify-icon>
				</a>
			</div>
		</section>
	</div>
</div>
