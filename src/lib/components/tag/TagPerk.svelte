<script lang="ts">
	import { ParticipateInPerk, WithdrawFromPerk, type Perk } from '$lib/graphql/generated/gateway'

	import { type ActionArray, useActions } from '$lib/actions/use-actions.svelte'

	import { errorMap, getTagContext } from './context'

	const context = getTagContext()

	const {
		use = [],
		data,
	}: {
		use?: ActionArray
		data: Partial<Omit<Perk, 'organization'>>
	} = $props()

	const now = new Date()

	async function participate(_id: string) {
		const response = await ParticipateInPerk({
			variables: {
				id: _id,
			},
		})

		if (response.errors?.length) {
			context.error = errorMap[response.errors[0].message] || response.errors[0]
		}
	}

	async function withdraw(_id: string) {
		const response = await WithdrawFromPerk({
			variables: {
				id: _id,
			},
		})

		if (response.errors?.length) {
			context.error = errorMap[response.errors[0].message] || response.errors[0]
		}
	}
</script>

<div
	use:useActions={use}
	class="sm:bg-initial sm:preset-filled-surface-100-900 border-surface-200-800 flex flex-col justify-between overflow-hidden sm:rounded-xl sm:border-[1px]"
>
	<div>
		{#if data.picture}
			<header class="bg-surface-200-800">
				<img
					src={data.picture}
					loading="lazy"
					class="aspect-[16/15] w-full object-cover"
					alt=""
					onerror={(e) => {
						if (e.target) (e.target as HTMLImageElement).style.visibility = 'hidden'
					}}
				/>
			</header>
		{/if}

		<div class="space-y-2 p-4">
			<div class="flex flex-auto touch-pan-x items-center gap-3 overflow-auto pb-2">
				<span class="btn p-0">
					{#if data.type === 'IN_PERSON'}
						<iconify-icon icon="radix-icons:person" class="text-secondary-500 mr-1 text-lg"></iconify-icon>
					{:else if data.type === 'VIRTUAL'}
						<iconify-icon icon="radix-icons:desktop" class="text-secondary-500 mr-1 text-lg"></iconify-icon>
					{/if}
				</span>

				{#if data.date}
					<span class="btn p-0"
						><iconify-icon icon="mdi-light:calendar" class="text-secondary-500 mr-1 text-lg"></iconify-icon>{new Date(
							data.date,
						).toLocaleDateString(undefined, { dateStyle: 'medium' })}</span
					>
				{/if}
				{#if data.date && data.location}
					<span class="text-surface-500 max-h-6 overflow-hidden">&middot; &middot; &middot;</span>
				{/if}
				{#if data.location}
					<span class="btn p-0 text-ellipsis"
						><iconify-icon icon="mdi-light:map-marker" class="text-secondary-500 mr-1 text-lg"
						></iconify-icon>{data.location}</span
					>
				{/if}
			</div>

			<h6 class="h6 text-secondary-700 dark:text-secondary-600 line-clamp-2 pb-1 font-bold text-pretty" data-toc-ignore>
				{data.name}
			</h6>

			{#if data.description}
				<article>
					<p
						class="line-clamp-9 text-sm leading-tight text-pretty whitespace-pre-wrap text-neutral-500 dark:text-neutral-400"
					>
						{data.description}
					</p>
				</article>
			{/if}
		</div>
	</div>

	{#if data.runTo && new Date(data.runTo) > now}
		<footer class="flex items-center justify-between gap-4 px-4 pb-6">
			{#if data.isParticipant}
				<button
					type="button"
					class="btn preset-filled-secondary-500 dark:preset-filled-surface-500"
					onclick={(e) => {
						e.preventDefault()
						withdraw(data._id)
					}}>Opt-Out</button
				>
			{:else if data.isFull}
				<button type="button" class="btn preset-filled-secondary-500" disabled>Full</button>
			{:else}
				<button
					type="button"
					class="btn preset-filled-primary-500"
					onclick={(e) => {
						e.preventDefault()
						participate(data._id)
					}}>Opt-In</button
				>
			{/if}

			<a target="_blank" href={data.url} rel="noreferrer" class="btn preset-outlined justify-self-end">View Details</a>
		</footer>
	{/if}
</div>

<style>
</style>
