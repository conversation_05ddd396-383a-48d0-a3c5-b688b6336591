import { setContext, getContext } from 'svelte'

import type { Tag } from '$lib/graphql/generated/gateway'

const key = Symbol()

export interface TagError {
	message: string
}

class TagContext {
	tag? = $state<Tag | undefined>()
	error? = $state<TagError | undefined>()

	constructor(value: TagContext) {
		this.tag = value.tag
		this.error = value.error
	}
}

export function setTagContext(value: TagContext) {
	return setContext(key, new TagContext(value))
}

export function getTagContext() {
	return getContext<TagContext>(key)
}

export const errorMap: { [index: string]: TagError } = {
	'Cannot return null for non-nullable field Query.tag.': {
		message: 'Invalid tag Serial Number!',
	},
}
