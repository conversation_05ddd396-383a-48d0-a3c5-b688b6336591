<script lang="ts">
	import { ReleaseTag, type Tag } from '$lib/graphql/generated/gateway'
	import { getModal } from '$lib/modules/Modal'

	import { getAuth } from '$lib/services/auth.svelte'

	import { getTagContext, errorMap } from './context'

	const auth = getAuth()
	const modal = getModal()
	const context = getTagContext()

	async function release() {
		modal.open({
			type: 'confirm',
			title: 'Release Tag',
			body: `
			<p>Are you sure you want to release this Tag?</p>
			<br />
			<p class="text-error-500 font-bold">This actions is irreversible.</p>
			<p class="text-error-500">Anyone with physical access to the Tag will be able to claim it.</p>
			`,
			submitClasses: 'preset-filled-error-500',
			response: async (response: boolean) => {
				if (response) {
					const response = await ReleaseTag({
						variables: {
							id: context.tag?._id,
						},
					})

					if (response.errors?.length) {
						context.error = errorMap[response.errors[0].message] || response.errors[0]
					}

					context.tag = response.data?.releaseTag as Tag
				}
			},
		})
	}

	async function transfer() {
		// TODO
	}
</script>

{#if auth.isInternalUser}
	<hr />

	<br />

	<div class="flex justify-end gap-4">
		<button type="button" class="preset-soft-warning btn" onclick={transfer}>Transfer</button>

		<button type="button" class="preset-soft-error btn" onclick={release}>Release</button>
	</div>

	<br />
{/if}
