<script lang="ts">
	import { AsyncPerks } from '$lib/graphql/generated/gateway'

	import { getTagContext } from './context'
	import TagPerk from './TagPerk.svelte'

	const context = getTagContext()

	const perks = $derived.by(() => {
		if (!context.tag) return { rows: [], total: 0 }

		return AsyncPerks({
			variables: {
				search: {
					tagId: context.tag?._id,
				},
			},
		}).then(({ data }) => {
			return {
				rows: data?.perks?.nodes || [],
				total: data?.perks?.totalCount || 0,
			}
		})
	})
</script>

{#await perks then result}
	{#if result?.rows?.length}
		<h3 class="h3">Perks</h3>

		<br />
		<br />

		<div class="grid gap-5 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3 xl:gap-8 2xl:grid-cols-4">
			{#each result.rows as perk (perk._id)}
				<TagPerk data={perk}></TagPerk>
			{/each}
		</div>

		<br />
	{/if}
{/await}
