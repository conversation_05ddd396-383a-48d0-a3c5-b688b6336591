<script lang="ts">
	import { ClaimTag, Tag } from '$lib/graphql/generated/gateway'

	import { getTagContext, errorMap } from './context'

	const context = getTagContext()

	async function claim() {
		const response = await ClaimTag({
			variables: {
				id: context.tag?._id,
			},
		})

		if (response.errors?.length) {
			context.error = errorMap[response.errors[0].message] || response.errors[0]
		}

		context.tag = response.data?.claimTag as Tag
	}

	// claim()
</script>

<hr />

<br />

<button type="button" class="preset-filled-primary btn" onclick={claim}>Register this Tag</button>
