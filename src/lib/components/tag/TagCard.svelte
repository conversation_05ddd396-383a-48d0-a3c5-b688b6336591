<script lang="ts">
	import { useActions, type ActionArray } from '$lib/actions/use-actions.svelte.js'

	import type { TagsQuery } from '$lib/graphql/generated/gateway'

	const { use = [], data }: { use?: ActionArray; data: TagsQuery['tags']['nodes'][number] } = $props()
</script>

<div use:useActions={use} class="card card-hover relative overflow-hidden rounded-lg">
	<a href={data.product?.url} class="flex h-full flex-col no-underline">
		{#if data.productDetails?.stamp}
			<img
				src={data.productDetails.stamp}
				alt="Product stamp"
				class="absolute top-5 right-5 z-10 h-8 w-8 object-contain"
			/>
		{/if}
		{#if data.product?.picture}
			<div class="aspect-video min-h-52 flex-shrink-0 overflow-hidden pt-6">
				<img src={data.product.picture} alt="" class="h-full w-full object-contain" />
			</div>
		{/if}
		<div class="flex flex-grow flex-col space-y-2 p-4">
			<h3 class="h5 text-secondary-700 dark:text-secondary-600 line-clamp-9 pb-1 leading-6 font-bold text-pretty">
				{data.product?.name}
			</h3>
			{#if data.product?.description}
				<p class="line-clamp-5 text-sm leading-normal text-pretty text-neutral-500 dark:text-neutral-400">
					{data.product.description}
				</p>
			{/if}
			{#if data.productDetails?.serialNumber || data.productDetails?.batch}
				<div
					class="border-t border-neutral-200 pt-4 text-xs text-neutral-500 dark:border-neutral-700 dark:text-neutral-400"
				>
					{#if data.productDetails?.serialNumber}
						<p>Serial Number: {data.productDetails.serialNumber}</p>
					{/if}
					{#if data.productDetails?.batch}
						<p>Batch: {data.productDetails.batch}</p>
					{/if}
				</div>
			{/if}
		</div>
	</a>
</div>
