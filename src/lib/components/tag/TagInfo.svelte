<script lang="ts">
	import { getTagContext } from './context'

	const context = getTagContext()
</script>

<div class="tag-info">
	<h3 class="h3">Tag Information</h3>

	<table>
		<tbody>
			{#if context.tag?.program}
				<tr>
					<td><h6 class="h6 text-surface-400">Program</h6></td>
					<td>{context.tag?.program.name}</td>
				</tr>
			{/if}
		</tbody>
	</table>
</div>

<style lang="postcss">
	.tag-info {
		display: flex;
		flex-wrap: wrap;
		row-gap: 1rem;
		justify-content: space-between;
		padding-bottom: 1rem;
	}

	table {
		border-collapse: separate;
		border-spacing: 1em 0;
		margin-left: -1rem;
	}
</style>
