<script lang="ts">
	import { getTagContext } from './context'

	const context = getTagContext()
</script>

{#if context.tag?.product}
	<hr />

	<br />
	<br />

	<div class="header">
		<a href={context.tag?.product.url} target="_blank">
			<h3 class="h3">{context.tag?.product.name}</h3>
		</a>

		{#if context.tag?.productDetails?.stamp}
			<a href={context.tag?.product.url} target="_blank" class="stamp">
				<img src={context.tag?.productDetails?.stamp} loading="lazy" alt="" />
			</a>
		{/if}
	</div>

	<br />

	{#if context.tag?.product.picture}
		<div class="product-image">
			<a href={context.tag?.product.url} target="_blank" class="picture">
				<img src={context.tag?.productDetails?.picture || context.tag?.product.picture} alt="" />
			</a>
		</div>
	{/if}

	{#if context.tag?.product.description}
		<p class="leading-tight whitespace-pre-wrap">{context.tag?.product.description}</p>
	{/if}

	<br />

	{#if context.tag?.productDetails?.serialNumber || context.tag?.productDetails?.serialNumber}
		<br />

		<table>
			<tbody>
				{#if context.tag?.productDetails?.serialNumber}
					<tr>
						<td><h6 class="h6 text-surface-400">Serial Number</h6></td>
						<td>{context.tag?.productDetails?.serialNumber}</td>
					</tr>
				{/if}

				{#if context.tag?.productDetails?.batch}
					<tr>
						<td><h6 class="h6 text-surface-400">Batch</h6></td>
						<td>{context.tag?.productDetails.batch}</td>
					</tr>
				{/if}
			</tbody>
		</table>

		<br />
	{/if}
{/if}

<style>
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		row-gap: 1rem;

		.stamp {
			justify-self: flex-end;
			> img {
				max-width: 140px;
				max-height: 140px;
				object-fit: contain;
			}
		}

		@media only screen and (max-width: 600px) {
			.stamp {
				> img {
					max-width: 70px;
					max-height: 70px;
					object-fit: contain;
				}
			}
		}
	}
	.product-image {
		max-height: 500px;
		position: relative;
		margin-bottom: 2rem;
		text-align: center;

		.picture {
			margin-left: auto;
			margin-right: auto;
			display: inline-block;

			> img {
				max-width: 100%;
				max-height: 100%;
				object-fit: contain;
			}
		}
	}

	a {
		text-decoration: none;
	}

	table {
		border-collapse: separate;
		border-spacing: 1em 0;
		margin-left: -1rem;
	}
</style>
