import { browser } from '$app/environment'
import { page } from '$app/state'

import { appPath } from '$lib/config'

import type { menuNavLinks } from '$lib/links'

class SideBarMenu {
	currentRailCategory = $state<keyof ReturnType<typeof menuNavLinks> | undefined>()

	constructor() {
		if (browser) {
			$effect(() => {
				this.updateRailCategory(page.url)
			})
		}
	}

	updateRailCategory = (url: URL) => {
		if (url) {
			// ex: /basePath/...
			const basePath: string = url.pathname.split('/')[1]
			if (!basePath) return (this.currentRailCategory = 'home')
			// Translate base path to link section
			if (['docs'].includes(basePath)) {
				return (this.currentRailCategory = 'docs')
			}
			if (['settings', 'signout'].includes(basePath)) {
				return (this.currentRailCategory = 'profile')
			}
			if (['backoffice'].includes(basePath)) {
				return (this.currentRailCategory = 'backoffice')
			}
			if (['app'].includes(basePath)) {
				return (this.currentRailCategory = `${appPath}`)
			}
			if (!['blog'].includes(basePath)) {
				return (this.currentRailCategory = 'home')
			}

			return (this.currentRailCategory = 'home')
		}
	}
}

let instance: SideBarMenu | undefined = undefined
export function getSideBarMenu() {
	if (!instance) {
		throw new Error('SideBarMenu is not initialized.')
	}

	return instance
}
export function initSideBarMenu() {
	if (browser && instance && process.env.NODE_ENV !== 'test') {
		console.warn('SideBarMenu is already initialized.')
	}

	return (instance = new SideBarMenu())
}
