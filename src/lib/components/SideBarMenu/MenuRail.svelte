<script lang="ts">
	import { signIn } from '@auth/sveltekit/client'
	import { Avatar, Navigation } from '@skeletonlabs/skeleton-svelte'

	import { page } from '$app/state'

	import { appPath } from '$lib/config'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'

	import { getAuth, internalUsersRoles } from '$lib/services/auth.svelte'
	import { getTheme } from '$lib/services/theme.svelte'

	import { getSideBarMenu } from './state.svelte'

	const {
		hover = 'hover:bg-primary-50 dark:hover:bg-primary-500/20',
	}: {
		hover?: string
	} = $props()

	const auth = getAuth()
	const modal = getModal()
	const sideBarMenu = getSideBarMenu()
	const theme = getTheme()

	function onClickAnchor(): void {
		sideBarMenu.currentRailCategory = undefined
		modal.close()
	}

	function onValueChange(v: string) {
		if (!['theme'].includes(v)) {
			sideBarMenu.currentRailCategory = v
		}
	}

	const organizationId = page.data.organizationId

	const canSeeBackoffice = $derived(
		organizationId
			? auth.profile?.roles.some(
					(role) =>
						(role.organizationId === organizationId && role.role !== 'User') ||
						(internalUsersRoles.includes(role.role) && role.organizationId === null),
				)
			: auth.isInternalOrgUser,
	)
</script>

<Navigation.Rail
	value={sideBarMenu.currentRailCategory}
	{onValueChange}
	background="bg-transparent"
	classes="border-r border-surface-500/30"
>
	{#snippet header()}
		<Navigation.Tile id="home" label="Home" {hover}>
			<iconify-icon icon="radix-icons:home" width="24" height="24" class="mx-auto"></iconify-icon>
		</Navigation.Tile>
	{/snippet}

	{#snippet tiles()}
		<!-- <Navigation.Tile id="blog" href="/blog" label="Blog" {hover} onclick={() => onClickAnchor()}>
			<iconify-icon icon="radix-icons:section" width="24" height="24" class="mx-auto"></iconify-icon>
		</Navigation.Tile>

		<Navigation.Tile id="docs" href="/docs" label="Docs" {hover} onclick={() => onClickAnchor()}>
			<iconify-icon icon="radix-icons:reader" width="24" height="24" class="mx-auto"></iconify-icon>
		</Navigation.Tile> -->
	{/snippet}

	{#snippet footer()}
		{#if auth.isAuthenticated}
			<hr class="hr opacity-30 dark:opacity-100" />

			<Navigation.Tile
				id="app"
				labelClasses="text-warning-700"
				label="App"
				{hover}
				href={`/${appPath}`}
				onclick={() => onClickAnchor()}
			>
				<iconify-icon icon="radix-icons:external-link" width="24" height="24" class="text-warning-700 mx-auto"
				></iconify-icon>
			</Navigation.Tile>

			{#if canSeeBackoffice}
				<Navigation.Tile
					id="backoffice"
					labelClasses="text-warning-700"
					label="Backoffice"
					{hover}
					href="/backoffice"
					onclick={() => onClickAnchor()}
				>
					<iconify-icon icon="radix-icons:external-link" width="24" height="24" class="text-warning-700 mx-auto"
					></iconify-icon>
				</Navigation.Tile>
			{/if}
		{/if}

		<hr class="hr opacity-30 dark:opacity-100" />

		<Navigation.Tile id="theme" label={theme.label} {hover} selected={false} onclick={theme.toggleMode}>
			{#if theme.mode === 'Light'}
				<iconify-icon icon="radix-icons:sun" width="24" height="24" class="mx-auto block"></iconify-icon>
			{:else if theme.mode === 'Dark'}
				<iconify-icon icon="radix-icons:moon" width="24" height="24" class="mx-auto block"></iconify-icon>
			{:else}
				<iconify-icon icon="radix-icons:moon" width="24" height="24" class="mx-auto block"></iconify-icon>
			{/if}
		</Navigation.Tile>

		{#if auth.isAuthenticated}
			<Navigation.Tile
				id="profile"
				label={auth.profile?.name || auth.profile ? `${auth.profile?.givenName} ${auth.profile?.familyName}` : ''}
				{hover}
			>
				{#if auth.profile}
					<Avatar
						src={auth.profile?.picture || undefined}
						name={auth.profile?.name || ''}
						size="size-15"
						classes="mx-auto"
					/>
				{:else}
					<div class="placeholder-circle mx-auto w-10 animate-pulse"></div>
				{/if}
			</Navigation.Tile>
		{:else}
			<Navigation.Tile id="login" labelClasses="text-success-700" label="Login" {hover} onclick={() => signIn('oidc')}>
				<iconify-icon icon="radix-icons:enter" width="24" height="24"></iconify-icon>
			</Navigation.Tile>
		{/if}
	{/snippet}
</Navigation.Rail>
