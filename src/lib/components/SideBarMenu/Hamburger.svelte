<script lang="ts">
	import { getModal } from '$lib/modules/Modal/state.svelte.js'

	const { class: klass, drawer = 'side-menu' }: { class?: string; drawer?: string } = $props()

	const modal = getModal()
</script>

<button
	class="btn btn-lg hover:preset-tonal relative {klass}"
	aria-label="Open navigation"
	onclick={() => modal.open(drawer)}
>
	<iconify-icon icon="radix-icons:hamburger-menu" width="24" height="24" class="ml-0!" noobserver></iconify-icon>
</button>
