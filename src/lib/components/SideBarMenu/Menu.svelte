<script lang="ts">
	import MenuRail from './MenuRail.svelte'
	import SubMenu from './SubMenu.svelte'

	import type { Snippet } from 'svelte'

	const {
		class: Klass,
		menu,
	}: {
		class?: string
		menu?: Snippet
	} = $props()
</script>

<div class="preset-filled-surface-50-950 grid h-full grid-cols-[auto_1fr] {Klass}">
	<MenuRail />

	{#if menu}
		{@render menu()}
	{:else}
		<SubMenu />
	{/if}
</div>
