<script lang="ts">
	import { page } from '$app/state'

	import { menuNavLinks } from '$lib/links'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'

	import { getAuth } from '$lib/services/auth.svelte'

	import { getSideBarMenu } from './state.svelte'

	import type { Snippet } from 'svelte'

	const auth = getAuth()
	const modal = getModal()

	const sideBarMenu = getSideBarMenu()

	const {
		class: klass,
		headerFilter,
		header,
		footer,
	}: { class?: string; headerFilter?: string[]; header?: Snippet; footer?: Snippet } = $props()

	const submenu = $derived(
		menuNavLinks(auth.profile, page.params.orgId ?? page.data.organizationId)[`/${sideBarMenu.currentRailCategory}`],
	)
</script>

<!-- Nav Links -->
{#if submenu}
	<section
		class="border-surface-500/30 flex h-full w-[240px] flex-col space-y-4 overflow-y-auto border-r p-4 sm:w-[280px] {klass} {sideBarMenu.currentRailCategory ===
			'profile' && 'justify-end'}"
	>
		{#if header && (!headerFilter || !sideBarMenu.currentRailCategory || headerFilter.includes(sideBarMenu.currentRailCategory))}
			{@render header?.()}
		{/if}

		{#each submenu as segment, index (index)}
			<!-- Title -->
			{#if segment.title}
				<p class="pl-4 text-xl font-bold">{segment.title}</p>
			{/if}

			<!-- Nav List -->
			<nav class="list-nav">
				<ul>
					{#each segment.list as { href, label, badge, classes, full, onclick, target } (href)}
						{@const active = href && (full ? page.url.pathname === href : page.url.pathname.indexOf(href) === 0)}
						<li>
							<a
								{href}
								class="rounded-container {active
									? 'preset-filled-primary-500'
									: 'hover:bg-primary-50 dark:hover:bg-primary-500/20'} flex w-full items-center gap-4 px-4 py-3 {classes}"
								data-sveltekit-preload-data="hover"
								onclick={() => {
									setTimeout(() => modal.close())
									onclick?.()
								}}
								target={href ? target : undefined}
							>
								<span class="flex-auto">{label}</span>
								{#if badge}<span class="preset-filled-secondary-500 badge">{badge}</span>{/if}
							</a>
						</li>
					{/each}
				</ul>
			</nav>

			<!-- Divider -->
			{#if index + 1 < submenu.length}<hr class="hr opacity-30 dark:opacity-100" />{/if}
		{/each}

		{@render footer?.()}
	</section>
{/if}
