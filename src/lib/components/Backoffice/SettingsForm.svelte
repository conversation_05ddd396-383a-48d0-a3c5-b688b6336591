<script lang="ts">
	import { goto } from '$app/navigation'

	import { createOrganizationForm } from '$lib/forms'
	import {
		DeleteOrganization,
		UpdateOrganization,
		type CreateOrganizationInput,
		type FullOrganizationQuery,
		type UpdateOrganizationInput,
	} from '$lib/graphql/generated/gateway'
	import { FormBuilder, clearFormData, type FieldGroup, type Form } from '$lib/modules/Form'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'

	const {
		organizationId,
		organization,
	}: {
		organizationId: string
		organization: FullOrganizationQuery['organization']
	} = $props()

	const modal = getModal()

	const fields = createOrganizationForm()

	const personalDetailsFields = fields.filter(
		(field) => (field as FieldGroup<CreateOrganizationInput>).title === 'Organization Details',
	) as Form<UpdateOrganizationInput>

	const contactsFields = fields.filter(
		(field) => (field as FieldGroup<CreateOrganizationInput>).title === 'Contacts',
	) as Form<UpdateOrganizationInput>

	const value = clearFormData(organization || {}, [...personalDetailsFields, ...contactsFields])

	async function updateOrganization(data: UpdateOrganizationInput) {
		await UpdateOrganization({
			variables: {
				partialEntity: {
					...data,
					_id: organizationId,
				},
			},
		})
	}

	function confirmDeleteOrganization() {
		modal.open({
			type: 'confirm',
			title: 'Delete Organization',
			submit: 'Delete Organization',
			submitClasses: 'preset-filled-error-500',
			body: `<p>Are you sure you want to delete <strong class="text-error-500">${value.name ?? 'this organization'}</strong> ?</p>
             <p class="mt-4 text-primary-500">This action is irreversible.</p>`,
			onSubmit: async (response: boolean) => {
				if (response) {
					await DeleteOrganization({
						variables: {
							filter: {
								where: {
									_id: organizationId,
								},
							},
						},
					})
					await goto('/')
				}
			},
		})
	}
</script>

<div class="container mx-auto flex flex-col gap-8 px-6 pt-12 pb-20">
	<FormBuilder fields={personalDetailsFields} {value} submit="Update" onSubmit={updateOrganization} />

	<FormBuilder fields={contactsFields} {value} submit="Update" onSubmit={updateOrganization} />

	<div
		class="card bg-noise bg-surface-50-950 border-primary-100 dark:border-primary-800 text-primary-500 space-y-6 border p-8"
	>
		<h4 class="h4">Delete Organization</h4>

		<p>This action is irreversible. Once you delete your organization, there is no going back.</p>

		<button
			class="btn preset-outlined-primary-500 text-primary-500 hover:preset-filled-primary-500"
			onclick={confirmDeleteOrganization}>Delete Organization</button
		>
	</div>
</div>
