<script lang="ts">
	import {
		C<PERSON>R<PERSON>s,
		DeleteRole,
		AsyncInviteUsersSearch,
		CreateInvitation,
		OrganizationRoles,
		UserRole,
		type CreateInvitationInput,
		type UpdateInvitationInput,
	} from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { openFormDrawer } from '$lib/modules/Form'
	import { FieldType, type Form } from '$lib/modules/Form/form.interface'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'
	import ToggleGroup from '$lib/modules/ToggleGroup'
	import { validateEmail } from '$lib/utils'

	import { getAuth } from '$lib/services/auth.svelte'

	const auth = getAuth()
	const modal = getModal()

	const { organizationId }: { organizationId: string } = $props()

	const roles = [UserRole.Admin, UserRole.Owner, UserRole.Accountant, UserRole.User, UserRole.Developer]
	const rolesMap = {
		Admin: [UserRole.Admin, UserRole.Owner],
		Accountant: [UserRole.Accountant],
		User: [UserRole.User],
		Developer: [UserRole.Developer],
	}
	const roleOptions = [
		{ value: 'Admin', label: 'Admin' },
		{ value: 'Accountant', label: 'Accountant' },
		{ value: 'User', label: 'User' },
		{ value: 'Developer', label: 'Developer' },
	]

	interface Users {
		_id: string
		picture?: string | null
		name?: string | null
		roles: Array<{
			_id: string
			role: UserRole
			active: boolean
		}>
	}

	let selectedRoles = $state<string[]>([])

	let formData: UpdateInvitationInput = $state({})

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	const config: DataTableConfig<Users> = {
		columns: [
			{
				type: ColumnType.Image,
				label: 'Picture',
				property: 'picture',
				sortable: '_id',
				href: (row) => (auth?.isInternalUser ? `/backoffice/users/${row._id}` : undefined),
			},
			{
				type: ColumnType.String,
				label: 'Name',
				property: 'name',
				sortable: 'name',
				href: (row) => (auth?.isInternalUser ? `/backoffice/users/${row._id}` : undefined),
			},
			{
				type: ColumnType.ChipList,
				label: 'Roles',
				property: 'roles',
				labelProperty: 'role',
				onclick: (row, item) => {
					if (item.active) {
						modal.open({
							type: 'confirm',
							title: 'Remove Role',
							submit: 'Delete Role',
							submitClasses: 'preset-filled-error-500',
							body: `Are you sure you want to remove the <strong class="text-primary-400">${item.role}</strong> role from <strong class="text-primary-400">${row.name}</strong>?`,
							onSubmit: async (response: boolean) => {
								if (response) {
									await DeleteRole({
										variables: {
											filter: {
												where: {
													role: item.role,
													userId: row._id,
												},
											},
										},
										refetchQueries: ['OrganizationRoles'],
									})
								}
							},
						})
					} else {
						modal.open({
							type: 'confirm',
							title: 'Add Role',
							submit: 'Add Role',
							submitClasses: 'preset-filled-secondary-500',
							body: `Are you sure you want to add the <strong class="text-secondary-500">${item.role}</strong> role to <strong class="text-secondary-500">${row.name}</strong>?`,
							onSubmit: async (response: boolean) => {
								if (response) {
									await CreateRoles({
										variables: {
											partialEntities: [
												{
													role: item.role as unknown as UserRole,
													userId: row._id,
													organizationId,
												},
											],
										},
										refetchQueries: ['OrganizationRoles'],
									})
								}
							},
						})
					}
				},
				color: (_row, item) => {
					if (item?.active) {
						switch (item.role) {
							case UserRole.Owner:
							case UserRole.Admin:
								return 'error'
							default:
								return 'secondary'
						}
					}

					return 'tertiary'
				},
			},
		],
	}

	const query = () =>
		OrganizationRoles({
			variables: {
				filter: {
					limit: filter.limit,
					offset: filter.page * filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
					where: {
						organizationId,
						...(selectedRoles.length > 0
							? { role: { $in: selectedRoles.map((item) => rolesMap[item as keyof typeof rolesMap]).flat() } }
							: {}),
					},
				},
			},
		})

	const result = query()

	$effect(() => {
		$result.query.refetch({
			filter: {
				limit: filter.limit,
				offset: filter.page * filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				where: {
					organizationId,
					...(selectedRoles.length > 0
						? { role: { $in: selectedRoles.map((item) => rolesMap[item as keyof typeof rolesMap]).flat() } }
						: {}),
				},
			},
		})
	})

	const data = $derived.by(() => {
		const fetchedRoles = $result.data?.roles?.nodes || []

		const filteredRoles = fetchedRoles.filter((role) => {
			if (filter.search) {
				const nameMatch = role.user.name?.toLowerCase().includes(filter.search.toLowerCase())
				const emailMatch = role.user.email?.toLowerCase().includes(filter.search.toLowerCase())

				return nameMatch || emailMatch
			} else {
				return true
			}
		})

		const userRoles: Record<string, Users> = {}

		filteredRoles.forEach((node) => {
			const userId = node.userId

			if (!userRoles[userId]) {
				userRoles[userId] = {
					_id: userId,
					name: node.user?.name,
					picture: node.user?.picture,
					roles: roles.map((role) => ({
						_id: node._id,
						role,
						active: node.role === role,
					})),
				}
			} else {
				userRoles[userId].roles = userRoles[userId].roles.map((role) => ({
					...role,
					active: role.active || node.role === role.role,
				}))
			}
		})

		return {
			rows: Object.values(userRoles).slice(filter.page * filter.limit, (filter.page + 1) * filter.limit),
			total: filteredRoles.length,
		}
	})

	const fields = $derived<Form<CreateInvitationInput>>([
		{
			key: 'userIds',
			type: FieldType.AUTOCOMPLETE,
			props: {
				label: 'User',
				placeholder: 'Search by ID, name or email address',
				required: true,
				options: async (search: string) => {
					if (!formData.organizationId) {
						return []
					}

					return AsyncInviteUsersSearch({
						variables: {
							filter: {
								where: {
									...(search
										? {
												$or: [
													{ _id: search },
													{ name: { $regex: search, $options: 'smix' } },
													{ email: { $regex: search, $options: 'smix' } },
												],
											}
										: {}),
								},
								limit: 50,
							},
						},
						fetchPolicy: 'network-only',
					}).then((result) => {
						return result.data.users?.nodes?.length
							? result.data.users?.nodes.map((user) => ({
									label: `${user.name}${user.email ? ` - ${user.email}` : ''}`,
									value: user._id,
									image: user.picture || undefined,
									meta: {
										_id: user._id,
										email: user.email || undefined,
										img: user.picture || undefined,
										name: user.name || undefined,
									},
								}))
							: validateEmail(search)
								? [
										{
											label: search,
											value: search,
											meta: {
												email: search,
											},
										},
									]
								: []
					})
				},
				debounce: 250,
			},
		},
		{
			key: 'roles',
			type: FieldType.SELECT,
			props: {
				label: 'Role',
				options: [
					{ value: UserRole.Owner, label: 'Owner' },
					{ value: UserRole.Admin, label: 'Admin' },
					{ value: UserRole.Accountant, label: 'Accountant' },
					{ value: UserRole.User, label: 'User' },
					{ value: UserRole.Developer, label: 'Developer' },
				],
			},
		},
	])

	function invite() {
		formData.organizationId = organizationId

		openFormDrawer({
			title: 'Invite Members',
			submitLabel: 'Invite',
			fields,
			data: {
				organizationId,
				limit: 1,
				roles: [],
			},
			onSubmit: async (partialEntity: CreateInvitationInput) => {
				await CreateInvitation({
					variables: {
						partialEntity: {
							...partialEntity,
							...(!validateEmail(partialEntity.userIds as unknown as string)
								? { userIds: [partialEntity.userIds] }
								: { userIds: undefined }),
							...(validateEmail(partialEntity.userIds as unknown as string) && { emails: [partialEntity.userIds] }),
						},
					},
					refetchQueries: ['Invitations'],
				})
			},
			onValueChange: (data: CreateInvitationInput) => {
				formData = data
			},
		})
	}
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}>
	{#snippet filterActions()}
		<button class="preset-filled-secondary-500 btn btn-base w-fit" onclick={invite}>Invite</button>
	{/snippet}

	{#snippet filterExtra()}
		<ToggleGroup value={selectedRoles} onValueChange={(e) => (selectedRoles = e.value)} multiple>
			{#each roleOptions as option (option.label)}
				<ToggleGroup.Item value={option.value}>
					{option.label}
				</ToggleGroup.Item>
			{/each}
		</ToggleGroup>
	{/snippet}
</DataTable>
