<script lang="ts">
	import {
		OrganizationOidcClients,
		OrganizationCreateOidcClient,
		type OrganizationOidcClientsQuery,
		type OidcClientCreateInput,
		ApplicationType,
		OrganizationDeleteOidcClient,
	} from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { FieldType, openFormDrawer, type Form } from '$lib/modules/Form'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'

	import ApplicationsModal from '$lib/modals/ApplicationsModal.svelte'

	import type { QueryReturnType } from '$lib/utils'

	type OrganizationOidcClientsResult = QueryReturnType<OrganizationOidcClientsQuery['oidcClients']>

	const modal = getModal()

	const {
		organizationId,
	}: {
		organizationId: string
	} = $props()

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	const config = $derived<DataTableConfig<OrganizationOidcClientsResult>>({
		columns: [
			{
				type: ColumnType.Image,
				label: 'Logo',
				property: 'logo_uri',
			},
			{
				type: ColumnType.String,
				label: 'Client Name',
				property: 'client_name',
				sortable: 'client_name',
			},
			{
				type: ColumnType.String,
				label: 'Client ID',
				property: 'client_id',
			},
			{
				type: ColumnType.String,
				label: 'Grant Types',
				property: 'grant_types',
			},
			{
				type: ColumnType.Date,
				label: 'Created At',
				property: 'createdAt',
				sortable: 'createdAt',
				relative: true,
			},
		],

		actions: [
			{
				action: (items) => {
					modal.open({
						type: 'confirm-list',
						title: 'Delete Application',
						submit: 'Delete Application',
						submitClasses: 'preset-filled-error-500',
						body: `Are you sure you want to delete the selected ${items.length > 1 ? 'applications' : 'application'}?`,
						meta: { items: items.map((item) => ({ label: item.client_name, value: item._id })), disabled: true },
						onSubmit: async (response: boolean) => {
							if (response) {
								for (const item of items) {
									await OrganizationDeleteOidcClient({
										variables: {
											filter: {
												where: {
													_id: item?._id,
												},
											},
										},
										refetchQueries: ['OrganizationOidcClients'],
									})
								}
							}
						},
					})
				},
				icon: 'radix-icons:circle-backslash',
				classes: 'text-primary-500 hover:preset-filled-error-500',
				multiple: true,
			},
		],
	})

	const query = () =>
		OrganizationOidcClients({
			variables: {
				filter: {
					limit: filter.limit,
					offset: filter.page * filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
					where: {
						organizationId,
						...(filter.search ? { client_name: { $regex: filter.search, $options: 'smix' } } : {}),
					},
				},
			},
		})

	const result = query()

	$effect(() => {
		$result.query.refetch({
			filter: {
				limit: filter.limit,
				offset: filter.page * filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				where: {
					organizationId,
					...(filter.search ? { client_name: { $regex: filter.search, $options: 'smix' } } : {}),
				},
			},
		})
	})

	const data = $derived({
		rows: $result.data?.oidcClients?.nodes || [],
		total: $result.data?.oidcClients?.totalCount || 0,
	})

	const fields: Form<Omit<OidcClientCreateInput, 'client_secret'>> = [
		{
			key: 'client_name',
			type: FieldType.INPUT,
			props: {
				label: 'Application Name',
				required: true,
				minLength: 6,
				maxLength: 50,
				description: 'Something users will recognize and trust.',
			},
		},
		{
			key: 'logo_uri',
			type: FieldType.INPUT,
			props: {
				addonRight: {
					img: 'key',
					rounded: true,
					shim: true,
				},
				label: 'Logo URL',
				type: 'url',
				placeholder: 'https://example.com/logo.png',
				description: 'The full URL of your application logo.',
			},
		},
	]

	function registerApplication() {
		openFormDrawer({
			title: 'Register a new OAuth application',
			submitLabel: 'Create Application',
			fields,
			data: {
				organizationId,
				redirect_uris: [],
			},
			onSubmit: async (partialEntity) => {
				const client_secret = generateClientSecret()

				const result = await OrganizationCreateOidcClient({
					variables: {
						partialEntity: {
							...partialEntity,
							application_type: ApplicationType.Web,
							client_secret,
							grant_types: ['client_credentials'],
							redirect_uris: partialEntity.redirect_uris || [],
							response_types: [],
						},
					},
					refetchQueries: ['OrganizationOidcClients'],
				})

				if (result.data?.createOidcClient) {
					modal.open({
						title: 'New Application',
						type: {
							component: ApplicationsModal,
							props: {
								clientId: result.data.createOidcClient.client_id,
								clientSecret: client_secret,
							},
						},
					})
				}
			},
		})
	}

	function generateClientSecret(): string {
		return [...crypto.getRandomValues(new Uint8Array(999))]
			.map((c) => String.fromCharCode(c).replace(/[^a-z0-9]/i, ''))
			.join('')
			.substring(0, 86)
	}
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}>
	{#snippet filterActions()}
		<button class="preset-filled-secondary-500 btn btn-base w-fit" onclick={registerApplication}>
			Register Application
		</button>
	{/snippet}
</DataTable>
