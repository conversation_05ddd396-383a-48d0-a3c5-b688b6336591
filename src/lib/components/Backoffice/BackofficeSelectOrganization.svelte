<script lang="ts">
	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import { SelectOrganization } from '$lib/forms'
	import { menuNavLinks } from '$lib/links'

	import { getAuth, internalUsersRoles } from '$lib/services/auth.svelte'

	import type { OrganizationQuery } from '$lib/graphql/generated/gateway'

	const auth = getAuth()

	async function selectOrganization(org?: OrganizationQuery['organization']) {
		const currentId = $state.snapshot(page.params.orgId)

		const links = menuNavLinks(auth.profile, org?._id)['/backoffice'].flatMap((category) =>
			category.list.map((link) => link.href),
		)
		const newPathname = `/backoffice${org?._id ? `/${org._id}` : ''}${window.location.pathname
			.replace(currentId ? `/${currentId}` : '', '')
			.replace('/backoffice', '')}`

		if (links.includes(newPathname)) {
			goto(`${newPathname}${window.location.search}`)
		} else {
			goto(`/backoffice${org?._id ? `/${org._id}` : ''}${window.location.search}`)
		}
	}

	const numberOfOrgs = $derived.by(() => {
		return (
			[
				...new Set(
					auth.profile?.roles
						.filter(
							(role) =>
								(role.organizationId && role.role !== 'User') ||
								(internalUsersRoles.includes(role.role) && role.organizationId === null),
						)
						.map((role) => role.organizationId),
				),
			].length ?? 0
		)
	})
</script>

{#if (auth?.isInternalUser || numberOfOrgs > 1) && !page?.data?.organizationId}
	<SelectOrganization
		value={page.data.organizationId || page.params.orgId}
		onValueChange={selectOrganization}
		selectionBehavior="clear"
	/>
{/if}
