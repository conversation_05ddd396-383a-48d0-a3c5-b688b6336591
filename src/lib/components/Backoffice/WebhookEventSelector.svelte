<script lang="ts">
	let {
		value = $bindable([]),
		onValueChange,
		...rest
	}: {
		value?: string[]
		onValueChange?: (value: string[]) => void
	} = $props()

	const eventGroups = [
		{
			title: 'Activation',
			prefix: 'activation',
			events: ['created', 'updated', 'deleted', 'opt-in', 'opt-out'],
		},
		{
			title: 'Product',
			prefix: 'product',
			events: ['created', 'updated', 'deleted'],
		},
		{
			title: 'Program',
			prefix: 'program',
			events: ['created', 'updated', 'deleted'],
		},
		{
			title: 'Tag',
			prefix: 'tag',
			events: ['created', 'updated', 'deleted', 'tap', 'claimed', 'released'],
		},
	]

	function toggleEvent(prefix: string, event: string) {
		const pattern = `${prefix}.${event}`
		const allPattern = `${prefix}.*`

		if (value.includes(allPattern)) {
			return
		}

		const index = value.indexOf(pattern)
		if (index === -1 && !value.includes(allPattern)) {
			value.push(pattern)
		} else {
			value.splice(index, 1)
		}

		onValueChange?.(value)
	}

	function toggleAllEvents(prefix: string) {
		const allPattern = `${prefix}.*`
		const hasAll = value.includes(allPattern)

		if (hasAll) {
			value = value.filter((v) => !v.startsWith(`${prefix}.`))
		} else {
			value = [...value.filter((v) => !v.startsWith(`${prefix}.`)), allPattern]
		}

		onValueChange?.(value)
	}
</script>

<div class="space-y-4" {...rest}>
	{#each eventGroups as group (group.title)}
		<div class="card bg-noise bg-surface-50-950 border-surface-100 dark:border-surface-800 space-y-3 border p-4">
			<h5 class="h5 text-surface-900 dark:text-surface-100">{group.title}</h5>

			<div class="flex flex-wrap gap-4">
				{#each group.events as event (event)}
					<button
						type="button"
						class="btn hover:preset-filled {value.includes(`${group.prefix}.${event}`)
							? 'preset-filled'
							: 'preset-tonal'}"
						onclick={() => toggleEvent(group.prefix, event)}
						disabled={value.includes(`${group.prefix}.*`)}
					>
						{event}
					</button>
				{/each}

				<button
					type="button"
					class="btn hover:preset-filled-secondary-500 {value.includes(`${group.prefix}.*`)
						? 'preset-filled-secondary-500'
						: 'preset-tonal-secondary'}"
					onclick={() => toggleAllEvents(group.prefix)}
				>
					All Events
				</button>
			</div>
		</div>
	{/each}
</div>
