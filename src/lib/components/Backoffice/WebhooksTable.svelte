<script lang="ts">
	import { createWebhookForm } from '$lib/forms'
	import {
		<PERSON>hooks,
		CreateWebhook,
		DeleteWebhook,
		type WebhooksQuery,
		type CreateWebhookInput,
		UpdateWebhook,
		type UpdateWebhookInput,
	} from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { openFormDrawer } from '$lib/modules/Form'
	import { clearFormData } from '$lib/modules/Form/form.interface'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'

	type WebhookNode = WebhooksQuery['webhooks']['nodes'][number]

	const { organizationId }: { organizationId: string } = $props()

	const modal = getModal()

	let formData = $state<Partial<CreateWebhookInput>>({})

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	const fields = $derived.by(() => {
		if (!formData) {
			// Do Nothing
		}
		return createWebhookForm()
	})

	const config = $derived<DataTableConfig<WebhookNode>>({
		columns: [
			{
				type: ColumnType.String,
				label: 'URL',
				property: 'url',
				sortable: 'url',
			},
			{
				type: ColumnType.Boolean,
				label: 'Active',
				property: 'active',
			},
			{
				type: ColumnType.ChipList,
				label: 'Subscribe To',
				property: 'subscribeTo',
				color: () => 'preset-filled',
			},
			{
				type: ColumnType.Date,
				label: 'Created At',
				property: 'createdAt',
				sortable: 'createdAt',
				relative: true,
			},
			{
				type: ColumnType.Date,
				label: 'Updated At',
				property: 'updatedAt',
				sortable: 'updatedAt',
				relative: true,
				hide: true,
			},
		],
		actions: [
			{
				action: (items) => {
					if (items.length === 1) {
						const webhook = items[0]

						openFormDrawer({
							title: 'Edit Webhook',
							submitLabel: 'Update',
							fields,
							data: clearFormData(webhook, fields),
							onSubmit: async (partialEntity: UpdateWebhookInput) => {
								await UpdateWebhook({
									variables: {
										partialEntity: {
											...partialEntity,
											_id: webhook._id,
										},
									},
									refetchQueries: ['Webhooks'],
								})
							},
						})
					}
				},
				icon: 'radix-icons:pencil-1',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
			},
			{
				action: (items) => {
					modal.open({
						type: 'confirm-list',
						title: 'Delete Webhook',
						submitClasses: 'preset-filled-error-500',
						body: `Are you sure you want to delete the selected ${items.length > 1 ? 'webhooks' : 'webhook'}?`,
						meta: { items: items.map((item) => ({ label: item.url, value: item._id })), disabled: true },
						onSubmit: async (response: boolean) => {
							if (response) {
								for (const item of items) {
									await DeleteWebhook({
										variables: {
											filter: {
												where: {
													_id: item._id,
												},
											},
										},
										refetchQueries: ['Webhooks'],
									})
								}
							}
						},
					})
				},
				icon: 'radix-icons:circle-backslash',
				classes: 'text-primary-500 hover:preset-filled-error-500',
				multiple: true,
			},
		],
	})

	const query = () =>
		Webhooks({
			variables: {
				filter: {
					limit: filter.limit,
					offset: filter.page * filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
					where: {
						parentId: organizationId,
						parent: 'Organization',
						...(filter.search
							? {
									$or: [
										{ _id: filter.search },
										{ url: { $regex: filter.search, $options: 'smix' } },
										{ subscribeTo: { $regex: filter.search, $options: 'smix' } },
									],
								}
							: {}),
					},
				},
			},
		})

	const result = query()

	$effect(() => {
		$result.query.refetch({
			filter: {
				limit: filter.limit,
				offset: filter.page * filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				where: {
					parentId: organizationId,
					parent: 'Organization',
					...(filter.search
						? {
								$or: [
									{ _id: filter.search },
									{ url: { $regex: filter.search, $options: 'smix' } },
									{ subscribeTo: { $regex: filter.search, $options: 'smix' } },
								],
							}
						: {}),
				},
			},
		})
	})

	const data = $derived({
		rows: $result.data?.webhooks?.nodes || [],
		total: $result.data?.webhooks?.totalCount || 0,
	})

	function createWebhook() {
		formData = {
			parentId: organizationId,
			parent: 'Organization',
			active: true,
			subscribeTo: [],
		}

		openFormDrawer({
			title: 'Create New Webhook',
			submitLabel: 'Create Webhook',
			fields,
			data: formData,
			onValueChange: (data: CreateWebhookInput) => {
				formData = data
			},
			onSubmit: async (partialEntity: CreateWebhookInput) => {
				await CreateWebhook({
					variables: {
						partialEntity: {
							...partialEntity,
						},
					},
					refetchQueries: ['Webhooks'],
				})
			},
		})
	}
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}>
	{#snippet filterActions()}
		<button class="preset-filled-secondary-500 btn btn-base w-fit" onclick={createWebhook}>Create Webhook</button>
	{/snippet}
</DataTable>
