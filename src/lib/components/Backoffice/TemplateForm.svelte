<script lang="ts">
	import { invalidate } from '$app/navigation'

	import {
		UpdateOrganization,
		type FullOrganizationQuery,
		type UpdateOrganizationInput,
	} from '$lib/graphql/generated/gateway'
	import { FieldType, FormBuilder, clearFormData, type Form } from '$lib/modules/Form'

	const {
		organizationId,
		organization,
	}: {
		organizationId: string
		organization: FullOrganizationQuery['organization']
	} = $props()

	const fields: Form<UpdateOrganizationInput> = [
		{
			title: 'Background',
			fields: [
				{
					key: 'template.backgroundColor',
					type: FieldType.INPUT,
					props: {
						label: 'Background Color',
						placeholder:
							'white or #ffffff or rgba(255, 255, 255, 1) or linear-gradient(180deg, rgba(122, 172, 210, 1) 0%, rgba(29, 49, 82, 1) 100%)',
						description: 'This color will be used as the background for all pages.',
					},
				},
				{
					key: 'template.backgroundImageUrl',
					type: FieldType.INPUT,
					props: {
						type: 'url',
						label: 'Background Image URL',
						placeholder: 'https://example.com/image.png',
						description: 'This image will be used as the background for marketing and authentication pages.',
					},
				},
			],
		},
		{
			title: 'Colors',
			description: 'All colors should be provided as valid text, hex, rgba, or hsla values.',
			fields: [
				{
					key: 'template.textColor',
					type: FieldType.INPUT,
					props: {
						label: 'Text Color',
						description: 'This color will be used as the text color for main titles, buttons, and links.',
					},
				},
				{
					key: 'template.primaryColor',
					type: FieldType.INPUT,
					props: {
						label: 'Primary Color',
						description: 'This color will be used as the background for primary buttons and links.',
					},
				},
				{
					key: 'template.primaryTextColor',
					type: FieldType.INPUT,
					props: {
						label: 'Primary Text Color',
						description: 'This color will be used as the text color for primary buttons and links.',
					},
				},
				{
					key: 'template.secondaryColor',
					type: FieldType.INPUT,
					props: {
						label: 'Secondary Color',
						description: 'This color will be used as the background for secondary buttons and links.',
					},
				},
				{
					key: 'template.secondaryTextColor',
					type: FieldType.INPUT,
					props: {
						label: 'Secondary Text Color',
						description: 'This color will be used as the text color for secondary buttons and links.',
					},
				},
				{
					key: 'template.tertiaryColor',
					type: FieldType.INPUT,
					props: {
						label: 'tertiary Color',
						description: 'This color will be used as the background for tertiary buttons and links.',
					},
				},
				{
					key: 'template.tertiaryTextColor',
					type: FieldType.INPUT,
					props: {
						label: 'tertiary Text Color',
						description: 'This color will be used as the text color for tertiary buttons and links.',
					},
				},
			],
		},
		{
			title: 'Custom Branding',
			fields: [
				{
					key: 'template.footerMessage',
					type: FieldType.INPUT,
					props: {
						label: 'Footer Message',
						placeholder: 'Powered by [your organization name]',
						description: 'This message will be displayed in the footer of marketing and authentication pages.',
					},
				},
			],
		},
		{
			title: 'Support Integrations',
			fields: [
				{
					key: 'template.zendeskKey',
					type: FieldType.INPUT,
					props: {
						label: 'Zendesk Key',
						placeholder: '1234567890',
						description: 'This key will be used to integrate with Zendesk for customer support.',
					},
				},
			],
		},
	]

	const value = clearFormData(organization || {}, fields)

	async function updateOrganization(data: UpdateOrganizationInput) {
		await UpdateOrganization({
			variables: {
				partialEntity: {
					...data,
					_id: organizationId,
				},
			},
		})
		await invalidate('full:organization')
	}
</script>

<div class="container mx-auto flex flex-col gap-8 px-6 pt-12 pb-20">
	<FormBuilder {fields} {value} submit="Update" onSubmit={updateOrganization} />
</div>
