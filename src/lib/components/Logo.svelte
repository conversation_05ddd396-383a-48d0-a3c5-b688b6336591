<script lang="ts">
	export let type: 'icon' | 'logo' | 'logo-connect' | 'logo-square' | 'logo-square-framed' = 'icon'
</script>

{#if type === 'icon'}
	<svg
		class="logo {$$props.class || ''}"
		data-name="icon"
		version="1.1"
		viewBox="0 0 30.540001 30.790001"
		height="100%"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			class="cls-1"
			d="M 14.25,0 H 0 v 6.79 0 1.59 h 21.56 c 0.36,-0.05 2.19,-0.19 2.43,1.58 0.22,1.64 0.65,5.71 0.94,8.41 l 0.19,1.74 c 0.12,1.12 0.11,1.14 -0.3,1.62 -0.97,1.15 -1.96,1.62 -3.93,0.38 -0.72,-0.45 -1.39,-0.88 -1.99,-1.28 -0.13,-0.09 -0.22,-0.15 -0.26,-0.17 -2.55,-1.69 -3.96,-2.78 -3.98,-2.79 l -0.05,-0.04 c 0,0 -0.45,-0.31 -0.52,-0.82 -0.04,-0.37 0.12,-0.8 0.46,-1.28 0.55,0.19 1.16,0.31 1.81,0.27 0.8,-0.03 1.56,-0.27 2.25,-0.71 0.21,2.4 1.58,3.29 1.65,3.33 l 0.86,-1.34 c 0,0 -1.15,-0.83 -0.9,-3.32 0.04,-0.4 -0.18,-0.79 -0.55,-0.95 -0.37,-0.17 -0.81,-0.07 -1.08,0.23 -0.69,0.76 -1.43,1.14 -2.26,1.18 -1.61,0.07 -3.08,-1.28 -3.09,-1.29 l -1.1,1.15 c 0,0 0.39,0.37 0.97,0.75 -0.49,0.75 -0.69,1.49 -0.59,2.21 0.15,1.13 0.98,1.76 1.19,1.91 0.21,0.17 1.71,1.3 4.32,3.02 0.44,0.32 1.43,1.2 1.43,2.6 0,0.58 -0.88,1.27 -1.45,1.29 -0.02,0 -0.03,0 -0.05,0 -0.82,0 -1.47,-0.48 -2.29,-1.08 L 15.52,24.87 C 14.56,24.16 12.67,22.93 10.86,22.61 8.81,22.25 8.16,21.7 8.03,21.57 L 8.01,21.5 7.85,21.37 c 0,0 -1.07,-0.89 -2.52,-2.91 L 5.28,18.39 C 3.53,16.43 1.48,16.49 1.41,16.49 L 0,16.51 0.02,17.72 v 0 13.07 h 14.25 c 10.07,0 16.27,-7.48 16.27,-15.39 0,-7.91 -6.2,-15.39 -16.28,-15.39 z"
			id="path8"
		/>
	</svg>
{:else if type === 'logo'}
	<svg
		class="logo {$$props.class || ''}"
		data-name="logo"
		version="1.1"
		viewBox="0 0 101.79 31.01"
		height="100%"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g id="g1945" transform="translate(-57.11,-97.49)">
			<path
				class="cls-1"
				d="M 71.36,97.7 H 57.11 v 6.79 0 1.59 h 21.56 c 0.36,-0.05 2.19,-0.19 2.43,1.58 0.22,1.64 0.65,5.71 0.94,8.41 l 0.19,1.74 c 0.12,1.12 0.11,1.14 -0.3,1.62 -0.97,1.15 -1.96,1.62 -3.93,0.38 -0.72,-0.45 -1.39,-0.88 -1.99,-1.28 -0.13,-0.09 -0.22,-0.15 -0.26,-0.17 -2.55,-1.69 -3.96,-2.78 -3.98,-2.79 l -0.05,-0.04 c 0,0 -0.45,-0.31 -0.52,-0.82 -0.04,-0.37 0.12,-0.8 0.46,-1.28 0.55,0.19 1.16,0.31 1.81,0.27 0.8,-0.03 1.56,-0.27 2.25,-0.71 0.21,2.4 1.58,3.29 1.65,3.33 l 0.86,-1.34 c 0,0 -1.15,-0.83 -0.9,-3.32 0.04,-0.4 -0.18,-0.79 -0.55,-0.95 -0.37,-0.17 -0.81,-0.07 -1.08,0.23 -0.69,0.76 -1.43,1.14 -2.26,1.18 -1.61,0.07 -3.08,-1.28 -3.09,-1.29 l -1.1,1.15 c 0,0 0.39,0.37 0.97,0.75 -0.49,0.75 -0.69,1.49 -0.59,2.21 0.15,1.13 0.98,1.76 1.19,1.91 0.21,0.17 1.71,1.3 4.32,3.02 0.44,0.32 1.43,1.2 1.43,2.6 0,0.58 -0.88,1.27 -1.45,1.29 -0.02,0 -0.03,0 -0.05,0 -0.82,0 -1.47,-0.48 -2.29,-1.08 l -0.15,-0.11 c -0.96,-0.71 -2.85,-1.94 -4.66,-2.26 -2.05,-0.36 -2.7,-0.91 -2.83,-1.04 l -0.02,-0.07 -0.16,-0.13 c 0,0 -1.07,-0.89 -2.52,-2.91 l -0.05,-0.07 c -1.75,-1.96 -3.8,-1.9 -3.87,-1.9 l -1.41,0.02 0.02,1.21 v 0 13.07 h 14.25 c 10.07,0 16.27,-7.48 16.27,-15.39 0,-7.91 -6.2,-15.39 -16.28,-15.39 z"
				id="path1943"
			/>
		</g>
		<path
			class="cls-1"
			d="M 31.35,31 44.46,0 h 8.23 L 65.8,31 H 56.65 L 54.41,25.5 H 42.53 L 40.33,31 H 31.36 Z M 45.03,18.87 h 6.91 l -3.43,-8.75 -3.47,8.75 z"
			id="path1947"
		/>
		<path
			class="cls-1"
			d="M 71.03,31 V 0.22 h 17.96 c 7.79,0 12.8,4 12.8,10.82 v 0.09 c 0,7.26 -5.59,11.08 -13.24,11.08 h -8.98 v 8.8 h -8.53 z m 8.54,-15.48 h 8.81 c 3.04,0 4.88,-1.58 4.88,-4 v -0.09 c 0,-2.64 -1.85,-4.05 -4.93,-4.05 h -8.76 z"
			id="path1949"
		/>
	</svg>
{:else if type === 'logo-connect'}
	<svg
		class="logo logo-connect {$$props.class || ''}"
		data-name="logo-connect"
		version="1.1"
		viewBox="0 0 112.77361 40.309963"
		height="100%"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g id="g16" transform="translate(-61.3,-87.850036)">
			<g id="g10">
				<path
					class="cls-1"
					d="M 75.55,97.35 H 61.3 v 6.79 0 1.59 h 21.56 c 0.36,-0.05 2.19,-0.19 2.43,1.58 0.22,1.64 0.65,5.71 0.94,8.41 l 0.19,1.74 c 0.12,1.12 0.11,1.14 -0.3,1.62 -0.97,1.15 -1.96,1.62 -3.93,0.38 -0.72,-0.45 -1.39,-0.88 -1.99,-1.28 -0.13,-0.09 -0.22,-0.15 -0.26,-0.17 -2.55,-1.69 -3.96,-2.78 -3.98,-2.79 l -0.05,-0.04 c 0,0 -0.45,-0.31 -0.52,-0.82 -0.04,-0.37 0.12,-0.8 0.46,-1.28 0.55,0.19 1.16,0.31 1.81,0.27 0.8,-0.03 1.56,-0.27 2.25,-0.71 0.21,2.4 1.58,3.29 1.65,3.33 l 0.86,-1.34 c 0,0 -1.15,-0.83 -0.9,-3.32 0.04,-0.4 -0.18,-0.79 -0.55,-0.95 -0.37,-0.17 -0.81,-0.07 -1.08,0.23 -0.69,0.76 -1.43,1.14 -2.26,1.18 -1.61,0.07 -3.08,-1.28 -3.09,-1.29 l -1.1,1.15 c 0,0 0.39,0.37 0.97,0.75 -0.49,0.75 -0.69,1.49 -0.59,2.21 0.15,1.13 0.98,1.76 1.19,1.91 0.21,0.17 1.71,1.3 4.32,3.02 0.44,0.32 1.43,1.2 1.43,2.6 0,0.58 -0.88,1.27 -1.45,1.29 -0.02,0 -0.03,0 -0.05,0 -0.82,0 -1.47,-0.48 -2.29,-1.08 l -0.15,-0.11 c -0.96,-0.71 -2.85,-1.94 -4.66,-2.26 -2.05,-0.36 -2.7,-0.91 -2.83,-1.04 l -0.02,-0.07 -0.16,-0.13 c 0,0 -1.07,-0.89 -2.52,-2.91 l -0.05,-0.07 c -1.75,-1.96 -3.8,-1.9 -3.87,-1.9 l -1.41,0.02 0.02,1.21 v 0 13.07 h 14.25 c 10.07,0 16.27,-7.48 16.27,-15.39 0,-7.91 -6.2,-15.39 -16.28,-15.39 z"
					id="path8"
				/>
			</g>
			<path
				class="cls-1"
				d="m 92.65,128.15 13.11,-31 h 8.23 l 13.11,31 h -9.15 l -2.24,-5.5 h -11.88 l -2.2,5.5 h -8.97 z m 13.68,-12.14 h 6.91 l -3.43,-8.75 -3.47,8.75 z"
				id="path12"
			/>
			<path
				class="cls-1"
				d="M 132.33,128.15 V 97.37 h 17.96 c 7.79,0 12.8,4 12.8,10.82 v 0.09 c 0,7.26 -5.59,11.08 -13.24,11.08 h -8.98 v 8.8 h -8.53 z m 8.54,-15.48 h 8.81 c 3.04,0 4.88,-1.58 4.88,-4 v -0.09 c 0,-2.64 -1.85,-4.05 -4.93,-4.05 h -8.76 z"
				id="path14"
			/>
		</g>
		<path
			class="cls-1"
			d="m 112.48,23.709964 c 0.76,-4.53 0.06,-9.32 -2.28,-13.62 -0.16,-0.3000001 -0.33,-0.5900001 -0.51,-0.8800001 -0.18,-0.29 -0.36,-0.58 -0.55,-0.86 -2.74,-4.05 -6.68,-6.87 -11.05,-8.27999998 -0.8,-0.26 -1.65,0.23 -1.85,1.04999998 -0.09,0.39 -0.02,0.78 0.17,1.1 0.18,0.29 0.46,0.52 0.81,0.63 4.02,1.3 7.61,3.99 9.98,7.8800001 2.37,3.89 3.11,8.32 2.42,12.48 -0.13,0.76 0.35,1.48 1.09,1.66 0.39,0.1 0.79,0.02 1.11,-0.17 0.34,-0.21 0.6,-0.56 0.67,-0.99 z"
			id="path18"
		/>
		<path
			class="cls-1"
			d="m 106.8,22.269964 c 0,0 0,-0.01 0,-0.02 0,-0.01 0,-0.02 0,-0.03 0,-0.06 0.02,-0.12 0.02,-0.18 0,-0.05 0.01,-0.11 0.02,-0.16 0.02,-0.21 0.04,-0.42 0.06,-0.63 0,-0.04 0,-0.08 0,-0.12 0,-0.06 0,-0.12 0,-0.18 0.02,-0.41 0.02,-0.82 0,-1.23 0,-0.05 0,-0.1 0,-0.15 0,-0.01 0,-0.02 0,-0.03 0,-0.05 0,-0.09 0,-0.14 0,-0.04 0,-0.08 0,-0.12 0,-0.04 0,-0.08 0,-0.12 -0.01,-0.19 -0.03,-0.37 -0.05,-0.56 0,-0.05 -0.01,-0.1 -0.02,-0.14 0,-0.06 -0.01,-0.12 -0.02,-0.18 0,-0.05 -0.01,-0.11 -0.02,-0.16 -0.04,-0.32 -0.1,-0.65 -0.17,-0.97 0,-0.05 -0.02,-0.09 -0.03,-0.14 -0.03,-0.15 -0.07,-0.3 -0.1,-0.44 -0.02,-0.07 -0.03,-0.13 -0.05,-0.2 -0.01,-0.05 -0.03,-0.1 -0.04,-0.15 -0.01,-0.05 -0.02,-0.09 -0.04,-0.14 -0.02,-0.07 -0.04,-0.13 -0.06,-0.2 -0.01,-0.05 -0.03,-0.09 -0.04,-0.14 -0.05,-0.16 -0.1,-0.31 -0.15,-0.47 -0.02,-0.05 -0.04,-0.1 -0.05,-0.16 -0.01,-0.04 -0.03,-0.07 -0.04,-0.11 -0.01,-0.03 -0.02,-0.06 -0.03,-0.09 -0.01,-0.04 -0.03,-0.07 -0.04,-0.11 -0.09,-0.23 -0.18,-0.46 -0.28,-0.69 -0.02,-0.05 -0.04,-0.09 -0.06,-0.14 -0.02,-0.05 -0.05,-0.11 -0.07,-0.16 -0.03,-0.06 -0.06,-0.13 -0.09,-0.19 -0.03,-0.06 -0.06,-0.12 -0.08,-0.17 -0.05,-0.11 -0.11,-0.21 -0.16,-0.32 -0.02,-0.04 -0.04,-0.08 -0.06,-0.12 -0.05,-0.09 -0.1,-0.18 -0.15,-0.27 -0.02,-0.04 -0.05,-0.09 -0.08,-0.13 -0.04,-0.06 -0.07,-0.13 -0.11,-0.19 -0.03,-0.06 -0.07,-0.11 -0.1,-0.17 -0.03,-0.06 -0.07,-0.11 -0.1,-0.17 -0.04,-0.06 -0.08,-0.12 -0.12,-0.19 -0.03,-0.04 -0.06,-0.09 -0.08,-0.13 -0.06,-0.09 -0.12,-0.18 -0.18,-0.26 -0.02,-0.04 -0.05,-0.07 -0.07,-0.11 -0.07,-0.1 -0.14,-0.19 -0.21,-0.29 -0.04,-0.05 -0.08,-0.1 -0.12,-0.16 -0.04,-0.06 -0.09,-0.11 -0.13,-0.17 -0.04,-0.05 -0.07,-0.09 -0.11,-0.14 -0.03,-0.04 -0.07,-0.08 -0.1,-0.12 -0.16,-0.19 -0.32,-0.38 -0.48,-0.5600001 -0.02,-0.03 -0.05,-0.06 -0.08,-0.08 -0.02,-0.02 -0.04,-0.05 -0.06,-0.07 -0.03,-0.03 -0.05,-0.06 -0.08,-0.09 -0.04,-0.04 -0.08,-0.08 -0.11,-0.12 -0.11,-0.12 -0.23,-0.24 -0.35,-0.35 -0.03,-0.03 -0.07,-0.07 -0.1,-0.1 -0.05,-0.05 -0.09,-0.09 -0.14,-0.13 -0.04,-0.03 -0.07,-0.07 -0.11,-0.1 -0.04,-0.03 -0.07,-0.07 -0.11,-0.1 -0.05,-0.05 -0.11,-0.1 -0.16,-0.14 -0.07,-0.06 -0.14,-0.12 -0.22,-0.19 -0.03,-0.03 -0.07,-0.06 -0.1,-0.09 -0.3,-0.25 -0.6,-0.48 -0.92,-0.7 -0.04,-0.03 -0.09,-0.06 -0.13,-0.09 -0.05,-0.03 -0.1,-0.07 -0.15,-0.1 -0.04,-0.03 -0.08,-0.05 -0.12,-0.08 -0.16,-0.1 -0.32,-0.2 -0.48,-0.3 -0.03,-0.02 -0.07,-0.04 -0.1,-0.06 -0.03,-0.02 -0.07,-0.04 -0.1,-0.06 -0.04,-0.02 -0.08,-0.05 -0.12,-0.07 0,0 -0.02,-0.01 -0.03,-0.02 -0.04,-0.02 -0.08,-0.05 -0.13,-0.07 -0.23,-0.13 -0.47,-0.25 -0.71,-0.37 -0.05,-0.02 -0.1,-0.05 -0.14,-0.07 -0.05,-0.02 -0.1,-0.05 -0.14,-0.07 -0.09,-0.04 -0.18,-0.08 -0.27,-0.12 -0.04,-0.02 -0.07,-0.03 -0.11,-0.05 -0.07,-0.03 -0.14,-0.06 -0.2,-0.09 -0.05,-0.02 -0.1,-0.04 -0.15,-0.06 -0.05,-0.02 -0.1,-0.04 -0.15,-0.06 -0.04,-0.02 -0.09,-0.03 -0.13,-0.05 -0.1,-0.04 -0.21,-0.08 -0.31,-0.12 0,0 0,0 0,0 -0.81,-0.29 -1.7,0.18 -1.92,1.01 0,0 0,0.01 0,0.02 0,0 0,0.01 0,0.02 -0.09,0.38 -0.02,0.77 0.18,1.09 0.17,0.28 0.43,0.49 0.75,0.61 2.59,0.92 4.89,2.7100001 6.43,5.2300001 1.54,2.52 2.06,5.39 1.69,8.11 -0.1,0.73 0.37,1.42 1.08,1.6 h 0.02 c 0,0 0.01,0 0.02,0 0.39,0.09 0.77,0.02 1.08,-0.18 0.36,-0.22 0.63,-0.59 0.69,-1.05 z"
			id="path20"
		/>
	</svg>
{:else if type === 'logo-square'}
	<svg
		class="logo logo-square {$$props.class || ''}"
		data-name="logo-square"
		version="1.1"
		viewBox="0 0 92.129997 68.699997"
		height="100%"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g id="g18" transform="translate(-59.61,-81.67)">
			<g id="g12">
				<path
					class="cls-1"
					d="m 72.52,81.86 h -12.9 v 6.15 0 1.44 h 19.51 c 0.33,-0.05 1.98,-0.17 2.2,1.43 0.2,1.49 0.59,5.17 0.85,7.61 l 0.17,1.58 c 0.11,1.01 0.1,1.03 -0.27,1.46 -0.88,1.04 -1.78,1.46 -3.55,0.34 -0.65,-0.41 -1.26,-0.8 -1.8,-1.16 -0.12,-0.08 -0.2,-0.13 -0.24,-0.16 -2.31,-1.53 -3.58,-2.52 -3.6,-2.53 l -0.05,-0.04 c 0,0 -0.41,-0.28 -0.47,-0.74 -0.04,-0.33 0.11,-0.73 0.42,-1.15 0.5,0.17 1.05,0.28 1.64,0.25 0.73,-0.03 1.41,-0.24 2.03,-0.64 0.19,2.18 1.43,2.98 1.49,3.02 l 0.78,-1.21 c 0,0 -1.05,-0.75 -0.82,-3.01 0.04,-0.37 -0.17,-0.71 -0.5,-0.86 -0.34,-0.15 -0.73,-0.07 -0.98,0.21 -0.62,0.69 -1.29,1.03 -2.05,1.06 -1.46,0.06 -2.78,-1.16 -2.8,-1.17 l -0.99,1.04 c 0,0 0.36,0.34 0.88,0.68 -0.44,0.68 -0.62,1.35 -0.53,2 0.14,1.03 0.89,1.6 1.08,1.73 0.19,0.15 1.54,1.18 3.91,2.74 0.39,0.29 1.3,1.08 1.3,2.35 0,0.53 -0.79,1.15 -1.31,1.16 -0.02,0 -0.03,0 -0.05,0 -0.74,0 -1.33,-0.43 -2.07,-0.98 l -0.14,-0.1 c -0.87,-0.64 -2.58,-1.75 -4.22,-2.04 -1.85,-0.33 -2.45,-0.83 -2.56,-0.94 l -0.02,-0.06 -0.15,-0.12 c 0,0 -0.97,-0.81 -2.28,-2.63 l -0.05,-0.06 c -1.59,-1.78 -3.44,-1.72 -3.5,-1.72 l -1.27,0.02 0.02,1.1 v 0 11.83 h 12.9 c 9.12,0 14.73,-6.77 14.73,-13.93 0,-7.16 -5.61,-13.93 -14.73,-13.93 z"
					id="path10"
				/>
			</g>
			<path
				class="cls-1"
				d="M 87.99,109.73 99.85,81.67 h 7.44 l 11.86,28.06 h -8.28 l -2.03,-4.97 H 98.09 l -1.99,4.97 h -8.12 z m 12.38,-10.99 h 6.25 l -3.1,-7.92 -3.14,7.92 z"
				id="path14"
			/>
			<path
				class="cls-1"
				d="M 123.91,109.73 V 81.87 h 16.25 c 7.05,0 11.58,3.62 11.58,9.79 v 0.08 c 0,6.57 -5.06,10.03 -11.98,10.03 h -8.13 v 7.96 z m 7.72,-14.01 h 7.97 c 2.75,0 4.42,-1.43 4.42,-3.62 v -0.08 c 0,-2.39 -1.67,-3.66 -4.46,-3.66 h -7.93 z"
				id="path16"
			/>
		</g>
		<g id="g2450">
			<path
				id="path6"
				class="cls-1"
				d="M 16.300781 34.439453 C 7.3207902 34.439453 0.009765625 41.750478 0.009765625 50.730469 C 0.009765625 59.71046 7.3207902 67.019531 16.300781 67.019531 L 62.539062 67.019531 C 71.519054 67.019531 78.830078 59.71046 78.830078 50.730469 C 78.830078 41.750478 71.519054 34.439453 62.539062 34.439453 L 16.300781 34.439453 z M 36.550781 42.869141 L 40.720703 42.869141 L 47.369141 58.599609 L 42.730469 58.599609 L 41.589844 55.810547 L 35.560547 55.810547 L 34.439453 58.599609 L 29.900391 58.599609 L 36.550781 42.869141 z M 15.25 42.880859 L 30.650391 42.880859 L 30.650391 46.550781 L 25.330078 46.550781 L 25.330078 58.480469 L 21.060547 58.480469 L 21.060547 46.550781 L 15.25 46.550781 L 15.25 42.880859 z M 50.029297 42.880859 L 59.140625 42.880859 C 63.090621 42.880859 65.630859 44.909144 65.630859 48.369141 L 65.630859 48.410156 C 65.630859 52.090153 62.800152 54.029297 58.910156 54.029297 L 54.349609 54.029297 L 54.349609 58.490234 L 50.029297 58.490234 L 50.029297 58.5 L 50.019531 58.490234 L 50.029297 58.490234 L 50.029297 42.880859 z M 54.359375 46.519531 L 54.359375 50.640625 L 58.830078 50.640625 C 60.370077 50.640625 61.310547 49.839374 61.310547 48.609375 L 61.310547 48.570312 C 61.310547 47.230314 60.370545 46.519531 58.810547 46.519531 L 54.359375 46.519531 z M 38.599609 48 L 36.839844 52.439453 L 40.339844 52.439453 L 38.599609 48 z "
			/>
		</g>
		<g id="g32" transform="translate(-59.61,-81.67)">
			<path
				class="cls-1"
				d="m 145,149.73 c 3.78,-4.37 6.17,-9.98 6.46,-16.13 0.02,-0.43 0.03,-0.85 0.03,-1.28 0,-0.43 0,-0.86 -0.03,-1.28 -0.29,-6.15 -2.68,-11.76 -6.46,-16.13 -0.69,-0.8 -1.93,-0.83 -2.67,-0.08 -0.36,0.36 -0.53,0.82 -0.53,1.29 0,0.43 0.15,0.86 0.45,1.21 3.47,4.03 5.57,9.27 5.57,15 0,5.73 -2.1,10.97 -5.57,15 -0.63,0.73 -0.6,1.82 0.08,2.5 0.36,0.36 0.83,0.54 1.3,0.54 0.51,0 1.01,-0.21 1.37,-0.62 z"
				id="path28"
			/>
			<path
				class="cls-1"
				d="m 140.55,144.46 c 0,0 0,0 0.01,-0.02 0,-0.01 0.02,-0.02 0.02,-0.03 0.05,-0.06 0.1,-0.12 0.14,-0.18 0.04,-0.05 0.09,-0.11 0.13,-0.16 0.16,-0.21 0.32,-0.43 0.47,-0.64 0.03,-0.04 0.06,-0.08 0.09,-0.13 0.04,-0.06 0.09,-0.13 0.13,-0.19 0.29,-0.43 0.56,-0.86 0.81,-1.31 0.03,-0.05 0.06,-0.11 0.09,-0.16 0,-0.01 0.01,-0.02 0.02,-0.04 0.03,-0.05 0.06,-0.1 0.08,-0.16 0.02,-0.04 0.05,-0.09 0.07,-0.13 0.02,-0.04 0.05,-0.09 0.07,-0.13 0.11,-0.21 0.21,-0.42 0.31,-0.63 0.03,-0.05 0.05,-0.11 0.08,-0.16 0.03,-0.07 0.06,-0.14 0.09,-0.2 0.03,-0.06 0.05,-0.12 0.08,-0.18 0.16,-0.38 0.32,-0.76 0.46,-1.15 0.02,-0.06 0.04,-0.11 0.06,-0.17 0.06,-0.18 0.12,-0.36 0.18,-0.54 0.03,-0.08 0.05,-0.16 0.08,-0.25 0.02,-0.06 0.04,-0.12 0.05,-0.18 0.02,-0.06 0.03,-0.11 0.05,-0.17 0.02,-0.08 0.05,-0.17 0.07,-0.25 0.02,-0.06 0.03,-0.12 0.04,-0.17 0.05,-0.2 0.1,-0.4 0.14,-0.6 0.02,-0.07 0.03,-0.13 0.04,-0.2 0.01,-0.05 0.02,-0.1 0.03,-0.15 0,-0.04 0.02,-0.08 0.02,-0.11 0,-0.05 0.02,-0.09 0.03,-0.14 0.06,-0.31 0.11,-0.61 0.15,-0.93 0,-0.06 0.02,-0.13 0.03,-0.19 0,-0.07 0.02,-0.14 0.03,-0.22 0.01,-0.09 0.02,-0.18 0.03,-0.27 0,-0.08 0.02,-0.16 0.02,-0.24 0.01,-0.15 0.03,-0.3 0.03,-0.45 0,-0.06 0,-0.11 0.01,-0.17 0,-0.13 0.01,-0.26 0.02,-0.39 0,-0.06 0,-0.13 0,-0.19 0,-0.09 0,-0.19 0,-0.28 0,-0.08 0,-0.17 0,-0.25 0,-0.08 0,-0.17 0,-0.25 0,-0.09 0,-0.19 0,-0.28 0,-0.07 0,-0.13 0,-0.19 0,-0.13 -0.01,-0.27 -0.02,-0.4 0,-0.05 0,-0.11 0,-0.16 0,-0.15 -0.02,-0.3 -0.03,-0.45 0,-0.08 -0.01,-0.16 -0.02,-0.24 0,-0.09 -0.02,-0.18 -0.03,-0.27 0,-0.07 -0.02,-0.14 -0.03,-0.22 0,-0.07 -0.02,-0.13 -0.03,-0.2 -0.04,-0.31 -0.09,-0.62 -0.15,-0.92 0,-0.05 -0.02,-0.09 -0.03,-0.14 0,-0.04 -0.02,-0.08 -0.02,-0.11 0,-0.05 -0.02,-0.1 -0.03,-0.15 -0.01,-0.07 -0.03,-0.13 -0.04,-0.2 -0.04,-0.2 -0.09,-0.4 -0.14,-0.6 -0.01,-0.06 -0.03,-0.12 -0.04,-0.17 -0.02,-0.08 -0.04,-0.16 -0.06,-0.24 -0.02,-0.06 -0.03,-0.12 -0.05,-0.19 -0.02,-0.06 -0.03,-0.12 -0.05,-0.18 -0.03,-0.09 -0.05,-0.17 -0.08,-0.26 -0.04,-0.11 -0.07,-0.23 -0.11,-0.34 -0.02,-0.05 -0.04,-0.11 -0.05,-0.16 -0.16,-0.46 -0.34,-0.91 -0.53,-1.36 -0.03,-0.06 -0.05,-0.12 -0.08,-0.18 -0.03,-0.07 -0.06,-0.14 -0.09,-0.2 -0.03,-0.05 -0.05,-0.11 -0.08,-0.16 -0.1,-0.21 -0.21,-0.43 -0.31,-0.63 -0.02,-0.04 -0.05,-0.09 -0.07,-0.13 -0.02,-0.05 -0.05,-0.09 -0.07,-0.13 -0.03,-0.05 -0.06,-0.1 -0.08,-0.16 0,-0.01 -0.01,-0.02 -0.02,-0.04 -0.03,-0.05 -0.06,-0.11 -0.09,-0.16 -0.16,-0.29 -0.34,-0.58 -0.52,-0.86 -0.04,-0.06 -0.07,-0.11 -0.11,-0.17 -0.04,-0.06 -0.07,-0.11 -0.11,-0.17 -0.07,-0.1 -0.14,-0.2 -0.21,-0.31 -0.03,-0.04 -0.06,-0.08 -0.09,-0.13 -0.05,-0.08 -0.11,-0.15 -0.16,-0.23 -0.04,-0.05 -0.08,-0.11 -0.12,-0.16 -0.04,-0.05 -0.08,-0.11 -0.12,-0.16 -0.04,-0.05 -0.07,-0.1 -0.11,-0.14 -0.08,-0.11 -0.17,-0.22 -0.26,-0.33 0,0 0,0 0,0 -0.69,-0.84 -1.94,-0.92 -2.72,-0.17 0,0 -0.01,0.01 -0.02,0.02 0,0 -0.01,0.01 -0.02,0.02 -0.35,0.36 -0.52,0.82 -0.52,1.29 0,0.41 0.13,0.81 0.41,1.15 2.18,2.69 3.49,6.11 3.49,9.83 0,3.72 -1.31,7.14 -3.49,9.83 -0.59,0.72 -0.53,1.77 0.12,2.43 l 0.02,0.02 c 0,0 0.01,0.01 0.02,0.02 0.36,0.35 0.82,0.52 1.28,0.52 0.53,0 1.06,-0.23 1.43,-0.67 z"
				id="path30"
			/>
		</g>
	</svg>
{:else if type === 'logo-square-framed'}
	<svg
		class="logo logo-square-framed {$$props.class || ''}"
		data-name="logo-square-framed"
		version="1.1"
		viewBox="0 0 114.05 83.940002"
		height="100%"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			class="cls-1"
			d="M 102.12,83.94 H 11.94 C 5.36,83.94 0,78.58 0,72 V 11.94 C 0,5.36 5.36,0 11.94,0 h 90.17 c 6.58,0 11.94,5.36 11.94,11.94 V 72 c 0,6.58 -5.36,11.94 -11.94,11.94 z M 11.94,2.16 c -5.39,0 -9.78,4.39 -9.78,9.78 V 72 c 0,5.39 4.39,9.78 9.78,9.78 h 90.17 c 5.39,0 9.78,-4.39 9.78,-9.78 V 11.94 c 0,-5.39 -4.39,-9.78 -9.78,-9.78 z"
			id="path1444"
		/>
		<g id="g1474" transform="translate(-50.97,-73.22)">
			<g id="g1458">
				<g id="g1452">
					<path class="cls-1" d="m 62.35,88.11 -0.4,-0.2 v 0.2 z" id="path1448" />
					<path
						class="cls-1"
						d="m 74.84,81.03 h -12.9 v 6.15 0 1.44 h 19.51 c 0.33,-0.05 1.98,-0.17 2.2,1.43 0.2,1.49 0.59,5.17 0.85,7.61 l 0.17,1.58 c 0.11,1.01 0.1,1.03 -0.27,1.46 -0.88,1.04 -1.78,1.46 -3.55,0.34 -0.65,-0.41 -1.26,-0.8 -1.8,-1.16 -0.12,-0.08 -0.2,-0.13 -0.24,-0.16 -2.31,-1.53 -3.58,-2.52 -3.6,-2.53 l -0.05,-0.04 c 0,0 -0.41,-0.28 -0.47,-0.74 -0.04,-0.33 0.11,-0.73 0.42,-1.15 0.5,0.17 1.05,0.28 1.64,0.25 0.73,-0.03 1.41,-0.24 2.03,-0.64 0.19,2.18 1.43,2.98 1.49,3.02 l 0.78,-1.21 c 0,0 -1.05,-0.75 -0.82,-3.01 0.04,-0.37 -0.17,-0.71 -0.5,-0.86 -0.34,-0.15 -0.73,-0.07 -0.98,0.21 -0.62,0.69 -1.29,1.03 -2.05,1.06 -1.46,0.06 -2.78,-1.16 -2.8,-1.17 l -0.99,1.04 c 0,0 0.36,0.34 0.88,0.68 -0.44,0.68 -0.62,1.35 -0.53,2 0.14,1.03 0.89,1.6 1.08,1.73 0.19,0.15 1.54,1.18 3.91,2.74 0.39,0.29 1.3,1.08 1.3,2.35 0,0.53 -0.79,1.15 -1.31,1.16 -0.02,0 -0.03,0 -0.05,0 -0.74,0 -1.33,-0.43 -2.07,-0.98 l -0.14,-0.1 c -0.87,-0.64 -2.58,-1.75 -4.22,-2.04 -1.85,-0.33 -2.45,-0.83 -2.56,-0.94 l -0.02,-0.06 -0.15,-0.12 c 0,0 -0.97,-0.81 -2.28,-2.63 L 66.7,97.68 C 65.11,95.9 63.26,95.96 63.2,95.96 l -1.27,0.02 0.02,1.1 v 0 11.83 h 12.9 c 9.12,0 14.73,-6.77 14.73,-13.93 0,-7.16 -5.61,-13.93 -14.73,-13.93 z"
						id="path1450"
					/>
				</g>
				<path
					class="cls-1"
					d="m 90.31,108.9 11.86,-28.06 h 7.44 l 11.86,28.06 h -8.28 l -2.03,-4.97 h -10.75 l -1.99,4.97 H 90.3 Z m 12.38,-10.98 h 6.25 l -3.1,-7.92 -3.14,7.92 z"
					id="path1454"
				/>
				<path
					class="cls-1"
					d="M 126.23,108.9 V 81.04 h 16.25 c 7.05,0 11.58,3.62 11.58,9.79 v 0.08 c 0,6.57 -5.06,10.03 -11.98,10.03 h -8.13 v 7.96 z m 7.72,-14 h 7.97 c 2.75,0 4.42,-1.43 4.42,-3.62 V 91.2 c 0,-2.39 -1.67,-3.66 -4.46,-3.66 h -7.93 z"
					id="path1456"
				/>
			</g>
			<g id="g1466">
				<path
					id="path1446"
					class="cls-1"
					d="M 78.229766 115.29031 C 69.249775 115.29031 61.940703 122.59938 61.940703 131.57937 C 61.940703 140.55937 69.249775 147.87039 78.229766 147.87039 L 124.47 147.87039 C 133.44999 147.87039 140.75906 140.55937 140.75906 131.57937 C 140.75906 122.59938 133.44999 115.29031 124.47 115.29031 L 78.229766 115.29031 z M 98.479766 123.72 L 102.64969 123.72 L 109.30008 139.45047 L 104.65945 139.45047 L 103.52078 136.65945 L 97.489531 136.65945 L 96.370391 139.45047 L 91.829375 139.45047 L 98.479766 123.72 z M 77.180937 123.72977 L 92.579375 123.72977 L 92.579375 127.39969 L 87.259062 127.39969 L 87.259062 139.32937 L 82.989531 139.32937 L 82.989531 127.39969 L 77.180937 127.39969 L 77.180937 123.72977 z M 111.96023 123.72977 L 121.06961 123.72977 C 125.01961 123.72977 127.55984 125.76 127.55984 129.22 L 127.55984 129.25906 C 127.55984 132.93906 124.72914 134.88016 120.83914 134.88016 L 116.28055 134.88016 L 116.28055 139.33914 L 111.96023 139.33914 L 111.96023 139.35086 L 111.95047 139.33914 L 111.96023 139.33914 L 111.96023 123.72977 z M 116.29031 127.37039 L 116.29031 131.48953 L 120.75906 131.48953 C 122.29906 131.48953 123.23953 130.69023 123.23953 129.46023 L 123.23953 129.41922 C 123.23953 128.07922 122.29953 127.37039 120.73953 127.37039 L 116.29031 127.37039 z M 100.53055 128.85086 L 98.770781 133.29031 L 102.27078 133.29031 L 100.53055 128.85086 z "
				/>
			</g>
			<g id="g1472">
				<path
					class="cls-1"
					d="m 147.32,148.91 c 3.78,-4.37 6.17,-9.98 6.46,-16.13 0.02,-0.43 0.03,-0.85 0.03,-1.28 0,-0.43 0,-0.86 -0.03,-1.28 -0.29,-6.15 -2.68,-11.76 -6.46,-16.13 -0.69,-0.8 -1.93,-0.83 -2.67,-0.08 -0.36,0.36 -0.53,0.82 -0.53,1.29 0,0.43 0.15,0.86 0.45,1.21 3.47,4.03 5.57,9.27 5.57,15 0,5.73 -2.1,10.97 -5.57,15 -0.63,0.73 -0.6,1.82 0.08,2.5 0.36,0.36 0.83,0.54 1.3,0.54 0.51,0 1.01,-0.21 1.37,-0.62 z"
					id="path1468"
				/>
				<path
					class="cls-1"
					d="m 143.41,143.63 c 0,0 0,0 0.01,-0.02 0,-0.01 0.02,-0.02 0.02,-0.03 0.05,-0.06 0.1,-0.12 0.14,-0.18 0.04,-0.05 0.09,-0.11 0.13,-0.16 0.16,-0.21 0.32,-0.43 0.47,-0.64 0.03,-0.04 0.06,-0.08 0.09,-0.13 0.04,-0.06 0.09,-0.13 0.13,-0.19 0.29,-0.43 0.56,-0.86 0.81,-1.31 0.03,-0.05 0.06,-0.11 0.09,-0.16 0,-0.01 0.01,-0.02 0.02,-0.04 0.03,-0.05 0.06,-0.1 0.08,-0.16 0.02,-0.04 0.05,-0.09 0.07,-0.13 0.02,-0.04 0.05,-0.09 0.07,-0.13 0.11,-0.21 0.21,-0.42 0.31,-0.63 0.03,-0.05 0.05,-0.11 0.08,-0.16 0.03,-0.07 0.06,-0.14 0.09,-0.2 0.03,-0.06 0.05,-0.12 0.08,-0.18 0.16,-0.38 0.32,-0.76 0.46,-1.15 0.02,-0.06 0.04,-0.11 0.06,-0.17 0.06,-0.18 0.12,-0.36 0.18,-0.54 0.03,-0.08 0.05,-0.16 0.08,-0.25 0.02,-0.06 0.04,-0.12 0.05,-0.18 0.02,-0.06 0.03,-0.11 0.05,-0.17 0.02,-0.08 0.05,-0.17 0.07,-0.25 0.02,-0.06 0.03,-0.12 0.04,-0.17 0.05,-0.2 0.1,-0.4 0.14,-0.6 0.02,-0.07 0.03,-0.13 0.04,-0.2 0.01,-0.05 0.02,-0.1 0.03,-0.15 0,-0.04 0.02,-0.08 0.02,-0.11 0,-0.05 0.02,-0.09 0.03,-0.14 0.06,-0.31 0.11,-0.61 0.15,-0.93 0,-0.06 0.02,-0.13 0.03,-0.19 0,-0.07 0.02,-0.14 0.03,-0.22 0.01,-0.09 0.02,-0.18 0.03,-0.27 0,-0.08 0.02,-0.16 0.02,-0.24 0.01,-0.15 0.03,-0.3 0.03,-0.45 0,-0.06 0,-0.11 0.01,-0.17 0,-0.13 0.01,-0.26 0.02,-0.39 0,-0.06 0,-0.13 0,-0.19 0,-0.09 0,-0.19 0,-0.28 0,-0.08 0,-0.17 0,-0.25 0,-0.08 0,-0.17 0,-0.25 0,-0.09 0,-0.19 0,-0.28 0,-0.07 0,-0.13 0,-0.19 0,-0.13 -0.01,-0.27 -0.02,-0.4 0,-0.05 0,-0.11 0,-0.16 0,-0.15 -0.02,-0.3 -0.03,-0.45 0,-0.08 -0.01,-0.16 -0.02,-0.24 0,-0.09 -0.02,-0.18 -0.03,-0.27 0,-0.07 -0.02,-0.14 -0.03,-0.22 0,-0.07 -0.02,-0.13 -0.03,-0.2 -0.04,-0.31 -0.09,-0.62 -0.15,-0.92 0,-0.05 -0.02,-0.09 -0.03,-0.14 0,-0.04 -0.02,-0.08 -0.02,-0.11 0,-0.05 -0.02,-0.1 -0.03,-0.15 -0.01,-0.07 -0.03,-0.13 -0.04,-0.2 -0.04,-0.2 -0.09,-0.4 -0.14,-0.6 -0.01,-0.06 -0.03,-0.12 -0.04,-0.17 -0.02,-0.08 -0.04,-0.16 -0.06,-0.24 -0.02,-0.06 -0.03,-0.12 -0.05,-0.19 -0.02,-0.06 -0.03,-0.12 -0.05,-0.18 -0.03,-0.09 -0.05,-0.17 -0.08,-0.26 -0.04,-0.11 -0.07,-0.23 -0.11,-0.34 -0.02,-0.05 -0.04,-0.11 -0.05,-0.16 -0.16,-0.46 -0.34,-0.91 -0.53,-1.36 -0.03,-0.06 -0.05,-0.12 -0.08,-0.18 -0.03,-0.07 -0.06,-0.14 -0.09,-0.2 -0.03,-0.05 -0.05,-0.11 -0.08,-0.16 -0.1,-0.21 -0.21,-0.43 -0.31,-0.63 -0.02,-0.04 -0.05,-0.09 -0.07,-0.13 -0.02,-0.05 -0.05,-0.09 -0.07,-0.13 -0.03,-0.05 -0.06,-0.1 -0.08,-0.16 0,-0.01 -0.01,-0.02 -0.02,-0.04 -0.03,-0.05 -0.06,-0.11 -0.09,-0.16 -0.16,-0.29 -0.34,-0.58 -0.52,-0.86 -0.04,-0.06 -0.07,-0.11 -0.11,-0.17 -0.04,-0.06 -0.07,-0.11 -0.11,-0.17 -0.07,-0.1 -0.14,-0.2 -0.21,-0.31 -0.03,-0.04 -0.06,-0.08 -0.09,-0.13 -0.05,-0.08 -0.11,-0.15 -0.16,-0.23 -0.04,-0.05 -0.08,-0.11 -0.12,-0.16 -0.04,-0.05 -0.08,-0.11 -0.12,-0.16 -0.04,-0.05 -0.07,-0.1 -0.11,-0.14 -0.08,-0.11 -0.17,-0.22 -0.26,-0.33 0,0 0,0 0,0 -0.69,-0.84 -1.94,-0.92 -2.72,-0.17 0,0 -0.01,0.01 -0.02,0.02 0,0 -0.01,0.01 -0.02,0.02 -0.35,0.36 -0.52,0.82 -0.52,1.29 0,0.41 0.13,0.81 0.41,1.15 2.18,2.69 3.49,6.11 3.49,9.83 0,3.72 -1.31,7.14 -3.49,9.83 -0.59,0.72 -0.53,1.77 0.12,2.43 l 0.02,0.02 c 0,0 0.01,0.01 0.02,0.02 0.36,0.35 0.82,0.52 1.28,0.52 0.53,0 1.06,-0.23 1.43,-0.67 z"
					id="path1470"
				/>
			</g>
		</g>
	</svg>
{/if}
