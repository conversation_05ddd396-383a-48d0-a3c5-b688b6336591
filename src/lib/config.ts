import { merge } from './utils'

import type { UserRole } from './graphql/generated/gateway'

export const internalUrls = [
	'temp.daptapgo.io',
	'develop.daptapgo.io',
	'stage.daptapgo.io',
	'daptapgo.io',
	'localhost:5173',
	'localhost:4173',
]

export const appPath = ''
export const authPaths = ['backoffice', appPath, 'org', 'settings']
export const authPathsRegex = new RegExp(`^/(${authPaths.filter((path) => path.length).join('|')})`)

const toolingPaths = [
	'sitemap.xml',
	'robots.txt',
	'status',
	'terms_and_conditions',
	'privacy_policy',
	'login',
	'unauthorized',
	'sign-out',
	'invitation',
]

const appSpecificPaths = ['tap', 'tag', 'products']

const customDomainPaths = [...authPaths.filter((path) => path.length), ...toolingPaths, ...appSpecificPaths]
export const customDomainPathsRegex = new RegExp(`^/(${customDomainPaths.join('|')})`)

export type ProjectConfigFilter =
	| boolean
	| 'SystemAdmin'
	| 'InternalUser'
	| 'InternalOrgUser'
	| Array<Exclude<UserRole | keyof typeof UserRole, 'SystemAdmin'>>
export interface ProjectSettings {
	userSetting: {
		password: ProjectConfigFilter
		createOrganization: ProjectConfigFilter
	}
}
const defaultProjectSettings: ProjectSettings = {
	userSetting: {
		password: true,
		createOrganization: true,
	},
}

export const projectConfig = merge<ProjectSettings>(defaultProjectSettings, {
	userSetting: {
		password: false,
		createOrganization: 'SystemAdmin',
	},
})

export const reroutes: Record<string, string> = {
	'/backoffice/users': '/backoffice/users/manage',
	'/backoffice/organizations': '/backoffice/organizations/manage',
}
