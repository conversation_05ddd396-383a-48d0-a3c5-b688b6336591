import { tick } from 'svelte'

import { browser } from '$app/environment'
import { page } from '$app/state'

import { projectConfig, type ProjectSettings, type ProjectConfigFilter } from '$lib/config'
import { AsyncMe, type User, type Organization, type UserRole } from '$lib/graphql/generated/gateway'
import { flatten, type LeafFields } from '$lib/utils'
import { isValidSession, silentRefresh } from '$lib/utils/session'

export const internalUsersRoles = ['SystemAdmin', 'Support', 'Developer']

export class Auth {
	#internalSession = $state<App.CustomSession>()
	session = $derived.by(() => {
		if (browser) {
			return this.#internalSession || page.data.session
		}

		return page.data.session
	})

	profile: User | undefined = $derived.by(() => {
		if (page.data.profile) {
			return structuredClone(page.data.profile)
		} else if (browser && this.session?.user_id) {
			AsyncMe({
				context: {
					session: this.session,
				},
			}).then(({ data: { me } }) => (this.profile = structuredClone(me as User)))
		}
		return undefined
	})

	isAuthenticated = $derived(!!page?.data?.session)

	isInternalUser = $derived(
		this.profile?.roles.some((role) => internalUsersRoles.includes(role.role) && role.organizationId === null),
	)
	isInternalOrgUser = $derived(
		this.profile?.roles.some(
			(role) =>
				(role.organizationId && role.role !== 'User') ||
				(internalUsersRoles.includes(role.role) && role.organizationId === null),
		),
	)
	isSystemAdmin = $derived(
		this.profile?.roles.some((role) => role.role === 'SystemAdmin' && role.organizationId === null),
	)

	currentOrg = $state<Organization>()
	currentProject = $state<Organization>()

	hasOrgRole = (role: UserRole | UserRole[], org?: string) => {
		if (!Array.isArray(role)) {
			role = [role]
		}

		if (!org) {
			return this.currentOrg && this.profile?.roles.some((r) => r.organizationId === org && role.includes(r.role))
		}

		return this.profile?.roles.some((r) => r.organizationId === org && role.includes(r.role))
	}

	get token() {
		return (async () => {
			if (this.session && !isValidSession(this.session)) {
				this.#internalSession = await silentRefresh(this.session)

				await tick()
			}
			return this.session?.access_token
		})()
	}
}

let instance: Auth
export function initAuth() {
	if (!instance) {
		instance = new Auth()
	}

	return instance
}

export function getAuth() {
	return instance
}

// Feature Flags

const flatConfig = flatten(projectConfig)

export function featureEnabled(auth: Auth, key: LeafFields<ProjectSettings>, org?: string) {
	const value = flatConfig[key] as ProjectConfigFilter
	if (value === undefined) {
		throw new Error(`Unknown feature: ${key}`)
	}

	if (typeof value === 'boolean') {
		return value
	}

	if (value === 'SystemAdmin') {
		return auth.isSystemAdmin
	}

	if (value === 'InternalUser') {
		return auth.isInternalUser
	}

	if (value === 'InternalOrgUser') {
		return auth.isInternalOrgUser
	}

	if (Array.isArray(value)) {
		return auth.hasOrgRole(value as UserRole[], org)
	}

	return false
}
