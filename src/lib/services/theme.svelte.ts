import { PersistedState } from 'runed'
import { setContext, getContext } from 'svelte'
import { MediaQuery } from 'svelte/reactivity'

import { browser } from '$app/environment'

class Theme {
	#modeOs = new MediaQuery('(prefers-color-scheme: light)')
	#modeUser = new PersistedState<boolean | 'undefined'>('modeUserPrefers', 'undefined')
	#modeCurrent = $derived(this.#modeUser.current !== 'undefined' ? this.#modeUser.current : this.#modeOs.current)

	mode = $derived(this.#modeCurrent ? 'Light' : 'Dark')
	label = $derived(this.#modeUser.current === 'undefined' ? 'Auto' : this.#modeCurrent ? 'Light' : 'Dark')

	constructor() {
		if (browser) {
			$effect(() => {
				document.documentElement.setAttribute('data-mode', this.mode.toLowerCase())
			})
		}
	}

	setMode = (value: 'Light' | 'Dark' | 'Auto') => {
		switch (value) {
			case 'Light':
				this.#modeUser.current = true
				break
			case 'Dark':
				this.#modeUser.current = false
				break
			default:
				this.#modeUser.current = 'undefined'
				break
		}
	}

	toggleMode = () => {
		if (this.#modeUser.current === true) {
			this.#modeUser.current = false
		} else if (this.#modeUser.current === false) {
			this.#modeUser.current = 'undefined'
		} else {
			this.#modeUser.current = true
		}
	}
}

const DEFAULT_KEY = Symbol()

export function initTheme(key = DEFAULT_KEY) {
	return setContext<Theme>(key, new Theme())
}

export function getTheme(key = DEFAULT_KEY) {
	return getContext<Theme>(key)
}
