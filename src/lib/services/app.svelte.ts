import { setContext, getContext } from 'svelte'

import { page } from '$app/state'

import { appPath } from '$lib/config'

const appRegex = new RegExp(`^/${appPath}`)

class App {
	navbarOpened = $state(false)
	asideOpened = $state(false)
	searchOpened = $state(false)

	homePath = $derived.by(() => {
		if (/^\/backoffice/.test(page.url.pathname)) {
			return '/backoffice'
		}

		if (appRegex.test(page.url.pathname)) {
			return `/${appPath}`
		}

		return page.data?.customDomain ? `/${appPath}` : '/'
	})
}

const DEFAULT_KEY = Symbol()

export function initApp(key = DEFAULT_KEY) {
	return setContext<App>(key, new App())
}

export function getApp(key = DEFAULT_KEY) {
	return getContext<App>(key)
}
