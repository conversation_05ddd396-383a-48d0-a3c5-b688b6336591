import { signIn } from '@auth/sveltekit/client'

import { browser } from '$app/environment'

import { oncefy } from '$lib/utils'

export function isValidSession(session?: App.CustomSession) {
	return !!session && +new Date() < (session.expires_at as number) * 1000 + 5000
}

async function refreshToken() {
	const response = await fetch('/refresh_token', {
		headers: {
			'content-type': 'application/json',
		},
	})

	const session = await response.json()

	if (!response.ok) throw session

	if (session.error === 'RefreshAccessTokenError') {
		if (browser) {
			return signIn('oidc') // Force sign in to hopefully resolve error
		} else {
			throw new Error('RefreshAccessTokenError')
		}
	}

	return session as App.CustomSession
}

async function refreshSession(session?: App.CustomSession) {
	if (session) {
		try {
			return refreshToken()
		} catch (error) {
			console.error(error)
			return undefined
		}
	}
	return undefined
}

export const silentRefresh = oncefy(refreshSession)
