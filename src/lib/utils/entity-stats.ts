import {
	format,
	sub,
	startOfMonth,
	endOfMonth,
	startOfYear,
	endOfYear,
	startOfQuarter,
	endOfQuarter,
	eachDayOfInterval,
	eachMonthOfInterval,
	eachWeekOfInterval,
	startOfDay,
	endOfDay,
} from 'date-fns'

import type { Stats2D, StatsInput } from '$lib/graphql/generated/gateway'
import type { ScopeOptions } from '$lib/modules/Dashboard'

export const scopeMap: { [index in ScopeOptions]: string } = {
	currentMonth: format(new Date(), 'MMMM y'),
	lastMonth: format(sub(new Date(), { months: 1 }), 'MMMM y'),
	currentYear: 'Current Year',
	lastYear: 'Last Year',
	currentQuarter: 'Current Quarter',
	lastQuarter: 'Last Quarter',
	last30Days: 'Last 30D',
	last14Days: 'Last 14D',
	last7Days: 'Last 7D',
}

export type StatsPeriod = 'day' | 'week' | 'month'

export function scopeResolution(scope: ScopeOptions): StatsPeriod {
	switch (scope) {
		case 'currentMonth':
		case 'lastMonth':
			return 'day'
		case 'currentYear':
		case 'lastYear':
			return 'month'
		case 'currentQuarter':
		case 'lastQuarter':
			return 'week'
		case 'last30Days':
		case 'last14Days':
		case 'last7Days':
		default:
			return 'day'
	}
}

export function scopeToScopeQuery(scope: ScopeOptions): StatsInput {
	const now = new Date()
	switch (scope) {
		case 'currentMonth':
			return {
				from: startOfMonth(now),
				to: endOfMonth(now),
			}
		case 'lastMonth':
			return {
				from: startOfMonth(sub(now, { months: 1 })),
				to: endOfMonth(sub(now, { months: 1 })),
			}
		case 'currentYear':
			return {
				from: startOfYear(now),
			}
		case 'lastYear':
			return {
				from: startOfYear(sub(now, { years: 1 })),
				to: endOfYear(sub(now, { years: 1 })),
			}
		case 'currentQuarter':
			return {
				from: startOfQuarter(now),
				to: endOfQuarter(now),
			}
		case 'lastQuarter':
			return {
				from: startOfQuarter(sub(now, { months: 3 })),
				to: endOfQuarter(sub(now, { months: 3 })),
			}
		case 'last30Days':
			return {
				from: startOfDay(sub(now, { days: 30 })),
			}
		case 'last14Days':
			return {
				from: startOfDay(sub(now, { days: 14 })),
			}
		case 'last7Days':
			return {
				from: startOfDay(sub(now, { days: 7 })),
			}
	}
}

export function scopeToPreviousScopeQuery(scope: ScopeOptions): StatsInput {
	const now = new Date()
	switch (scope) {
		case 'currentMonth':
			return {
				from: startOfMonth(sub(now, { months: 1 })),
				to: endOfMonth(sub(now, { months: 1 })),
			}
		case 'lastMonth':
			return {
				from: startOfMonth(sub(now, { months: 2 })),
				to: endOfMonth(sub(now, { months: 2 })),
			}
		case 'currentYear':
			return {
				from: startOfYear(sub(now, { years: 1 })),
				to: endOfYear(sub(now, { years: 1 })),
			}
		case 'lastYear':
			return {
				from: startOfYear(sub(now, { years: 2 })),
				to: endOfYear(sub(now, { years: 2 })),
			}
		case 'currentQuarter':
			return {
				from: startOfQuarter(sub(now, { months: 3 })),
				to: endOfQuarter(sub(now, { months: 3 })),
			}
		case 'lastQuarter':
			return {
				from: startOfQuarter(sub(now, { months: 6 })),
				to: endOfQuarter(sub(now, { months: 6 })),
			}
		case 'last30Days':
			return {
				from: startOfDay(sub(now, { days: 60 })),
				to: endOfDay(sub(now, { days: 30 })),
			}
		case 'last14Days':
			return {
				from: startOfDay(sub(now, { days: 28 })),
				to: endOfDay(sub(now, { days: 14 })),
			}
		case 'last7Days':
			return {
				from: startOfDay(sub(now, { days: 14 })),
				to: endOfDay(sub(now, { days: 7 })),
			}
	}
}

// const weekDays = [undefined, 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
const weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
export function translateWeekDays(data: string[]) {
	return data.map((day) => weekDays[parseInt(day)])
}

export function fillMissingData(
	data: Stats2D | null | undefined,
	scope: ScopeOptions | 'weekDays',
	filter?: StatsInput,
	filler: number | null = null,
) {
	if (!data) {
		data = { x: [], y: [] }
	}

	if (scope === 'weekDays') {
		return fillWeekDays(data)
	}

	if (!filter) {
		return data
	}

	const resolution = scopeResolution(scope)
	let array: string[]
	if (resolution === 'day') {
		array = eachDayOfInterval({
			start: new Date(filter.from),
			end: filter.to ? new Date(filter.to) : new Date(),
		}).map((date) => format(date, 'dd/MM/yyyy'))
	} else if (resolution === 'week') {
		array = eachWeekOfInterval({
			start: new Date(filter.from),
			end: filter.to ? new Date(filter.to) : new Date(),
		}).map((date) => format(date, 'I'))
	} else {
		array = eachMonthOfInterval({
			start: new Date(filter.from),
			end: filter.to ? new Date(filter.to) : new Date(),
		}).map((date) => format(date, 'MM/yyyy'))
	}

	const result: { x: string[]; y: Array<number | null> } = { x: [], y: [] }

	for (const item of array) {
		const index = data.x?.indexOf(item)
		if (typeof index !== 'undefined' && index !== -1) {
			result.x.push(item)
			result.y.push(data.y?.[index] || filler)
		} else {
			result.x.push(item)
			result.y.push(filler)
		}
	}
	return result
}

function fillWeekDays(data: Stats2D, filler: number | null = null) {
	const result: { x: string[]; y: Array<number | null> } = { x: [], y: [] }

	for (let i = 1; i <= 7; i++) {
		const index = data.x?.indexOf(i.toString())
		if (typeof index !== 'undefined' && index !== -1) {
			result.x.push(i.toString())
			result.y.push(data.y?.[index] || filler)
		} else {
			result.x.push(i.toString())
			result.y.push(filler)
		}
	}

	return result
}
