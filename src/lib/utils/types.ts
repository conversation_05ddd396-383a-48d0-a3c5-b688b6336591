export interface DocumentNode {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	[key: string]: any
}

type ResultWithNodes<T extends DocumentNode> = {
	__typename?: string
	totalCount?: number
	nodes?: T[] | null
}

export type Unpacked<T> = T extends Array<infer U> ? U : T

export type QueryReturnType<T> = T extends ResultWithNodes<infer U> ? U : T extends DocumentNode[] ? Unpacked<T> : T

export type IsAny<Type, ResultIfAny, ResultIfNotAny> = true extends false & Type ? ResultIfAny : ResultIfNotAny
export type Flatten<Type> = Type extends ReadonlyArray<infer Item> ? Item : Type
export type ArrayElement<Type> = Type extends ReadonlyArray<infer Item> ? Item : never

type ParseNumber<T> = T extends `${infer U extends number}` ? U : never

export type EnumToPrimitiveUnion<T> = Exclude<`${T & string}` | ParseNumber<`${T & number}`>, undefined>

export type PropertyType<Type, Property extends string> = string extends Property
	? unknown
	: Property extends keyof Type
		? Type[Property]
		: Property extends `${number}`
			? Type extends ReadonlyArray<infer ArrayType>
				? ArrayType
				: unknown
			: Property extends `${infer Key}.${infer Rest}`
				? Key extends `${number}`
					? Type extends ReadonlyArray<infer ArrayType>
						? PropertyType<ArrayType, Rest>
						: unknown
					: Key extends keyof Type
						? Type[Key] extends Map<string, infer MapType>
							? MapType
							: PropertyType<Type[Key], Rest>
						: unknown
				: unknown

export type KeysOfAType<TSchema, Type> = {
	[key in keyof TSchema]: NonNullable<TSchema[key]> extends Type ? key : never
}[keyof TSchema]

export type KeysOfOtherType<TSchema, Type> = {
	[key in keyof TSchema]: NonNullable<TSchema[key]> extends Type ? never : key
}[keyof TSchema]

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type MatchKeysAndValues<TSchema> = Readonly<Partial<TSchema>> & Record<string, any>

export type Join<T extends unknown[], D extends string> = T extends []
	? ''
	: T extends [string | number]
		? `${T[0]}`
		: T extends [string | number, ...infer R]
			? `${T[0]}${D}${Join<R, D>}`
			: string

export type NestedPaths<Type, Depth extends number[]> = Depth['length'] extends 3
	? []
	: Type extends string | number | bigint | boolean | Date | RegExp
		? []
		: Type extends ReadonlyArray<infer ArrayType>
			? [] | [number, ...NestedPaths<ArrayType, [...Depth, 1]>]
			: // eslint-disable-next-line @typescript-eslint/no-explicit-any
				Type extends Map<string, any>
				? [string]
				: Type extends object
					? {
							[Key in Extract<keyof Type, string>]: Type[Key] extends Type
								? [Key]
								: Type extends Type[Key]
									? [Key]
									: Type[Key] extends ReadonlyArray<infer ArrayType>
										? Type extends ArrayType
											? [Key]
											: ArrayType extends Type
												? [Key]
												: [Key, ...NestedPaths<Type[Key], [...Depth, 1]>]
										: [Key, ...NestedPaths<Type[Key], [...Depth, 1]>] | [Key]
						}[Extract<keyof Type, string>]
					: []

export type NestedFields<T> = Join<NestedPaths<T, []>, '.'>

export type LeafPaths<Type, Depth extends number[]> = Depth['length'] extends 3
	? []
	: Type extends string | number | bigint | boolean | Date | RegExp | null | undefined | symbol
		? []
		: Type extends ReadonlyArray<infer ArrayType>
			? ArrayType extends string | number | bigint | boolean | Date | RegExp | null | undefined | symbol
				? [[number]]
				: [number, ...LeafPaths<ArrayType, [...Depth, 1]>]
			: Type extends object
				? {
						[Key in Extract<keyof Type, string>]: Type[Key] extends
							| string
							| number
							| bigint
							| boolean
							| Date
							| RegExp
							| null
							| undefined
							| symbol
							? [Key]
							: Type[Key] extends ReadonlyArray<infer ArrayType>
								? ArrayType extends string | number | bigint | boolean | Date | RegExp | null | undefined | symbol
									? [Key, number]
									: [Key, number, ...LeafPaths<ArrayType, [...Depth, 1]>]
								: [Key, ...LeafPaths<Type[Key], [...Depth, 1]>]
					}[Extract<keyof Type, string>]
				: []

export type LeafFields<T> = Join<LeafPaths<T, []>, '.'>

export type NestedPathsOfType<TSchema, Type> = KeysOfAType<
	{
		[Property in Join<NestedPaths<TSchema, []>, '.'>]: PropertyType<TSchema, Property>
	},
	Type
>

export type StrictMatchKeysAndValues<TSchema> = {
	[Property in NestedFields<TSchema>]?: PropertyType<TSchema, Property>
}

export type StrictPropertyType<T, P extends NestedFields<T> = NestedFields<T>> = Exclude<
	Unpacked<StrictMatchKeysAndValues<T>[P]>,
	undefined
>

export type ResolvedPropertyType<T, P extends NestedFields<T> = NestedFields<T>> =
	| StrictPropertyType<T, P>
	| EnumToPrimitiveUnion<StrictPropertyType<T, P>>

export type ExtractValues<T> = T[keyof T]

export type SwitchNestedIfArray<T, P extends NestedFields<T>> =
	Unpacked<PropertyType<T, P>> extends never ? PropertyType<T, P> : Unpacked<PropertyType<T, P>>

type UnionToIntersection<U> = (U extends unknown ? (k: U) => void : never) extends (k: infer I) => void ? I : never
type UnionToOvlds<U> = UnionToIntersection<U extends unknown ? (f: U) => void : never>

type PopUnion<U> = UnionToOvlds<U> extends (a: infer A) => void ? A : never

export type UnionConcat<U extends string, Sep extends string> =
	PopUnion<U> extends infer SELF
		? SELF extends string
			? Exclude<U, SELF> extends never
				? SELF
				: `${UnionConcat<Exclude<U, SELF>, Sep>}${Sep}${SELF}` | UnionConcat<Exclude<U, SELF>, Sep> | SELF
			: never
		: never
