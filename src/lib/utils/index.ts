export * from './types'

import type { LeafFields, PropertyType } from './types'

const timeUnitFormatter = (locale: string | string[], unit: string, unitDisplay: Intl.RelativeTimeFormatStyle) =>
	Intl.NumberFormat(locale, { style: 'unit', unit, unitDisplay }).format

const divMod = (n: number, m: number) => [Math.floor(n / m), n % m]

const createDurationFormatter = (locale: string | string[], unitDisplay: Intl.RelativeTimeFormatStyle = 'long') => {
	const fmtDays = timeUnitFormatter(locale, 'day', unitDisplay),
		fmtHours = timeUnitFormatter(locale, 'hour', unitDisplay),
		fmtMinutes = timeUnitFormatter(locale, 'minute', unitDisplay),
		fmtSeconds = timeUnitFormatter(locale, 'second', unitDisplay),
		fmtMilliseconds = timeUnitFormatter(locale, 'millisecond', unitDisplay),
		fmtList = new Intl.ListFormat(locale, {
			style: 'long',
			type: 'conjunction',
		})
	return (milliseconds: number) => {
		let days, hours, minutes, seconds
		;[days, milliseconds] = divMod(milliseconds, 864e5)
		;[hours, milliseconds] = divMod(milliseconds, 36e5)
		;[minutes, milliseconds] = divMod(milliseconds, 6e4)
		;[seconds, milliseconds] = divMod(milliseconds, 1e3)
		return fmtList.format(
			[
				days ? fmtDays(days) : null,
				hours ? fmtHours(hours) : null,
				minutes ? fmtMinutes(minutes) : null,
				seconds ? fmtSeconds(seconds) : null,
				unitDisplay != 'narrow' && (unitDisplay != 'short' || !seconds) && milliseconds
					? fmtMilliseconds(milliseconds)
					: null,
			].filter((v) => v !== null) as string[],
		)
	}
}

export const durationFormatter = createDurationFormatter('en-US')
export const durationFormatterShort = createDurationFormatter('en-US', 'short')
export const durationFormatterNarrow = createDurationFormatter('en-US', 'narrow')

const emailRegex =
	// eslint-disable-next-line no-useless-escape
	/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
export const validateEmail = (email: string) => {
	return emailRegex.test(email)
}

export type UnwrapPromise<T> =
	T extends Promise<infer U>
		? U
		: T extends (...args: unknown[]) => Promise<infer U>
			? U
			: T extends (...args: unknown[]) => infer U
				? U
				: T

class Oncefy<F extends (...args: never[]) => ReturnType<F>> {
	pendingConcurrent: Array<(error?: Error, result?: UnwrapPromise<ReturnType<F>>) => void> = []
	isRunningConcurrent = false

	constructor(readonly fn: F) {}

	async invoke(...args: Parameters<F>) {
		// eslint-disable-next-line no-async-promise-executor
		return new Promise<UnwrapPromise<ReturnType<F>>>(async (resolve, reject) => {
			this.pendingConcurrent.push((error?: Error, result?: UnwrapPromise<ReturnType<F>>) => {
				return error ? reject(error) : resolve(result as UnwrapPromise<ReturnType<F>>)
			})

			if (!this.isRunningConcurrent) {
				this.isRunningConcurrent = true

				try {
					const result = await Promise.resolve(this.fn(...args))
					this.resolveConcurrent(undefined, result as UnwrapPromise<ReturnType<F>>)
				} catch (error) {
					this.resolveConcurrent(error as Error)
				}
			}
		})
	}

	resolveConcurrent = (error?: Error, result?: UnwrapPromise<ReturnType<F>>) => {
		this.pendingConcurrent.map((callback) => callback(error, result))
		this.pendingConcurrent = []
		this.isRunningConcurrent = false
	}
}

export function oncefy<
	F extends (...args: never[]) => ReturnType<F>,
	T extends (value: UnwrapPromise<ReturnType<F>>) => UnwrapPromise<ReturnType<T>>,
>(fn: F, then?: T) {
	const instance = new Oncefy(fn)
	if (then) {
		return (...args: Parameters<F>) => instance.invoke(...args).then(then)
	}

	return (...args: Parameters<F>) => instance.invoke(...args)
}

// Calculate the locale for date format based on user's preferred languages
let calculatedLocale: string | string[] | null
export function getLocaleForDateFormat() {
	if (calculatedLocale !== undefined) {
		return calculatedLocale === null ? undefined : calculatedLocale
	}

	// Check if the default locale is 'en' or 'en-US'
	const defaultLocale = Intl.DateTimeFormat().resolvedOptions().locale.toLowerCase()
	if (defaultLocale === 'en' || defaultLocale === 'en-us') {
		// Get the user's preferred languages
		const languages = navigator.languages || [navigator.language]

		// Check if there are other preferred languages specified
		const nonEnglishLang = languages.find((lang) => !['en', 'en-us'].includes(lang.toLowerCase()))

		if (nonEnglishLang) {
			calculatedLocale = nonEnglishLang

			return calculatedLocale
		}
	}

	// If no special case is detected, use the default locale
	calculatedLocale = null
	return calculatedLocale === null ? undefined : calculatedLocale
}

function isObject(value: unknown): value is object {
	return typeof value === 'object' && value !== null
}

export function merge<T extends object>(a: T, b: Partial<T>): T {
	const result: Partial<T> = {}
	const keys = new Set([...Object.keys(a), ...Object.keys(b)])

	for (const key of keys) {
		const aValue = a[key as keyof T]
		const bValue = b[key as keyof T]

		if (isObject(aValue) && isObject(bValue)) {
			;(result as Record<string, unknown>)[key] = merge(aValue, bValue)
		} else {
			;(result as Record<string, unknown>)[key] = (bValue !== undefined ? bValue : aValue) as T[keyof T]
		}
	}

	return result as T
}

export type FlattenObject<T> = {
	[P in LeafFields<T>]: PropertyType<T, P>
}

export function flatten<T extends object>(obj: T): FlattenObject<T> {
	const result = {} as FlattenObject<T>
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const stack: Array<{ currentObj: any; path: string }> = [{ currentObj: obj, path: '' }]

	while (stack.length > 0) {
		const { currentObj, path } = stack.pop()!

		if (
			currentObj != null &&
			typeof currentObj === 'object' &&
			!(currentObj instanceof Date) &&
			!(currentObj instanceof RegExp)
		) {
			if (Array.isArray(currentObj)) {
				for (let i = 0; i < currentObj.length; i++) {
					const element = currentObj[i]
					const elementPath = path ? `${path}.${i}` : `${i}`
					stack.push({ currentObj: element, path: elementPath })
				}
			} else {
				for (const key in currentObj) {
					if (Object.prototype.hasOwnProperty.call(currentObj, key)) {
						const value = currentObj[key]
						const newPath = path ? `${path}.${key}` : key
						stack.push({ currentObj: value, path: newPath })
					}
				}
			}
		} else {
			// Leaf value
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			;(result as any)[path as keyof FlattenObject<T>] = currentObj
		}
	}

	return result
}

export function diffObjects<A extends object, B extends object>(a: A, b: B) {
	const result = {} as Record<string, unknown>

	const flattenA = flatten(a)
	const flattenB = flatten(b)

	for (const key in flattenA) {
		if (key in flattenB) {
			const aValue = flattenA[key as keyof FlattenObject<A>]
			const bValue = flattenB[key as keyof FlattenObject<B>]

			if ((aValue as unknown) !== (bValue as unknown)) {
				result[key] = bValue
			}
		}
	}

	for (const key in flattenB) {
		if (!(key in flattenA)) {
			result[key] = flattenB[key as keyof FlattenObject<B>]
		}
	}

	return result
}
