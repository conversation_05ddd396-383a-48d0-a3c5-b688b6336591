// Navigation Sitemap
import { gdpr } from './modules/GDPR/state.svelte.js'
import { internalUsersRoles } from './services/auth.svelte.js'

import type { User } from './graphql/generated/gateway'

export type List = Array<{
	href?: string
	onclick?: () => void
	label: string
	keywords: string
	badge?: string
	classes?: string
	full?: boolean
	target?: '_blank' | '_self' | '_parent' | '_top'
	reroute?: string
}>

export type Links = Record<string, Array<{ title: string; list: List }>>

export const menuNavLinks = (profile?: User, orgId?: string): Links => ({
	'/home': [
		{
			title: '',
			list: [
				{ href: '/', label: 'Home', keywords: '', full: true },
				...(profile ? [{ href: '/products', label: 'My Products', keywords: '' }] : []),
			],
		},
		// {
		// 	title: 'Product',
		// 	list: [
		// 		{ href: '/features', label: 'Features', keywords: '' },
		// 		{ href: '/pricing', label: 'Pricing', keywords: '' },
		// 	],
		// },
		...(orgId === undefined
			? [
					{
						title: 'Company',
						list: [
							{ href: '/about', label: 'About', keywords: '' },
							{ href: '/faq', label: 'FAQ', keywords: '' },
						],
					},
				]
			: []),
		{
			title: 'Service',
			list: [
				{ href: 'https://status.daptapgo.io/', label: 'Status', keywords: '', target: '_blank' },
				{ href: '/terms_and_conditions', label: 'Terms of Service', keywords: '' },
				{ href: '/privacy_policy', label: 'Privacy Policy', keywords: '' },
				...(import.meta.env.VITE_DATADOG_CLIENT_TOKEN !== undefined || import.meta.env.GOOGLE_TAG_ID !== undefined
					? [
							{
								onclick: gdpr.openBanner,
								label: 'Cookie Settings',
								keywords: '',
							},
						]
					: []),
				// { href: '/gdpr', label: 'GDPR Compliance', keywords: '' },
				// { href: '/data_processing_agreement', label: 'Data Processing Agreement', keywords: '' }
			],
		},
	],
	'/docs': [
		// {
		// 	title: 'Docs',
		// 	list: [
		// 		{ href: '/docs/introduction', label: 'Introduction', keywords: 'svelte, sirens, license, release' },
		// 		{
		// 			href: '/docs/get-started',
		// 			label: 'Get Started',
		// 			keywords: 'start, install, cli, tailwind, themes, stylesheets'
		// 		},
		// 		{ href: '/docs/quickstart', label: 'Quickstart', keywords: 'start, setup, tutorial, guide' }
		// 	]
		// },
		// {
		// 	title: 'Essentials',
		// 	list: [
		// 		{ href: '/docs/themes', label: 'Themes', keywords: 'theme, customize, fonts, gradient, background' },
		// 		{ href: '/docs/colors', label: 'Colors', keywords: 'theme, colors, swatches' },
		// 		{ href: '/docs/styling', label: 'Styling', keywords: 'styles, styling, props, classes, class, css' },
		// 		{ href: '/docs/tokens', label: 'Design Tokens', keywords: 'theme, color, pairing, css, utility' },
		// 		{ href: '/docs/variants', label: 'Variants', keywords: 'variant, variants, presets, backgrounds, classes' },
		// 		{
		// 			href: '/docs/transitions',
		// 			label: 'Transitions',
		// 			keywords: 'transition, transitions, blur, fade, fly, slide, scale, draw, crossfade, prefers, reduced, motion'
		// 		}
		// 	]
		// },
		// {
		// 	title: 'Resources',
		// 	list: [
		// 		{ href: '/docs/generator', label: 'Theme Generator', keywords: 'create, custom, style, css, design' },
		// 		{ href: '/docs/purgecss', label: 'PurgeCSS', keywords: 'purgecss, vite, tree, shaking, bundle, optimize' },
		// 		{ href: '/docs/contributing', label: 'Contributing', keywords: 'branch, pr' },
		// 		{
		// 			href: '/docs/sponsorship',
		// 			label: 'Sponsorship',
		// 			keywords: 'sponsor, funding, contribute, support, github, ko-fi, patreon',
		// 			badge: 'New'
		// 		}
		// 	]
		// }
	],
	'/profile': [
		{
			title: 'Personal Settings',
			list: [
				{ href: '/settings/profile', label: 'Profile', keywords: '' },
				{ href: '/settings/security', label: 'Security', keywords: '' },
				{
					href: '/settings/organizations',
					label: 'Organizations',
					keywords: '',
				},
			],
		},
		{
			title: '',
			list: [
				{
					href: '/sign-out',
					label: 'Sign Out',
					keywords: '',
					classes: 'text-warning-700 font-semibold',
				},
			],
		},
	],
	'/backoffice': [
		{
			title: '',
			list:
				orgId !== undefined
					? [
							{ href: `/backoffice/${orgId}`, label: 'Dashboard', keywords: '', full: true },
							{ href: `/backoffice/${orgId}/people`, label: 'People', keywords: '' },
						]
					: [
							{ href: '/backoffice', label: 'Dashboard', keywords: '', full: true },
							{ href: '/backoffice/people', label: 'People', keywords: '' },
						],
		},
		{
			title: '',
			list:
				orgId !== undefined
					? [
							{ href: `/backoffice/${orgId}/tags/manage`, label: 'Tags', keywords: '' },
							{ href: `/backoffice/${orgId}/products/manage`, label: 'Products', keywords: '' },
							{ href: `/backoffice/${orgId}/programs/manage`, label: 'Programs', keywords: '' },
							{ href: `/backoffice/${orgId}/activations/manage`, label: 'Activations', keywords: '' },
						]
					: [
							{ href: '/backoffice/tags/manage', label: 'Tags', keywords: '' },
							{ href: '/backoffice/products/manage', label: 'Products', keywords: '' },
							{ href: '/backoffice/programs/manage', label: 'Programs', keywords: '' },
							{ href: '/backoffice/activations/manage', label: 'Activations', keywords: '' },
						],
		},
		...(orgId !== undefined
			? profile?.roles.some((role) => internalUsersRoles.includes(role.role) && role.organizationId === null) ||
				profile?.roles.some((role) => role.organizationId == orgId && ['Owner', 'Admin'].includes(role.role))
				? [
						{
							title: '',
							list: [
								{
									href: `/backoffice/${orgId}/settings`,
									label: 'Settings',
									keywords: '',
								},
							],
						},
					]
				: []
			: profile?.roles.some((role) => internalUsersRoles.includes(role.role) && role.organizationId === null)
				? [
						{
							title: '',
							list: [
								{
									href: '/backoffice/users',
									label: 'Users',
									keywords: '',
									reroute: '/manage',
								},
								{
									href: '/backoffice/organizations',
									label: 'Organizations',
									keywords: '',
									reroute: '/manage',
								},
							],
						},
					]
				: []),
	],
})
