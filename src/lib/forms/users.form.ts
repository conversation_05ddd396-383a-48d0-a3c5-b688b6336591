import { type UpdateUserInput } from '$lib/graphql/generated/gateway'
import { FieldType, type Form } from '$lib/modules/Form'

export const editUserForm = (): Form<UpdateUserInput> => [
	{
		title: 'Personal Details',
		description: 'Your personal details are used to identify you and customize your experience.',
		fields: [
			{
				key: 'name',
				type: FieldType.INPUT,
				props: {
					label: 'First Name',
					required: true,
					autocomplete: 'section-personal given-name',
				},
			},
			{
				key: 'familyName',
				type: FieldType.INPUT,
				props: {
					label: 'Last Name',
					required: true,
					autocomplete: 'section-personal family-name',
				},
			},
			{
				key: 'picture',
				type: FieldType.INPUT,
				props: {
					label: 'Picture URL',
					type: 'url',
					autocomplete: 'section-personal photo' as HTMLInputElement['autocomplete'],
					description: 'This picture will be used for your avatar.',
					addonRight: {
						img: 'key',
						rounded: true,
						shim: true,
					},
				},
			},
		],
	},
	// {
	// 	title: 'Contacts',
	// 	description:
	// 		'We will only use your contact details for the described purposes and will not send unsolicited communications.',
	// 	fields: [
	// 		{
	// 			key: 'email',
	// 			type: FieldType.INPUT,
	// 			props: {
	// 				type: 'email',
	// 				label: 'Email',
	// 				required: true,
	// 				autocomplete: 'section-contact email',
	// 				description: 'This email will be used as you account identity.',
	// 			},
	// 		},
	// 		{
	// 			key: 'contactEmail',
	// 			type: FieldType.INPUT,
	// 			props: {
	// 				type: 'email',
	// 				label: 'Contact Email',
	// 				autocomplete: 'section-contact email',
	// 				description:
	// 					'This email will be used for customer support and opt-in communication. If left blank, your primary email will be used.',
	// 			},
	// 		},
	// 		{
	// 			key: 'phoneNumber',
	// 			type: FieldType.INPUT,
	// 			props: {
	// 				type: 'tel',
	// 				label: 'Phone Number',
	// 				autocomplete: 'section-contact tel',
	// 				description: 'This phone number will be used for customer support, account verification and recovery.',
	// 			},
	// 		},
	// 	],
	// },
	// {
	// 	title: 'Address',
	// 	fields: [
	// 		{
	// 			key: 'address.country',
	// 			type: FieldType.SELECT,
	// 			props: {
	// 				label: 'Country',
	// 				required: true,
	// 				autocomplete: 'section-address country',
	// 				options: [
	// 					{ label: 'Portugal', value: 'PT' },
	// 					{ label: 'United States', value: 'US' },
	// 					{ label: 'United Kingdom', value: 'GB' },
	// 				],
	// 			},
	// 		},
	// 		{
	// 			key: 'address.streetAddress',
	// 			type: FieldType.INPUT,
	// 			props: {
	// 				label: 'Street Address',
	// 				required: true,
	// 				autocomplete: 'section-address street-address',
	// 				placeholder: '123 Main St.',
	// 			},
	// 		},
	// 		{
	// 			key: 'address.postalCode',
	// 			type: FieldType.INPUT,
	// 			props: {
	// 				label: 'ZIP / Postal Code',
	// 				required: true,
	// 				autocomplete: 'section-address postal-code',
	// 			},
	// 		},
	// 		{
	// 			key: 'address.locality',
	// 			type: FieldType.INPUT,
	// 			props: {
	// 				label: 'City',
	// 				required: true,
	// 				autocomplete: 'section-address address-level2',
	// 			},
	// 		},
	// 		{
	// 			key: 'address.region',
	// 			type: FieldType.INPUT,
	// 			props: {
	// 				label: 'State / Region',
	// 				required: true,
	// 				autocomplete: 'section-address address-level1',
	// 			},
	// 		},
	// 	],
	// },
]
