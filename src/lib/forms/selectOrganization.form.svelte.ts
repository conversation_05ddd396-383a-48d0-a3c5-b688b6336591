import { FieldType, type Field } from '$lib/modules/Form'

import SelectOrganization from './SelectOrganization.svelte'

import type { Organization } from '$lib/graphql/generated/gateway'
// import { selectOrganizationSnippet } from './selectOrganization.snippet.svelte'

export const selectOrganization = {
	key: 'organizationId',
	type: FieldType.CUSTOM,
	props: {
		label: 'Organization',
		// snippet: selectOrganizationSnippet,
		component: SelectOrganization,
		props: {
			onValueChange: (value: Organization) => {
				return value?._id
			},
		},
		placeholder: 'Search Organization',
		required: true,
	},
} satisfies Field<{ organizationId: string }>
