import { type CreateProductInput } from '$lib/graphql/generated/gateway'
import { FieldType, type Form } from '$lib/modules/Form'

import { selectOrganization } from './selectOrganization.form.svelte'

export const createProductForm = (orgId?: string): Form<CreateProductInput> => [
	{
		title: 'Product Details',
		fields: [
			{
				...(orgId
					? {
							key: 'organizationId',
							type: FieldType.HIDDEN,
							props: {
								required: true,
								default: orgId,
							},
						}
					: selectOrganization),
			},
			{
				key: 'name',
				type: FieldType.INPUT,
				props: {
					required: true,
					label: 'Name',
				},
			},
			{
				key: 'picture',
				type: FieldType.INPUT,
				props: {
					required: true,
					label: 'Picture',
					addonRight: {
						img: 'key',
						shim: true,
					},
					addonDivider: true,
				},
			},
			{
				key: 'description',
				type: FieldType.INPUT,
				props: {
					label: 'Description',
				},
			},
			{
				key: 'url',
				type: FieldType.INPUT,
				props: {
					required: true,
					label: 'External Url',
				},
			},
		],
	},
]
