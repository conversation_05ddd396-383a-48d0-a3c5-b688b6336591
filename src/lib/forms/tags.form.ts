import {
	AsyncProducts,
	AsyncPrograms,
	AttachProductToTags,
	AttachProgramToTags,
	DetachProductFromTags,
	DetachProgramFromTags,
	type ProductDetails,
	type TagsQuery,
} from '$lib/graphql/generated/gateway'
import { FieldType, openFormDrawer, type Form } from '$lib/modules/Form'
import { getModal } from '$lib/modules/Modal'

import { selectOrganization } from './selectOrganization.form.svelte'

import type { QueryReturnType } from '$lib/utils'

export type TagsResult = QueryReturnType<TagsQuery['tags']>

export interface CreateTagsData {
	organizationId: string
	csv: FileList | string
	labels?: string[]
	programId?: string
	productId?: string
	productDetails?: ProductDetails
}

export interface AttachProduct {
	productId: string
	serialNumber?: string
	sku?: string
	batch?: string
	size?: string
	color?: string
	picture?: string
	stamp?: string
}

export interface AttachProgram {
	programId: string
}

export const createTagsForm = (orgId?: string): Form<CreateTagsData> => [
	{
		...(orgId
			? {
					key: 'organizationId',
					type: FieldType.HIDDEN,
					props: {
						required: true,
						default: orgId,
					},
				}
			: selectOrganization),
	},
	{
		title: 'Chips Details',
		tabs: [
			{
				title: 'Upload',
				fields: [
					{
						key: 'csv',
						type: FieldType.FILE_UPLOAD,
						props: {
							accept: '.csv',
							required: true,
						},
					},
				],
			},
			{
				title: 'Text',
				fields: [
					{
						key: 'csv',
						type: FieldType.TEXTAREA,
						props: {
							placeholder: 'Paste CSV here',
							required: true,
						},
					},
				],
			},
		],
	},
	{
		title: 'Tags Details',
		fields: [
			{
				key: 'labels',
				type: FieldType.INPUT_TAGS,
				props: {
					label: 'Labels',
				},
			},
			{
				key: 'programId',
				type: FieldType.AUTOCOMPLETE,
				props: {
					label: 'Program',
					placeholder: 'Select Program',
					options: AsyncPrograms({
						variables: {
							filter: {
								where: {
									organizationId: orgId,
								},
							},
						},
					}).then(({ data }) => {
						return data?.programs?.nodes.map((item) => ({ value: item._id, label: item.name })) ?? []
					}),
				},
			},
			{
				key: 'productId',
				type: FieldType.AUTOCOMPLETE,
				props: {
					label: 'Product',
					placeholder: 'Select Product',
					options: AsyncProducts({
						variables: {
							filter: {
								where: {
									organizationId: orgId,
								},
							},
						},
					}).then(({ data }) => {
						return data?.products?.nodes.map((item) => ({ value: item._id, label: item.name })) ?? []
					}),
				},
			},
		],
		hide: '!data?.organizationId',
	},
	{
		title: 'Product Details',
		fields: [
			{
				key: 'productDetails.serialNumber',
				type: FieldType.INPUT,
				props: {
					label: 'Serial Number',
				},
			},
			{
				key: 'productDetails.sku',
				type: FieldType.INPUT,
				props: {
					label: 'SKU',
				},
			},
			{
				key: 'productDetails.batch',
				type: FieldType.INPUT,
				props: {
					label: 'Batch',
				},
			},
			{
				key: 'productDetails.size',
				type: FieldType.INPUT,
				props: {
					label: 'Size',
				},
			},
			{
				key: 'productDetails.color',
				type: FieldType.INPUT,
				props: {
					label: 'Color',
				},
			},
			{
				key: 'productDetails.picture',
				type: FieldType.INPUT,
				props: {
					label: 'Picture',
					addonRight: {
						img: 'key',
						shim: true,
					},
					addonDivider: true,
				},
			},
			{
				key: 'productDetails.stamp',
				type: FieldType.INPUT,
				props: {
					label: 'Stamp',
					addonRight: {
						img: 'key',
						shim: true,
					},
					addonDivider: true,
				},
			},
		],
		hide: '!data.productId',
	},
]

export const attachProduct = (organizationId: string, tags: TagsResult[]) =>
	openFormDrawer({
		title: 'Attach Product',
		submitLabel: 'Attach',
		fields: [
			{
				key: 'productId',
				type: FieldType.AUTOCOMPLETE,
				props: {
					label: 'Product',
					placeholder: 'Select Product',
					options: AsyncProducts({
						variables: {
							filter: {
								where: {
									organizationId,
								},
							},
						},
					}).then(({ data }) => {
						return data?.products?.nodes.map((item) => ({ value: item._id, label: item.name })) ?? []
					}),
				},
			},
			{
				title: 'Product Details',
				fields: [
					{
						key: 'serialNumber',
						type: FieldType.INPUT,
						props: {
							label: 'Serial Number',
						},
					},
					{
						key: 'sku',
						type: FieldType.INPUT,
						props: {
							label: 'SKU',
						},
					},
					{
						key: 'batch',
						type: FieldType.INPUT,
						props: {
							label: 'Batch',
						},
					},
					{
						key: 'size',
						type: FieldType.INPUT,
						props: {
							label: 'Size',
						},
					},
					{
						key: 'color',
						type: FieldType.INPUT,
						props: {
							label: 'Color',
						},
					},
					{
						key: 'picture',
						type: FieldType.INPUT,
						props: {
							label: 'Picture',
							addonRight: {
								img: 'key',
								shim: true,
							},
							addonDivider: true,
						},
					},
					{
						key: 'stamp',
						type: FieldType.INPUT,
						props: {
							label: 'Stamp',
							addonRight: {
								img: 'key',
								shim: true,
							},
							addonDivider: true,
						},
					},
				],
			},
		] satisfies Form<AttachProduct>,
		onSubmit: async (data: AttachProduct) => {
			await AttachProductToTags({
				variables: {
					filter: {
						where: {
							_id: { $in: tags.map((tag) => tag._id) },
						},
					},
					productId: data.productId,
					productDetails: {
						batch: data.batch,
						color: data.color,
						picture: data.picture,
						serialNumber: data.serialNumber,
						size: data.size,
						sku: data.sku,
						stamp: data.stamp,
					},
				},
				refetchQueries: ['Tags'],
			})
		},
	})

export const detachProduct = (tags: TagsResult[]) =>
	getModal().open({
		type: 'confirm',
		title: 'Detach Product',
		submitClasses: 'preset-filled-error-500',
		body: 'Are you sure you want to detach the <strong class="text-primary-400">Product</strong> from the selected tags?',
		response: async (response: boolean) => {
			if (response) {
				await DetachProductFromTags({
					variables: {
						filter: {
							where: {
								_id: { $in: tags.map((tag) => tag._id) },
							},
						},
					},
					refetchQueries: ['Tags'],
				})
			}
		},
	})

export const attachProgram = (organizationId: string, tags: TagsResult[]) =>
	openFormDrawer({
		title: 'Attach Program',
		submitLabel: 'Attach',
		fields: [
			{
				key: 'programId',
				type: FieldType.AUTOCOMPLETE,
				props: {
					label: 'Program',
					placeholder: 'Select Program',
					options: AsyncPrograms({
						variables: {
							filter: {
								where: {
									organizationId,
								},
							},
						},
					}).then(({ data }) => {
						return data?.programs?.nodes.map((item) => ({ value: item._id, label: item.name })) ?? []
					}),
				},
			},
		] satisfies Form<AttachProgram>,
		onSubmit: async (data: AttachProgram) => {
			await AttachProgramToTags({
				variables: {
					filter: {
						where: {
							_id: { $in: tags.map((tag) => tag._id) },
						},
					},
					programId: data.programId,
				},
				refetchQueries: ['Tags'],
			})
		},
	})

export const detachProgram = (tags: TagsResult[]) =>
	getModal().open({
		type: 'confirm',
		title: 'Detach Program',
		submitClasses: 'preset-filled-error-500',
		body: 'Are you sure you want to detach the <strong class="text-primary-400">Program</strong> from the selected tags?',
		response: async (response: boolean) => {
			if (response) {
				await DetachProgramFromTags({
					variables: {
						filter: {
							where: {
								_id: { $in: tags.map((tag) => tag._id) },
							},
						},
					},
					refetchQueries: ['Tags'],
				})
			}
		},
	})
