<!-- <script lang="ts" module>
	import SelectOrganization from './SelectOrganization.svelte'

	import type { OrganizationQuery } from '$lib/graphql/generated/gateway'
	import type { CustomPropertiesSnippet } from '$lib/modules/Form'

	export { selectOrganizationSnippet }
</script>

{#snippet selectOrganizationSnippet({
	value,
	disabled,
	required,
	onValueChange,
}: CustomPropertiesSnippet<OrganizationQuery['organization'], string>)}
	<SelectOrganization {value} {disabled} {required} {onValueChange} />
{/snippet} -->
