import { type CreateOrganizationInput } from '$lib/graphql/generated/gateway'
import { FieldType, type Form } from '$lib/modules/Form'

export const createOrganizationForm = (): Form<CreateOrganizationInput> => [
	{
		title: 'Organization Details',
		fields: [
			{
				key: 'name',
				type: FieldType.INPUT,
				props: {
					required: true,
					label: 'Name',
					placeholder: 'Acme Inc.',
					minLength: 8,
					maxLength: 50,
					autocomplete: 'organization',
				},
			},
			{
				key: 'logo',
				type: FieldType.INPUT,
				props: {
					type: 'url',
					required: true,
					label: 'Logo Url',
					addonRight: {
						img: 'key',
						shim: true,
					},
					addonDivider: true,
					placeholder: 'https://example.com/logo.png',
				},
			},
			// {
			// 	key: 'tag',
			// 	type: FieldType.INPUT,
			// 	props: {
			// 		required: true,
			// 		label: 'Tag'
			// 	}
			// },
			// {
			// 	key: 'description',
			// 	type: FieldType.INPUT,
			// 	props: {
			// 		label: 'Description',
			// 	},
			// },
			// {
			// 	key: 'website',
			// 	type: FieldType.INPUT,
			// 	props: {
			// 		label: 'Website',
			// 		type: 'url',
			// 		placeholder: 'https://example.com',
			// 		autocomplete: 'url',
			// 	},
			// },
		],
	},
	{
		title: 'Contacts',
		description:
			'We will only use your contact details for the described purposes and will not send unsolicited communications.',
		fields: [
			{
				key: 'billing',
				type: FieldType.INPUT,
				props: {
					required: true,
					label: 'Billing Email',
					type: 'email',
					autocomplete: 'billing email',
					placeholder: '<EMAIL>',
					description: 'This email will be used for billing and account verification only.',
				},
			},
			{
				key: 'contactEmail',
				type: FieldType.INPUT,
				props: {
					type: 'email',
					// required: true,
					label: 'Contact Email',
					autocomplete: 'work email',
					placeholder: '<EMAIL>',
					description: 'This email will be used for customer support and opt-in communication.',
				},
			},
			{
				key: 'contactPhoneNumber',
				type: FieldType.INPUT,
				props: {
					type: 'tel',
					label: 'Contact Phone Number',
					autocomplete: 'work tel',
					placeholder: '****** 555 5555',
					description: 'This phone number will be used for customer support and opt-in communication.',
				},
			},
		],
	},
]
