<script lang="ts">
	import { AsyncOrganizations, type OrganizationQuery } from '$lib/graphql/generated/gateway'
	import { SearchAutocomplete } from '$lib/modules/Form'

	const {
		value,
		disabled = false,
		required = false,

		selectionBehavior = 'replace',

		onValueChange,
	}: {
		value?: string
		disabled?: boolean
		required?: boolean

		selectionBehavior?: 'clear' | 'replace' | 'preserve'

		onValueChange?: (value: OrganizationQuery['organization'] | undefined) => void | Promise<void>
	} = $props()

	const options = async (search: string) =>
		AsyncOrganizations({
			variables: {
				filter: {
					where: {
						$or: [
							{ _id: search },
							{ name: { $regex: search, $options: 'smix' } },
							{ website: { $regex: search, $options: 'smix' } },
							{ tag: { $regex: search, $options: 'smix' } },
							{ billing: { $regex: search, $options: 'smix' } },
							{ contactEmail: { $regex: search, $options: 'smix' } },
							{ contactPhoneNumber: { $regex: search, $options: 'smix' } },
							{ customerId: search },
						],
					},
					limit: 50,
				},
			},
			fetchPolicy: 'network-only',
		}).then(
			({ data }) =>
				data?.organizations?.nodes?.map((org) => ({
					meta: org,
					value: org._id as string,
					label: org.name,
					image: org.logo,
				})) || [],
		)
</script>

<SearchAutocomplete
	placeholder="Select Organization"
	{value}
	{required}
	{disabled}
	{options}
	debounce={250}
	{selectionBehavior}
	onValueChange={(_, selected) => {
		if (selected?.length) {
			onValueChange?.(selected[0].meta as OrganizationQuery['organization'])
		} else {
			onValueChange?.(undefined)
		}
	}}
/>
