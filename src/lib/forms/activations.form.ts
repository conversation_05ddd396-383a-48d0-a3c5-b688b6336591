import { ActivityType, AsyncProducts, AsyncPrograms } from '$lib/graphql/generated/gateway'
import { FieldType, type Form } from '$lib/modules/Form'

import { selectOrganization } from './selectOrganization.form.svelte'

export interface CreateActivationData {
	organizationId: string
	name: string
	url: string
	picture: string
	description: string
	type: ActivityType
	date: Date
	location: string
	range: {
		start: Date
		end: Date
	}
	limit: number
	productIds: string[]
	programIds: string[]
	labels: string[]
	filterRange: {
		start: Date
		end: Date
	}
}

export const createActivationForm = (orgId?: string): Form<CreateActivationData> => [
	{
		title: 'Activation Details',
		fields: [
			{
				...(orgId
					? {
							key: 'organizationId',
							type: FieldType.HIDDEN,
							props: {
								required: true,
								default: orgId,
							},
						}
					: selectOrganization),
			},
			{
				key: 'name',
				type: FieldType.INPUT,
				props: {
					required: true,
					label: 'Name',
				},
			},
			{
				key: 'url',
				type: FieldType.INPUT,
				props: {
					required: true,
					label: 'External Url',
				},
			},
			{
				key: 'picture',
				type: FieldType.INPUT,
				props: {
					label: 'Picture',
					addonRight: {
						img: 'key',
						shim: true,
					},
					addonDivider: true,
				},
			},
			{
				key: 'description',
				type: FieldType.INPUT,
				props: {
					label: 'Description',
				},
			},
			{
				key: 'type',
				type: FieldType.SELECT,
				props: {
					label: 'Event Type',
					options: [
						{ value: ActivityType.InPerson, label: 'In Person' },
						{ value: ActivityType.Virtual, label: 'Virtual' },
					],
				},
			},
			{
				key: 'date',
				type: FieldType.DATEPICKER,
				props: {
					label: 'Event Date',
					showTimePicker: true,
				},
			},
			{
				key: 'location',
				type: FieldType.INPUT,
				props: {
					label: 'Event Location',
				},
			},
		],
	},
	{
		title: 'Activation Constrains',
		fields: [
			{
				key: 'range',
				type: FieldType.DATEPICKER,
				props: {
					label: 'Opt-in Period',
					isRange: true,
					isMultipane: true,
					showTimePicker: true,
					start: 'start',
					end: 'end',
				},
			},
			{
				key: 'limit',
				type: FieldType.INPUT,
				props: {
					type: 'number',
					label: 'Limit Participants',
					min: '1',
					max: '1000000',
				},
			},
		],
	},
	{
		title: 'Filter',
		fields: [
			{
				key: 'productIds',
				type: FieldType.AUTOCOMPLETE,
				props: {
					label: 'Products',
					multiple: true,
					selectionBehavior: 'preserve',
					placeholder: 'Select Products',
					options: AsyncProducts({
						variables: {
							filter: {
								where: {
									organizationId: orgId,
								},
							},
						},
					}).then(({ data }) => {
						return data?.products?.nodes.map((item) => ({ value: item._id, label: item.name })) || []
					}),
				},
				hide: '!data?.organizationId',
			},
			{
				key: 'programIds',
				type: FieldType.AUTOCOMPLETE,
				props: {
					label: 'Programs',
					multiple: true,
					selectionBehavior: 'preserve',
					placeholder: 'Select Programs',
					options: AsyncPrograms({
						variables: {
							filter: {
								where: {
									organizationId: orgId,
								},
							},
						},
					}).then(({ data }) => {
						return data?.programs?.nodes.map((item) => ({ value: item._id, label: item.name })) || []
					}),
				},
				hide: '!data?.organizationId',
			},
			{
				key: 'labels',
				type: FieldType.INPUT_TAGS,
				props: {
					label: 'Labels',
				},
				hide: '!data?.organizationId',
			},
			{
				key: 'filterRange',
				type: FieldType.DATEPICKER,
				props: {
					label: 'Tag Registration Interval',
					isRange: true,
					isMultipane: true,
					showTimePicker: true,
					start: 'start',
					end: 'end',
				},
			},
		],
	},
]
