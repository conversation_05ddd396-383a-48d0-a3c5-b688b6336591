import { FieldType, type Form } from '$lib/modules/Form'

import WebhookEventSelector from '$lib/components/Backoffice/WebhookEventSelector.svelte'

import type { CreateWebhookInput } from '$lib/graphql/generated/gateway'

export const createWebhookForm = (): Form<CreateWebhookInput> => [
	{
		key: 'active',
		type: FieldType.INPUT,
		props: {
			label: 'Active',
			type: 'checkbox',
			description: 'We will deliver event details when this hook is triggered.',
		},
	},
	{
		key: 'url',
		type: FieldType.INPUT,
		props: {
			type: 'url',
			label: 'Payload URL',
			required: true,
			placeholder: 'https://example.com/webhook',
			description: "We'll send a request to this URL with details of any subscribed events.",
		},
	},
	{
		key: 'secret',
		type: FieldType.INPUT,
		props: {
			type: 'text',
			label: 'Webhook Secret',
			description: 'This secret will be used to sign the webhook payload for security verification.',
		},
	},
	{
		key: 'break',
		type: FieldType.BREAK,
		props: {
			label: ' Which events would you like to trigger this webhook?',
		},
	},
	{
		key: 'subscribeTo',
		type: FieldType.CUSTOM,
		props: {
			component: WebhookEventSelector,
		},
	},
]
