import { type CreateProgramInput } from '$lib/graphql/generated/gateway'
import { FieldType, type Form } from '$lib/modules/Form'

import { selectOrganization } from './selectOrganization.form.svelte'

export const createProgramForm = (orgId?: string): Form<CreateProgramInput> => [
	{
		title: 'Program Details',
		fields: [
			{
				...(orgId
					? {
							key: 'organizationId',
							type: FieldType.HIDDEN,
							props: {
								required: true,
								default: orgId,
							},
						}
					: selectOrganization),
			},
			{
				key: 'name',
				type: FieldType.INPUT,
				props: {
					required: true,
					label: 'Name',
				},
			},
		],
	},
]
