import type { Action } from 'svelte/action'

// keep track of which callback is associated with each element
type IntersectionCallback = (entry: IntersectionObserverEntry) => void
const intersectionCallbacks = new WeakMap<Element, IntersectionCallback>()

// use a single intersection observer instance per options
const intersectionObservers = new WeakMap<IntersectionObserverInit, IntersectionObserver>()

function createObserver(init: IntersectionObserverInit) {
	const observer = new IntersectionObserver((entries) => {
		for (const entry of entries) {
			const callback = intersectionCallbacks.get(entry.target)
			if (callback) {
				callback(entry)
			}
		}
	}, init)
	intersectionObservers.set(init, observer)
	return observer
}

export interface Options extends IntersectionObserverInit {
	callback: IntersectionCallback
}

// separated from action to make it easier to destroy / recreate when options change
function observe(target: Element, options: Options) {
	const { callback } = options

	const observer = intersectionObservers.get(options) || createObserver(options)

	intersectionCallbacks.set(target, callback)
	observer.observe(target)

	return () => {
		observer.unobserve(target)
		intersectionCallbacks.delete(target)
	}
}

export const intersect: Action<Element, Options> = (target, args) => {
	let unobserve = observe(target, args)

	$effect(() => {
		unobserve()
		unobserve = observe(target, args)

		return () => {
			unobserve()
		}
	})
}
