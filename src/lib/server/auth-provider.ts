import { SvelteKitAuth } from '@auth/sveltekit'

import { internalUrls } from '$lib/config'

import type { OAuthUserConfig, OIDCConfig } from '@auth/core/providers'
import type { Profile, TokenSet } from '@auth/core/types'

import { VITE_OIDC_ISSUER, AUTH_SECRET, VITE_OIDC_SECRET } from '$env/static/private'

const scope = 'openid email profile roles'

function formUrlEncode(token: string) {
	return encodeURIComponent(token).replace(/%20/g, '+')
}
function clientSecretBasic(clientId: string, clientSecret: string) {
	const username = formUrlEncode(clientId)
	const password = formUrlEncode(clientSecret)
	const credentials = btoa(`${username}:${password}`)
	return `Basic ${credentials}`
}

export default function OpenIdConnect(options: OAuthUserConfig<Profile>): OIDCConfig<Profile> {
	return {
		id: 'oidc',
		name: 'OpenIdConnect',
		type: 'oidc',
		idToken: true,
		checks: ['pkce', 'state', 'nonce'],
		authorization: {
			params: {
				scope,
			},
		},
		client: {
			token_endpoint_auth_method: 'client_secret_basic',
		},
		profile(profile) {
			return {
				id: profile.sub,
				name: profile.name,
				email: profile.email,
				image: profile.picture,
			}
		},
		options,
	} as OIDCConfig<Profile>
}

export const clientId = (url: URL) => (internalUrls.includes(url.host) ? 'web' : url.host)

export const { handle, signIn, signOut } = SvelteKitAuth(async ({ url }) => ({
	providers: [
		OpenIdConnect({
			clientId: clientId(url),
			clientSecret: VITE_OIDC_SECRET,
			issuer: VITE_OIDC_ISSUER,
		}),
	],
	callbacks: {
		jwt: async ({ token, account }) => {
			if (account) {
				// Save the access token and refresh token in the JWT on the initial login
				return {
					user_id: account.providerAccountId,
					access_token: account.access_token,
					id_token: account.id_token,
					expires_at: account.exp ?? Math.floor(+new Date() / 1000 + (account.expires_in ?? 1)),
					refresh_token: account.refresh_token,
				}
			}

			if (+new Date() < (token.expires_at as number) * 1000 + 5000) {
				// If the access token has not expired yet, return it
				return token
			}

			if (token.refresh_token) {
				// If the access token has expired, try to refresh it
				try {
					const id = clientId(url)

					const response = await fetch(`${VITE_OIDC_ISSUER}/token`, {
						headers: {
							'Content-Type': 'application/x-www-form-urlencoded',
							Authorization: clientSecretBasic(id, VITE_OIDC_SECRET),
						},
						body: new URLSearchParams({
							client_id: id,
							grant_type: 'refresh_token',
							refresh_token: token.refresh_token as string,
						}),
						method: 'POST',
					})

					const tokenSet: TokenSet = await response.json()

					if (!response.ok) throw tokenSet

					return {
						...token, // Keep the previous token properties
						access_token: tokenSet.access_token,
						id_token: tokenSet.id_token ?? token.id_token,
						expires_at: Math.floor(+new Date() / 1000 + (tokenSet.expires_in ?? 1)),
						// Fall back to old refresh token, but note that
						// many providers may only allow using a refresh token once.
						refresh_token: tokenSet.refresh_token ?? token.refresh_token,
					}
				} catch {
					// The error property will be used client-side to handle the refresh token error
					return { ...token, error: 'RefreshAccessTokenError' }
				}
			}

			throw new TypeError('Missing refresh_token')
		},
		session: ({ session, token }) => {
			return {
				...session,
				user_id: token.user_id as string,
				access_token: token.access_token as string,
				expires_at: token.expires_at as number,
				id_token: token.id_token as string,
				error: token.error as 'RefreshAccessTokenError',
			} satisfies App.CustomSession
		},
		redirect: async ({ url, baseUrl }) => {
			const indexOfPortRedirect = url.indexOf('postLoginRedirect=')
			if (indexOfPortRedirect > -1) {
				const urlRedirect = url.substring(indexOfPortRedirect + 18)
				return `${baseUrl}${decodeURIComponent(urlRedirect)}`
			}

			// Allows relative callback URLs
			if (url.startsWith('/')) return `${baseUrl}${url}`
			// Allows callback URLs on the same origin
			else if (new URL(url).origin === baseUrl) return url
			return baseUrl
		},
	},
	trustHost: true,
	skipCSRFCheck: true as never, // TODO: Fix this
	secret: AUTH_SECRET,
}))
