<script lang="ts">
	import { signIn } from '@auth/sveltekit/client'
	import { AppBar } from '@skeletonlabs/skeleton-svelte'

	import { page } from '$app/state'

	import { Hamburger } from '$lib/components/SideBarMenu'
	import AppShell from '$lib/modules/AppShell'
	import { NotificationBell } from '$lib/modules/Notifications'
	import Search from '$lib/modules/Search'

	import Logo from '$lib/components/Logo.svelte'
	import { getApp } from '$lib/services/app.svelte'
	import { getAuth } from '$lib/services/auth.svelte'

	const app = getApp()
	const auth = getAuth()
</script>

<AppShell sticky="header">
	<!-- Header -->
	{#snippet header()}
		<AppBar background="bg-surface-50 dark:bg-surface-900" shadow="shadow-xs">
			{#snippet lead()}
				<!-- Logo -->
				<a rel="prefetch" class="brand flex items-center" href={app.homePath}>
					{#if page?.data?.organization?.logo}
						<img src={page?.data?.organization?.logo} alt="" class="h-[40px]" />
					{:else}
						<Logo class="mx-auto h-10 w-auto fill-[#15171f] dark:fill-white" type="logo" />
					{/if}
					<span class="ml-4 hidden text-xl font-extralight tracking-wider md:block"
						>{page?.data?.organization?.name || ''}</span
					>
				</a>
			{/snippet}

			{#snippet trail()}
				<Search />

				{#if auth.isAuthenticated}
					<NotificationBell />
				{/if}

				{#if !auth.isAuthenticated}
					<button class="btn hover:preset-tonal-primary" onclick={() => signIn('oidc')}>Log in</button>
				{/if}

				<Hamburger />
			{/snippet}
		</AppBar>
	{/snippet}

	{#if page}
		<div class="flex size-full items-center justify-center">
			<div class="space-y-4 text-center">
				<Logo class="mx-auto h-20 w-auto fill-[#15171f] dark:fill-white" />

				<h2 class="h2">
					{page.status}: {#if page.error}
						{page.error.message}
					{/if}
				</h2>

				<p>We're sorry, something went wrong.</p>
			</div>
		</div>
	{/if}
</AppShell>
