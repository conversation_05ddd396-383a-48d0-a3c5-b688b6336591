<script lang="ts">
	import { signIn } from '@auth/sveltekit/client'

	import { page } from '$app/state'

	import AppShell from '$lib/modules/AppShell'

	import Logo from '$lib/components/Logo.svelte'

	const templateVariables = $derived.by(() => {
		if (!page.data.organization?.template) return ''

		let result = ''

		if (page.data.organization.template.backgroundColor) {
			result += `--background-color: ${page.data.organization.template.backgroundColor};`
		}

		if (page.data.organization.template.textColor) {
			result += `--text-color: ${page.data.organization.template.textColor};`
		}

		if (page.data.organization.template.primaryColor) {
			result += `--primary-color: ${page.data.organization.template.primaryColor};`
		}
		if (page.data.organization.template.primaryTextColor) {
			result += `--primary-text-color: ${page.data.organization.template.primaryTextColor};`
		}

		if (page.data.organization.template.backgroundImageUrl) {
			result += `background-image: url(${page.data.organization.template.backgroundImageUrl})`
		}

		return result
	})
</script>

{#if templateVariables !== undefined}
	<div class="tag-container" style={templateVariables}>
		<AppShell
			shell="px-6 py-12 lg:px-8"
			colShell="sm:mx-auto sm:items-center"
			rowShell="justify-between mx-auto"
			pageHeaderShell="sm:mx-auto sm:w-full sm:max-w-sm pt-20 sm:pt-0"
			mainShell="flex items-center sm:mt-10 sm:mb-10"
			pageFooterShell="sm:mx-auto sm:w-full sm:max-w-sm text-center text-sm"
		>
			{#snippet pageHeader()}
				{#if page?.data?.organization?.logo}
					<img src={page?.data?.organization?.logo} alt="" class="mx-auto h-20 w-auto" />
				{:else}
					<Logo
						class="mx-auto h-20 w-auto fill-white"
						type="logo-square-framed"
						color={page?.data?.organization?.template?.textColor || '#ffffff'}
					/>
				{/if}
			{/snippet}

			<div class="flex flex-col items-center justify-center space-y-6">
				<button
					type="button"
					class="bg-tag-primary preset-primary btn w-full max-w-md rounded-full px-8 py-3 text-xl"
					onclick={() => signIn('oidc')}
				>
					Sign In
				</button>

				<div class="self-start px-1 text-left text-sm">
					By signin in, you agree to the
					<a class="font-semibold text-slate-400 hover:text-slate-300" target="_blank" href="/terms_and_conditions"
						>Terms of Service</a
					>
					and
					<a class="font-semibold text-slate-400 hover:text-slate-300" target="_blank" href="/privacy_policy"
						>Privacy Policy</a
					>
				</div>
			</div>

			{#snippet pageFooter()}
				{#if page?.data?.organization?.name}
					<div class="mt-10 align-middle">
						<h2 class="text-primary mr-1 inline leading-9">Powered by</h2>
						<img class="inline h-6" src="/icons/icon-32x32.png" alt="" />
					</div>
				{/if}
			{/snippet}
		</AppShell>
	</div>
{/if}

<style>
	:root {
		--background-color: linear-gradient(180deg, rgba(122, 172, 210, 1) 0%, rgba(29, 49, 82, 1) 100%);

		--text-color: rgb(255, 255, 255);

		--primary-color: #5c86ac;
		--primary-text-color: rgb(255, 255, 255);
	}

	.tag-container {
		background: var(--background-color, rgba(122, 172, 210, 1));
		background-size: cover;
		color: var(--text-color, rgb(255, 255, 255));
	}

	.btn.preset-primary {
		background: var(--primary-color, #5c86ac);
		color: var(--primary-text-color, rgb(255, 255, 255));
	}

	.text-primary {
		color: var(--primary-color, #5c86ac);
	}
</style>
