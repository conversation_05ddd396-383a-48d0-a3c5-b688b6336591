<script>
	import { signIn } from '@auth/sveltekit/client'

	import Logo from '$lib/components/Logo.svelte'
	import { getApp } from '$lib/services/app.svelte'
	import { getAuth } from '$lib/services/auth.svelte'

	const app = getApp()
	const auth = getAuth()
</script>

<div class="flex size-full items-center justify-center">
	<div class="space-y-4 text-center">
		<Logo class="mx-auto h-20 w-auto fill-[#15171f] dark:fill-white" />

		{#if auth.session}
			<h2 class="h2">Unauthorized Access</h2>

			<p>You are trying to access a restricted page for which you do not have the necessary permissions.</p>

			<a class="preset-filled btn" href={app.homePath}>Go to Home</a>
		{:else}
			<h2 class="h2">Authorization Required</h2>

			<p>You are trying to access a restricted page.</p>

			<p>Please sign in to continue.</p>

			<button class="preset-filled-primary-500 btn" onclick={() => signIn('oidc')}>Sign In</button>
		{/if}
	</div>
</div>
