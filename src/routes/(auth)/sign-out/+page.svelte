<script lang="ts">
	import { signOut } from '@auth/sveltekit/client'
	import { redirect } from '@sveltejs/kit'
	import { onMount } from 'svelte'

	import { page } from '$app/state'

	onMount(async () => {
		const session = page.data?.session

		if (!session || !session?.id_token) redirect(301, '/')

		const { end_session_endpoint } = await (
			await fetch(`${import.meta.env.VITE_OIDC_ISSUER}/.well-known/openid-configuration`)
		).json()

		const endsessionParams = new URLSearchParams({
			post_logout_redirect_uri: `${window.location.origin}/`,
			id_token_hint: session?.id_token,
		})

		await signOut()

		location.href = `${end_session_endpoint}?${endsessionParams}`
	})
</script>
