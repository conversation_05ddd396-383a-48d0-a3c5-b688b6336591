<script lang="ts">
	import { signIn } from '@auth/sveltekit/client'
	import { Toaster } from '@skeletonlabs/skeleton-svelte'
	import 'iconify-icon'

	import { browser } from '$app/environment'
	import { afterNavigate, beforeNavigate } from '$app/navigation'
	import { page } from '$app/state'

	import { appPath, customDomainPathsRegex } from '$lib/config'
	import { modalComponentRegistry } from '$lib/modals/registry'
	import { toaster } from '$lib/modules/Notifications'
	import { initGlobalServices } from '$lib/services'

	import { initSideBarMenu } from '$lib/components/SideBarMenu/state.svelte'
	import CookiesBanner from '$lib/modules/GDPR/CookiesBanner.svelte'
	import Modal from '$lib/modules/Modal/Modal.svelte'

	import '../app.css'

	if (browser && page.data.session?.error === 'RefreshAccessTokenError') {
		signIn('oidc') // Force sign in to hopefully resolve error
	}

	const { children } = $props()

	const { theme } = initGlobalServices()

	initSideBarMenu()

	// Scroll heading into view
	function scrollHeadingIntoView() {
		if (!window.location.hash) return
		const elemTarget: HTMLElement | null = document.querySelector(window.location.hash)
		if (elemTarget) elemTarget.scrollIntoView({ behavior: 'smooth' })
	}

	const appName = $derived(page.data.customDomain && page.data.organization ? page.data.organization.name : 'DapTap')

	const metaDefaults = {
		description: '',
		image: '/icons/icon-256x256.png',
	}

	const post = $derived(page.data.post)

	function makeTitle(slug: string) {
		const words = slug.split('-')

		for (let i = 0; i < words.length; i++) {
			const word = words[i]
			words[i] = word.charAt(0).toUpperCase() + word.slice(1)
		}

		return words.join(' ')
	}

	function titleFromPathname() {
		const filter = Object.values(page.params)
		const subtitles = page.url.pathname
			.split('/')
			.slice(1)
			.filter((s) => !filter.includes(s))
			.map(makeTitle)

		if (subtitles.length < 2) {
			return `${appName} · ${subtitles.join(' · ')}`
		} else {
			return subtitles.slice(subtitles.length - Math.min(subtitles.length, 3), subtitles.length).join(' · ')
		}
	}

	const meta = $derived({
		title: post
			? `${appName} Blog · ${post.title}`
			: page.data.meta
				? page.data.meta.title
				: page.url.pathname !== '/'
					? titleFromPathname()
					: appName,
		description: post
			? post.description || post.excerpt
			: page.data.meta
				? page.data.meta.description
				: metaDefaults.description,
		image: post ? post.og_image || post.feature_image : page.data.meta ? page.data.meta.image : metaDefaults.image,
		// Article
		article: post ? { publishTime: post.created_at, modifiedTime: post.updated_at, author: 'post.author' } : undefined,
	})

	// Lifecycle
	beforeNavigate(({ type, to, cancel }) => {
		if (type !== 'leave' && to && page.data.customDomain) {
			if (appPath === '' && to.url.pathname !== '/') {
				if (!customDomainPathsRegex.test(to.url.pathname)) {
					console.warn('Blocked navigation to', to.url.pathname)
					cancel()
				}
			}
		}
	})

	afterNavigate(({ from, to }) => {
		// Scroll to top
		const isNewPage = from && to && from.route.id !== to.route.id
		const elemPage = document.querySelector('#page')
		if (isNewPage && elemPage !== null) {
			elemPage.scrollTop = 0
		}
		// Scroll heading into view
		scrollHeadingIntoView()
	})

	function onkeydown(e: KeyboardEvent): void {
		if (e.ctrlKey && e.key === 't') {
			// Prevent default browser behavior of focusing URL bar
			e.preventDefault()
			// NOTE: using stopPropagation to override Chrome for Windows search shortcut
			e.stopPropagation()

			theme.toggleMode()
		}
	}
</script>

<svelte:head>
	<title>{meta.title}</title>
	<link rel="icon" href={page.data?.organization?.logo ?? '/favicon.ico'} />
	<!-- Meta Tags -->
	<meta name="title" content={meta.title} />
	<meta name="description" content={meta.description} />
	<meta
		name="keywords"
		content="svelte, sveltekit, web, ui, components, reactive, accessibility, typescript, css, open source"
	/>
	<meta name="theme-color" content="" />
	<meta name="author" content="Reality Connect" />
	<!-- Open Graph - https://ogp.me/ -->
	<meta property="og:site_name" content={appName} />
	<meta property="og:type" content="website" />
	<meta property="og:url" content="https://{page.url.host}{page.url.pathname}" />
	<meta property="og:locale" content="en_US" />
	<meta property="og:title" content={meta.title} />
	<meta property="og:description" content={meta.description} />
	<meta property="og:image" content={meta.image} />
	<meta property="og:image:secure_url" content={meta.image} />

	<!-- OG: Article -->
	{#if meta.article}
		<meta property="article:published_time" content={meta.article.publishTime} />
		<meta property="article:modified_time" content={meta.article.modifiedTime} />
		<meta property="article:author" content={meta.article.author} />
	{/if}
</svelte:head>

<svelte:window {onkeydown} />

<Modal components={modalComponentRegistry} />

<Toaster {toaster}></Toaster>

{@render children?.()}

<CookiesBanner showEditIcon={false} />
