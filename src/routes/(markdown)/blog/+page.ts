import type { PageLoad } from './$types'

const MAX_POSTS = 10

export const load: PageLoad = async () => {
	const modules = import.meta.glob('$lib/blog/*.{md,svx,svelte.md}')

	const postPromises = Object.entries(modules).map(([path, resolver]) =>
		resolver().then((post) => ({
			path: path.replace('/src/lib/blog/', '').replace(/\.[^/.]+$/, ''),
			meta: (post as unknown as App.MdsvexFile).metadata,
		})),
	)

	const posts = await Promise.all(postPromises)
	const publishedPosts = posts.filter((post) => post.meta.published).slice(0, MAX_POSTS)

	publishedPosts.sort((a, b) => (new Date(a.meta.date) > new Date(b.meta.date) ? -1 : 1))

	return { posts: publishedPosts }
}
