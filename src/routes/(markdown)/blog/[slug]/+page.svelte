<script lang="ts">
	import { getLocaleForDateFormat } from '$lib/utils'

	import type { PageData } from './$types'

	const { data }: { data: PageData } = $props()

	const Content = $derived(data.post?.content)
</script>

<article class="prose dark:prose-invert mx-auto">
	<div
		class="card bg-cover text-center"
		style={data.post.feature_image ? `background-image: url(${data.post.feature_image})` : ''}
	>
		<div class="bg-surface-800 bg-opacity-80 size-full px-12 pb-12">
			<h1 class="pt-20 pb-4 font-bold text-white">{data.post.title}</h1>

			<p class="text-white">
				Published: {new Date(data.post.created_at).toLocaleDateString(getLocaleForDateFormat(), { dateStyle: 'full' })}
			</p>
			{#if data.post.updated_at}
				<p class="text-white">
					Updated: {new Date(data.post.updated_at).toLocaleDateString(getLocaleForDateFormat(), { dateStyle: 'full' })}
				</p>
			{/if}
		</div>
	</div>
	<Content />
</article>
