<script lang="ts">
	import { getLocaleForDateFormat } from '$lib/utils'

	import type { PageData } from './$types'

	const { data }: { data: PageData } = $props()
</script>

<p>
	This is a minimalistic example of a blog built with <a href="https://kit.svelte.dev">SvelteKit</a>
	and <a href="https://mdsvex.com/">MDsveX</a>.
	<a href="https://github.com/mvasigh/sveltekit-mdsvex-blog">View source code on Github.</a>
</p>

{#each data.posts as { path, meta } (path)}
	<article>
		<h2><a href="/blog/{path}">{meta.title}</a></h2>
		<p>{meta.description}</p>
		<p>
			<small>
				{meta.author} on {new Date(meta.updated_at || meta.created_at).toLocaleDateString(getLocaleForDateFormat(), {
					dateStyle: 'full',
				})}
			</small>
		</p>
	</article>
{/each}
