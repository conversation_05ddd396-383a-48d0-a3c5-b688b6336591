<script lang="ts">
	import { signIn } from '@auth/sveltekit/client'
	import { AppBar } from '@skeletonlabs/skeleton-svelte'

	import { Hamburger } from '$lib/components/SideBarMenu'
	import AppShell from '$lib/modules/AppShell'
	import { NotificationBell } from '$lib/modules/Notifications'

	import Footer from '$lib/components/Footer.svelte'
	import Logo from '$lib/components/Logo.svelte'
	import { getApp } from '$lib/services/app.svelte'
	import { getAuth } from '$lib/services/auth.svelte'

	import type { Snippet } from 'svelte'

	const { children }: { children?: Snippet } = $props()

	const app = getApp()
	const auth = getAuth()
</script>

<AppShell sticky="header" colShell="container mx-auto" rowShell="mx-auto" mainShell="py-12 px-4">
	<!-- Header -->
	{#snippet header()}
		<AppBar background="bg-surface-50 dark:bg-surface-900" shadow="shadow-xs">
			{#snippet lead()}
				<!-- Logo -->
				<a rel="prefetch" class="brand flex items-center" href={app.homePath}>
					<Logo class="mx-auto h-10 w-auto fill-[#15171f] dark:fill-white" type="logo" />
				</a>
			{/snippet}

			{#snippet trail()}
				<a class="btn hidden sm:inline-flex" rel="prefetch" href="/pricing">Pricing</a>
				<!-- <a class="btn hidden sm:inline-flex" rel="prefetch" href="/blog">Blog</a> -->

				{#if auth.isAuthenticated}
					<NotificationBell />
				{/if}

				{#if !auth.isAuthenticated}
					<button class="btn hover:preset-tonal-primary" onclick={() => signIn('oidc')}>Log in</button>
				{/if}

				<Hamburger />
			{/snippet}
		</AppBar>
	{/snippet}

	{@render children?.()}

	<!-- Page Footer -->
	{#snippet footer()}
		<Footer />
	{/snippet}
</AppShell>
