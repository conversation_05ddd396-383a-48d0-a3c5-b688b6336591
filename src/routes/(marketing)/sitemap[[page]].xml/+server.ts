import { type RequestHandler } from '@sveltejs/kit'
import { response } from 'super-sitemap'

import { authPaths } from '$lib/config'

export const GET: RequestHandler = async ({ params, url }) => {
	const modules = import.meta.glob('$lib/blog/*.{md,svx,svelte.md}')
	const slugs = Object.keys(modules).map((path) => path.replace('/src/lib/blog/', '').replace(/\.[^/.]+$/, ''))
	const authPatterns = authPaths.filter((path) => path.length).map((path) => `^/${path}.*`)

	return await response({
		origin: `https://${url.host}`,
		page: params.page,
		excludeRoutePatterns: [...authPatterns, '.*\\(app\\).*', '.*\\(auth\\).*', '^/backoffice.*', '^/tap.*', '^/tag.*'],
		paramValues: {
			'/blog/[slug]': slugs,
		},
		additionalPaths: [
			'/data_processing_agreement',
			'/privacy_policy',
			'/terms_and_conditions',
			// '/cookie_policy',
			'/gdpr',
		],
	})
}
