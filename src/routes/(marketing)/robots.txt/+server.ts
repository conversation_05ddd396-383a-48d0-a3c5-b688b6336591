import { appPath } from '$lib/config'

import type { RequestHandler } from '@sveltejs/kit'

export const GET: RequestHandler = async ({ url }) => {
	// prettier-ignore
	const body = [
    'User-agent: *',
		`Disallow: /${appPath}`,
		'Disallow: /backoffice',
		'Disallow: /settings',
		'Disallow: /org',
		'Disallow: /tag',
    '',
    `Sitemap: https://${url.host}/sitemap.xml`
  ].join('\n').trim();

	const headers = {
		'Content-Type': 'text/plain',
	}

	return new Response(body, { headers })
}
