<script lang="ts">
	import { signIn } from '@auth/sveltekit/client'
	import { AppBar } from '@skeletonlabs/skeleton-svelte'

	import { page } from '$app/state'

	import { Hamburger } from '$lib/components/SideBarMenu'
	import { TagPerks, TagAlien, TagClaim, TagInfo, TagProduct, TagTransfer, setTagContext } from '$lib/components/tag'
	import AppShell from '$lib/modules/AppShell'
	import { NotificationBell } from '$lib/modules/Notifications'
	import Search from '$lib/modules/Search'

	import Logo from '$lib/components/Logo.svelte'
	import { getAuth } from '$lib/services/auth.svelte'

	const auth = getAuth()

	const context = setTagContext({ tag: page.data?.tag })
</script>

<AppShell sticky="header sidebarLeft">
	<!-- Header -->
	{#snippet header()}
		<AppBar background="bg-surface-50 dark:bg-surface-900" shadow="shadow-xs">
			{#snippet lead()}
				<!-- Logo -->
				<a rel="prefetch" class="brand flex items-center" href="/">
					{#if page?.data?.organization?.logo || context.tag?.organization?.logo}
						<img src={page?.data?.organization?.logo || context.tag?.organization?.logo} alt="" class="h-[40px]" />
					{:else}
						<Logo class="mx-auto h-10 w-auto fill-[#15171f] dark:fill-white" type="logo" />
					{/if}
					<span class="ml-4 hidden text-xl font-extralight tracking-wider md:block"
						>{page?.data?.organization?.name || context.tag?.organization?.name || ''}</span
					>
				</a>
			{/snippet}

			{#snippet trail()}
				<Search />

				{#if auth.isAuthenticated}
					<NotificationBell />
				{/if}

				{#if !auth.isAuthenticated}
					<button class="btn hover:preset-tonal-primary" onclick={() => signIn('oidc')}>Log in</button>
				{/if}

				<Hamburger drawer="side-menu-backoffice" />
			{/snippet}
		</AppBar>
	{/snippet}

	{#if context.error}
		<aside class="alert preset-filled-error">
			<div>
				<iconify-icon
					icon="radix-icons:exclamation-triangle"
					width="48"
					height="48"
					class="!ml-0"
					aria-label="Registered Tag"
				></iconify-icon>
			</div>

			<div class="alert-message">
				<p class="capitalize">{context.error.message}</p>
			</div>
		</aside>
	{:else if context.tag}
		<TagInfo />

		<!-- <TagOrganization /> -->

		<TagProduct />

		{#if !context.tag.userId}
			<TagClaim />
		{:else if context.tag.userId === auth.profile?._id}
			<TagPerks />

			<TagTransfer />
		{:else}
			<TagAlien />
		{/if}
	{/if}

	<!-- Page Footer -->
	{#snippet footer()}
		<!-- <Footer /> -->
	{/snippet}
</AppShell>
