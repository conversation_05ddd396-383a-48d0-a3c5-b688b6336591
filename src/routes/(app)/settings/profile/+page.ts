import { error } from '@sveltejs/kit'

import { AsyncFullUserProfile } from '$lib/graphql/generated/gateway'

import type { PageLoad } from './$types'

export const load: PageLoad = async ({ parent }) => {
	const parentData = await parent()

	const { data, loading } = await AsyncFullUserProfile({
		variables: {
			filter: { id: parentData.session?.user_id },
		},
		context: {
			session: parentData.session,
		},
	})

	if (!data?.user) {
		throw error(404, 'User not found')
	}

	return {
		user: data.user,
		loading,
		meta: {
			title: `Profile · Settings · ${data.user.name}`,
		},
	}
}
