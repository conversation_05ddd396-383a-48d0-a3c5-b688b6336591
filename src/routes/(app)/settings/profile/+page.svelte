<script lang="ts">
	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import { DeleteUser, MeSendVerification, UpdateFullUser, type UpdateUserInput } from '$lib/graphql/generated/gateway'
	import { FieldType, FormBuilder, clearFormData, type Form } from '$lib/modules/Form'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'
	import { toaster } from '$lib/modules/Notifications'

	const modal = getModal()

	const localeOptions = [
		{ value: null, label: '' },
		{ value: 'en_US', label: 'English (United States)' },
		{ value: 'en_GB', label: 'English (United Kingdom)' },
		{ value: 'fr_FR', label: 'French (France)' },
		{ value: 'de_DE', label: 'German (Germany)' },
		{ value: 'it_IT', label: 'Italian (Italy)' },
		{ value: 'es_ES', label: 'Spanish (Spain)' },
		{ value: 'pt_BR', label: 'Portuguese (Brazil)' },
		{ value: 'pt_PT', label: 'Portuguese (Portugal)' },
		{ value: 'ru_RU', label: 'Russian (Russia)' },
		{ value: 'ja_JP', label: 'Japanese (Japan)' },
		{ value: 'ko_KR', label: 'Korean (South Korea)' },
		{ value: 'zh_CN', label: 'Chinese (Simplified, China)' },
		{ value: 'zh_TW', label: 'Chinese (Traditional, Taiwan)' },
		{ value: 'ar_SA', label: 'Arabic (Saudi Arabia)' },
		{ value: 'hi_IN', label: 'Hindi (India)' },
		{ value: 'th_TH', label: 'Thai (Thailand)' },
		{ value: 'vi_VN', label: 'Vietnamese (Vietnam)' },
		{ value: 'nl_NL', label: 'Dutch (Netherlands)' },
		{ value: 'sv_SE', label: 'Swedish (Sweden)' },
		{ value: 'pl_PL', label: 'Polish (Poland)' },
		{ value: 'tr_TR', label: 'Turkish (Turkey)' },
		{ value: 'da_DK', label: 'Danish (Denmark)' },
		{ value: 'fi_FI', label: 'Finnish (Finland)' },
		{ value: 'nb_NO', label: 'Norwegian Bokmål (Norway)' },
		{ value: 'el_GR', label: 'Greek (Greece)' },
		{ value: 'he_IL', label: 'Hebrew (Israel)' },
		{ value: 'id_ID', label: 'Indonesian (Indonesia)' },
		{ value: 'ms_MY', label: 'Malay (Malaysia)' },
		{ value: 'uk_UA', label: 'Ukrainian (Ukraine)' },
	]

	const timezoneOptions = [
		{ value: null, label: '' },
		{ value: 'Africa/Cairo', label: 'Africa/Cairo (EET)' },
		{ value: 'Africa/Johannesburg', label: 'Africa/Johannesburg (SAST)' },
		{ value: 'Africa/Lagos', label: 'Africa/Lagos (WAT)' },
		{ value: 'Africa/Nairobi', label: 'Africa/Nairobi (EAT)' },
		{ value: 'America/Anchorage', label: 'America/Anchorage (AKST/AKDT)' },
		{ value: 'America/Bogota', label: 'America/Bogota (COT)' },
		{ value: 'America/Buenos_Aires', label: 'America/Buenos Aires (ART)' },
		{ value: 'America/Chicago', label: 'America/Chicago (CST/CDT)' },
		{ value: 'America/Denver', label: 'America/Denver (MST/MDT)' },
		{ value: 'America/Los_Angeles', label: 'America/Los Angeles (PST/PDT)' },
		{ value: 'America/Mexico_City', label: 'America/Mexico City (CST/CDT)' },
		{ value: 'America/New_York', label: 'America/New York (EST/EDT)' },
		{ value: 'America/Phoenix', label: 'America/Phoenix (MST)' },
		{ value: 'America/Sao_Paulo', label: 'America/Sao Paulo (BRT/BRST)' },
		{ value: 'America/Toronto', label: 'America/Toronto (EST/EDT)' },
		{ value: 'America/Vancouver', label: 'America/Vancouver (PST/PDT)' },
		{ value: 'Asia/Bangkok', label: 'Asia/Bangkok (ICT)' },
		{ value: 'Asia/Dubai', label: 'Asia/Dubai (GST)' },
		{ value: 'Asia/Hong_Kong', label: 'Asia/Hong Kong (HKT)' },
		{ value: 'Asia/Jakarta', label: 'Asia/Jakarta (WIB)' },
		{ value: 'Asia/Karachi', label: 'Asia/Karachi (PKT)' },
		{ value: 'Asia/Kolkata', label: 'Asia/Kolkata (IST)' },
		{ value: 'Asia/Manila', label: 'Asia/Manila (PHT)' },
		{ value: 'Asia/Seoul', label: 'Asia/Seoul (KST)' },
		{ value: 'Asia/Shanghai', label: 'Asia/Shanghai (CST)' },
		{ value: 'Asia/Singapore', label: 'Asia/Singapore (SGT)' },
		{ value: 'Asia/Tokyo', label: 'Asia/Tokyo (JST)' },
		{ value: 'Australia/Adelaide', label: 'Australia/Adelaide (ACST/ACDT)' },
		{ value: 'Australia/Brisbane', label: 'Australia/Brisbane (AEST)' },
		{ value: 'Australia/Melbourne', label: 'Australia/Melbourne (AEST/AEDT)' },
		{ value: 'Australia/Perth', label: 'Australia/Perth (AWST)' },
		{ value: 'Australia/Sydney', label: 'Australia/Sydney (AEST/AEDT)' },
		{ value: 'Europe/Amsterdam', label: 'Europe/Amsterdam (CET/CEST)' },
		{ value: 'Europe/Berlin', label: 'Europe/Berlin (CET/CEST)' },
		{ value: 'Europe/Istanbul', label: 'Europe/Istanbul (TRT)' },
		{ value: 'Europe/London', label: 'Europe/London (GMT/BST)' },
		{ value: 'Europe/Madrid', label: 'Europe/Madrid (CET/CEST)' },
		{ value: 'Europe/Moscow', label: 'Europe/Moscow (MSK)' },
		{ value: 'Europe/Paris', label: 'Europe/Paris (CET/CEST)' },
		{ value: 'Europe/Rome', label: 'Europe/Rome (CET/CEST)' },
		{ value: 'Pacific/Auckland', label: 'Pacific/Auckland (NZST/NZDT)' },
		{ value: 'Pacific/Honolulu', label: 'Pacific/Honolulu (HST)' },
	]

	const personalDetailsFields: Form<UpdateUserInput> = [
		{
			title: 'Personal Details',
			description: 'Your personal details are used to identify you and customize your experience.',
			fields: [
				{
					key: 'name',
					type: FieldType.INPUT,
					props: {
						label: 'First Name',
						required: true,
						autocomplete: 'section-personal given-name',
					},
				},
				{
					key: 'familyName',
					type: FieldType.INPUT,
					props: {
						label: 'Last Name',
						required: true,
						autocomplete: 'section-personal family-name',
					},
				},
				{
					key: 'picture',
					type: FieldType.INPUT,
					props: {
						type: 'url',
						label: 'Picture URL',
						autocomplete: 'section-personal photo' as HTMLInputElement['autocomplete'],
						description: 'This picture will be used for your avatar.',
						addonRight: {
							img: 'key',
							rounded: true,
							shim: true,
						},
					},
				},
			],
		},
	]

	const contactsFields: Form<UpdateUserInput> = [
		{
			title: 'Contacts',
			description:
				'We will only use your contact details for the described purposes and will not send unsolicited communications.',
			fields: [
				{
					key: 'email',
					type: FieldType.INPUT,
					props: {
						type: 'email',
						label: 'Email',
						required: true,
						autocomplete: 'section-contact email',
						description: 'This email will be used as you account identity.',
						addonLeft: () => ({
							icon: page.data.user.emailVerified ? 'radix-icons:check' : 'radix-icons:cross-2',
							shim: true,
						}),
						addonRight: page.data.user.emailVerified
							? undefined
							: {
									text: 'Send Verification',
									shim: true,
									onclick: async () => {
										const result = await MeSendVerification({
											variables: {
												type: 'email',
											},
										})
										if (result.data?.meSendVerification) {
											toaster.success({
												description: 'Email verification sent successfully.',
												duration: 5000,
											})
										}
									},
								},
					},
				},
				{
					key: 'contactEmail',
					type: FieldType.INPUT,
					props: {
						type: 'email',
						label: 'Contact Email',
						autocomplete: 'section-contact email',
						description:
							'This email will be used for customer support and opt-in communication. If left blank, your primary email will be used.',
						addonLeft: () => ({
							icon: page.data.user.contactEmailVerified ? 'radix-icons:check' : 'radix-icons:cross-2',
							shim: true,
						}),
						addonRight: page.data.user.contactEmailVerified
							? undefined
							: {
									text: 'Send Verification',
									shim: true,
									onclick: async () => {
										const result = await MeSendVerification({
											variables: {
												type: 'contactEmail',
											},
										})
										if (result.data?.meSendVerification) {
											toaster.success({
												description: 'Contact email verification sent successfully.',
												duration: 5000,
											})
										}
									},
								},
					},
				},
				{
					key: 'phoneNumber',
					type: FieldType.INPUT,
					props: {
						type: 'tel',
						label: 'Phone Number',
						autocomplete: 'section-contact tel',
						description: 'This phone number will be used for customer support, account verification and recovery.',
						addonLeft: () => ({
							icon: page.data.user.phoneNumberVerified ? 'radix-icons:check' : 'radix-icons:cross-2',
							shim: true,
						}),
						addonRight: page.data.user.phoneNumberVerified
							? undefined
							: {
									text: 'Send Verification',
									shim: true,
									onclick: async () => {
										const result = await MeSendVerification({
											variables: {
												type: 'phoneNumber',
											},
										})
										if (result.data?.meSendVerification) {
											toaster.success({
												description: 'Phone verification sent successfully.',
												duration: 5000,
											})
										}
									},
								},
					},
				},
			],
		},
	]

	const preferencesFields: Form<UpdateUserInput> = [
		{
			title: 'Preferences',
			fields: [
				{
					key: 'locale',
					type: FieldType.SELECT,
					props: {
						label: 'Locale',
						options: localeOptions,
						autocomplete: 'section-preferences language' as HTMLSelectElement['autocomplete'],
						description:
							'The locale will be used to display the content and communicate in your preferred language, format dates and times. (overrides your browser settings)',
					},
				},
				{
					key: 'zoneinfo',
					type: FieldType.SELECT,
					props: {
						label: 'Timezone',
						options: timezoneOptions,
						description:
							'The timezone will be used to display dates and times in your preferred timezone. (overrides your browser settings)',
					},
				},
			],
		},
	]

	const value = clearFormData(page.data.user || {}, [...personalDetailsFields, ...contactsFields, ...preferencesFields])

	async function updateUser(data: UpdateUserInput) {
		await UpdateFullUser({
			variables: {
				partialEntity: {
					...data,
					_id: page.data.user._id,
				},
			},
		})
	}

	function confirmDeleteUser() {
		modal.open({
			type: 'confirm',
			title: 'Delete Account',
			submit: 'Delete Account',
			submitClasses: 'preset-filled-error-500',
			body: `<p>Are you sure you want to delete <strong class="text-error-500">${value.name ?? 'this account'}</strong> ?</p>
             <p class="mt-4 text-primary-500">This action is irreversible.</p>`,
			onSubmit: async (response: boolean) => {
				if (response) {
					await DeleteUser({
						variables: {
							filter: {
								where: {
									_id: page.data.user._id,
								},
							},
						},
					})
					await goto('/')
				}
			},
		})
	}
</script>

<div class="container mx-auto flex flex-col gap-8 px-6 pt-12 pb-20">
	<FormBuilder fields={personalDetailsFields} {value} submit="Update" onSubmit={updateUser} reset />

	<FormBuilder fields={contactsFields} {value} submit="Update" onSubmit={updateUser} reset />

	<FormBuilder fields={preferencesFields} {value} submit="Update" onSubmit={updateUser} reset />

	<div
		class="card bg-noise bg-surface-50-950 border-primary-100 dark:border-primary-800 text-primary-500 space-y-6 border p-8"
	>
		<h4 class="h4">Delete Account</h4>

		<p>This action is irreversible. Once you delete your account, there is no going back.</p>

		<button
			class="btn preset-outlined-primary-500 text-primary-500 hover:preset-filled-primary-500"
			onclick={confirmDeleteUser}>Delete account</button
		>
	</div>
</div>
