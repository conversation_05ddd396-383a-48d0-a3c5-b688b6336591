import { redirect, error } from '@sveltejs/kit'

import { AsyncCustomer } from '$lib/graphql/generated/gateway'

import type { PageLoad } from './$types'

export const load: PageLoad = async ({ parent, url }) => {
	const parentData = await parent()

	if (!parentData?.session) {
		redirect(303, '/login?postLoginRedirect=' + encodeURIComponent(url.toString().replace(url.origin, '')))
	}

	const { data, loading } = await AsyncCustomer({
		variables: {
			organizationId: parentData.session.user_id, // TODO needs to be adapted for users
		},
		context: {
			session: parentData.session,
		},
	})

	if (!data?.customer) {
		throw error(404, 'Customer not found')
	}

	return {
		customer: data.customer,
		loading,
		meta: {
			title: `Profile · Settings · ${data.customer.name}`,
		},
	}
}
