<script lang="ts">
	import { Tabs } from '@skeletonlabs/skeleton-svelte'

	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import { featureEnabled, getAuth } from '$lib/services/auth.svelte'

	import type { Snippet } from 'svelte'

	const {
		children,
	}: {
		children?: Snippet
	} = $props()

	const auth = getAuth()
</script>

<Tabs value={page.url.pathname} onValueChange={(e) => goto(e.value)} fluid classes="mt-2">
	{#snippet list()}
		<Tabs.Control value="/settings/profile">Profile</Tabs.Control>

		{#if featureEnabled(auth, 'userSetting.password')}
			<Tabs.Control value="/settings/security">Security</Tabs.Control>
		{/if}

		{#if featureEnabled(auth, 'userSetting.createOrganization') || auth.isInternalOrgUser}
			<Tabs.Control value="/settings/organizations">Organizations</Tabs.Control>
		{/if}
	{/snippet}

	{#snippet content()}
		{@render children?.()}
	{/snippet}
</Tabs>
