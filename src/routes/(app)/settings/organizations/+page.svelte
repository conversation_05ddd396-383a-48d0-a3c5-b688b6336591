<script lang="ts">
	import { createOrganizationForm } from '$lib/forms'
	import {
		FullUserProfile,
		CreateOrganization,
		type CreateOrganizationInput,
		type FullUserProfileQuery,
		DeleteRole,
		UserRole,
	} from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { openFormDrawer } from '$lib/modules/Form'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'

	import { featureEnabled, getAuth } from '$lib/services/auth.svelte'

	const auth = getAuth()
	const modal = getModal()

	type UserOrganizationResult = FullUserProfileQuery['user']['organizations'][0] & {
		roles: Array<{
			_id: string
			role: UserRole
			active: boolean
		}>
	}

	const fields = createOrganizationForm()

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	const config = $derived<DataTableConfig<UserOrganizationResult>>({
		columns: [
			{
				type: ColumnType.Profile,
				label: 'Organization',
				image: 'logo',
				name: 'name',
				sortable: 'name',
				href: (row) => `/backoffice/organizations/${row._id}`,
			},
			{
				type: ColumnType.ChipList,
				label: 'Roles',
				property: 'roles',
				labelProperty: 'role',
				onclick: (row, item) => {
					modal.open({
						type: 'confirm',
						title: 'Remove Role',
						submitClasses: 'preset-filled-error-500',
						body: `Are you sure you want to remove the <strong class="text-primary-400">${item.role}</strong> role from <strong class="text-primary-400">${row.name}</strong>?`,
						onSubmit: async (response: boolean) => {
							if (response) {
								await DeleteRole({
									variables: {
										filter: {
											where: {
												_id: item._id,
											},
										},
									},
									refetchQueries: ['FullUserProfile'],
								})
							}
						},
					})
				},
				color: (_row, item) => {
					switch (item?.role) {
						case UserRole.Owner:
						case UserRole.Admin:
							return 'error'
						default:
							return 'secondary'
					}
				},
			},
		],
		actions: [
			{
				action: (items) => {
					modal.open({
						type: 'confirm-list',
						title: 'Leave Organization',
						submitClasses: 'preset-filled-error-500',
						body: `Are you sure you want to leave ${items.length > 1 ? 'these organizations' : 'this organization'}? This will remove all your roles in the selected organization(s).`,
						meta: { items: items.map((item) => ({ label: item.name, value: item._id })), disabled: true },
						onSubmit: async (response: boolean) => {
							if (response) {
								const currentUser = $result.data?.user
								if (currentUser) {
									await DeleteRole({
										variables: {
											filter: {
												where: {
													userId: currentUser._id,
													organizationId: { $in: items.map((item) => item._id) },
												},
											},
										},
										refetchQueries: ['FullUserProfile'],
									})
								}
							}
						},
					})
				},
				icon: 'radix-icons:circle-backslash',
				classes: 'text-primary-500 hover:preset-filled-error-500',
				multiple: true,
			},
		],
	})

	const result = FullUserProfile({
		variables: {
			filter: {
				id: auth.profile?._id,
			},
		},
	})

	const data = $derived.by(() => {
		const user = $result.data?.user
		const allOrganizations = user?.organizations || []
		const userRoles = user?.roles || []

		let filteredOrganizations = allOrganizations.map((org) => ({
			...org,
			roles: userRoles
				.filter((role) => role.organizationId === org._id)
				.map((role) => ({
					_id: role._id,
					role: role.role,
					active: true,
				})),
		}))

		if (filter.search) {
			const searchLower = filter.search.toLowerCase()
			filteredOrganizations = filteredOrganizations.filter(
				(org) =>
					org._id.toLowerCase().includes(searchLower) ||
					org.name.toLowerCase().includes(searchLower) ||
					org.roles.some((role) => role.role.toLowerCase().includes(searchLower)),
			)
		}

		if (filter.sort) {
			filteredOrganizations = filteredOrganizations.sort((a, b) => {
				const key = filter!.sort!.key as keyof UserOrganizationResult
				const direction = filter!.sort!.direction

				if (a[key] < b[key]) {
					return direction === 'asc' ? -1 : 1
				} else if (a[key] > b[key]) {
					return direction === 'asc' ? 1 : -1
				} else {
					return 0
				}
			})
		}

		const start = filter.page * filter.limit
		const end = start + filter.limit
		const paginatedOrganizations = filteredOrganizations.slice(start, end)

		return {
			rows: paginatedOrganizations,
			total: filteredOrganizations.length,
		}
	})

	function create() {
		openFormDrawer({
			title: 'Create Organization',
			submitLabel: 'Create',
			fields,
			onSubmit: async (partialEntity: CreateOrganizationInput) => {
				await CreateOrganization({
					variables: {
						partialEntity,
					},
					refetchQueries: ['FullUserProfile'],
				})
			},
		})
	}
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}>
	{#snippet filterActions()}
		{#if featureEnabled(auth, 'userSetting.createOrganization')}
			<button class="preset-filled-secondary-500 btn btn-base w-fit" onclick={create}>Create New</button>
		{/if}
	{/snippet}
</DataTable>
