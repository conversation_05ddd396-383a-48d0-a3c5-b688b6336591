<script lang="ts">
	import { MeChangePassword, type MutationMeChangePasswordArgs } from '$lib/graphql/generated/gateway'
	import { FieldType, FormBuilder, type Form } from '$lib/modules/Form'
	import { toaster } from '$lib/modules/Notifications'

	type ChangePasswordForm = MutationMeChangePasswordArgs & {
		confirmPassword: string
	}

	const personalDetailsFields: Form<ChangePasswordForm> = [
		{
			title: 'Change password',
			fields: [
				{
					key: 'currentPassword',
					type: FieldType.INPUT,
					props: {
						type: 'password',
						label: 'Current Password',
						required: true,
						autocomplete: 'current-password',
						minLength: 6,
						maxLength: 50,
					},
				},
				{
					key: 'newPassword',
					type: FieldType.INPUT,
					props: {
						type: 'password',
						label: 'New Password',
						required: true,
						autocomplete: 'new-password',
						minLength: 8,
						maxLength: 50,
						description: 'Password must be at least 8 characters long.',
					},
				},
				{
					key: 'confirmPassword',
					type: FieldType.INPUT,
					props: {
						type: 'password',
						label: 'Confirm New Password',
						required: true,
						autocomplete: 'new-password',
						minLength: 8,
						maxLength: 50,
					},
				},
			],
		},
	]

	function validateForm(formData: ChangePasswordForm): { isValid: boolean; message: string } {
		if (formData.newPassword !== formData.confirmPassword) {
			return { isValid: false, message: 'New password and confirm password do not match.' }
		}
		return { isValid: true, message: '' }
	}

	function showToast(description: string, isSuccess: boolean) {
		toaster.create({
			description,
			type: isSuccess ? 'success' : 'error',
			duration: 5000,
		})
	}

	async function updatePassword(data: ChangePasswordForm) {
		const formData = data
		const validation = validateForm(formData)

		if (!validation.isValid) {
			showToast(validation.message, false)
			return
		}

		const result = await MeChangePassword({
			variables: {
				currentPassword: formData.currentPassword,
				newPassword: formData.newPassword,
			},
		})

		if (result.data?.meChangePassword) {
			showToast('Password changed successfully!', true)
		}
	}
</script>

<div class="container mx-auto flex flex-col gap-8 px-6 pt-12 pb-20">
	<FormBuilder fields={personalDetailsFields} submit="Update" onSubmit={updatePassword} />
</div>
