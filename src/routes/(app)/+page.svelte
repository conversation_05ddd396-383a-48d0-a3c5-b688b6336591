<script lang="ts">
	import { ProgressRing } from '@skeletonlabs/skeleton-svelte'

	import { page } from '$app/state'

	import { TagPerk } from '$lib/components/tag'
	import { Perks } from '$lib/graphql/generated/gateway'

	import { intersect } from '$lib/actions/intersect.svelte'

	import type { DataTableFilter } from '$lib/modules/DataTable'

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	const query = () =>
		Perks({
			variables: {
				search: {
					// ...(filter.search
					// 	? {
					// 			$or: [{ _Id: filter.search }],
					// 		}
					// 	: {}),
					...(page.data.organizationId ? { organizationId: page.data.organizationId } : {}),
				},
				options: {
					limit: filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				},
			},
			fetchPolicy: 'network-only',
			nextFetchPolicy: 'cache-first',
		})
	const result = query()

	$effect(() => {
		$result.query.refetch({
			search: {
				// ...(filter.search
				// 	? {
				// 			$or: [{ _Id: filter.search }],
				// 		}
				// 	: {}),
				...(page.data.organizationId ? { organizationId: page.data.organizationId } : {}),
			},
			options: {
				limit: filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
			},
		})
	})

	const data = $derived({
		rows: $result.data?.perks?.nodes || [],
		total: $result.data?.perks?.totalCount || 0,
	})

	function fetchMore() {
		$result.query.fetchMore({
			variables: {
				...$result.query.variables,
				options: {
					...$result.query.variables?.options,
					offset: $result.query.getCurrentResult().data.perks.nodes?.length,
				},
			},
		})
	}
</script>

<div class="container mx-auto sm:p-6 xl:px-0">
	<div class="grid gap-5 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3 xl:gap-8 2xl:grid-cols-4">
		{#each data.rows as perk, index (perk._id)}
			<!-- Infinity Scroll -->
			{#if data.rows.length - index === 1 && data.rows.length < $result.query.getCurrentResult().data.perks.totalCount}
				<TagPerk use={[[intersect, { callback: fetchMore, threshold: 1 }]]} data={perk}></TagPerk>
			{:else}
				<TagPerk data={perk}></TagPerk>
			{/if}
		{:else}
			{#if !$result.loading}
				<div class="text-center">
					<p class="text text-gray-500 py-6">You have no perks available at the moment.</p>
				</div>
			{/if}
		{/each}
	</div>

	{#if $result.loading}
		<ProgressRing
			value={null}
			classes="ml-2"
			size="size-5"
			meterStroke="stroke-secondary-500"
			trackStroke="stroke-secondary-500/30"
		/>
	{:else if data.total > data.rows.length}
		<button class="preset-filled btn mx-auto my-4 block" onclick={() => fetchMore()}>Load More</button>
	{/if}
</div>
