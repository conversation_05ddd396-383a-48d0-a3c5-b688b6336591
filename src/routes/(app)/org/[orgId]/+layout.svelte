<script lang="ts">
	import { Tabs } from '@skeletonlabs/skeleton-svelte'

	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import type { Snippet } from 'svelte'

	const { children }: { children?: Snippet } = $props()
</script>

<Tabs value={page.url.pathname} onValueChange={(e) => goto(e.value)} fluid classes="mt-2">
	{#snippet list()}
		<Tabs.Control value="/org/{page.params.id}/">Members</Tabs.Control>
		<Tabs.Control value="/org/{page.params.id}//applications">Applications</Tabs.Control>
		<Tabs.Control value="/org/{page.params.id}//billing">Billing</Tabs.Control>
		<Tabs.Control value="/org/{page.params.id}//template">Template</Tabs.Control>
		<Tabs.Control value="/org/{page.params.id}//settings">Settings</Tabs.Control>
	{/snippet}

	{#snippet content()}
		{@render children?.()}
	{/snippet}
</Tabs>
