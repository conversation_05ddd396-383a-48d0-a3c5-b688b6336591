import { redirect } from '@sveltejs/kit'

import { AsyncMe } from '$lib/graphql/generated/gateway'

import type { LayoutLoad } from './$types'

export const ssr = false

export const load: LayoutLoad = async ({ parent, url }) => {
	const parentData = await parent()

	if (!parentData?.session) {
		redirect(303, '/login?postLoginRedirect=' + encodeURIComponent(url.toString().replace(url.origin, '')))
	}

	try {
		const { data } = await AsyncMe({
			context: {
				session: parentData.session,
			},
		})

		return {
			profile: data?.me,
		}
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
	} catch (error: any) {
		if (error.networkError?.result?.errors?.[0]?.message === 'Invalid Authorization Token') {
			redirect(303, '/sign-out')
		}

		throw error
	}
}
