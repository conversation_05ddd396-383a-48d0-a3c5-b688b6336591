<script lang="ts">
	import { ProgressRing } from '@skeletonlabs/skeleton-svelte'

	import { page } from '$app/state'

	import { TagCard } from '$lib/components/tag'
	import { Tags } from '$lib/graphql/generated/gateway'

	import { intersect } from '$lib/actions/intersect.svelte'

	import type { DataTableFilter } from '$lib/modules/DataTable'

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	const query = () =>
		Tags({
			variables: {
				filter: {
					where: {
						userId: page.data.session?.user_id,
						...(page.data.organizationId ? { organizationId: page.data.organizationId } : {}),
					},
					limit: filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				},
			},
			fetchPolicy: 'network-only',
			nextFetchPolicy: 'cache-first',
		})
	const result = query()

	$effect(() => {
		$result.query.refetch({
			filter: {
				where: {
					userId: page.data.session?.user_id,
					...(page.data.organizationId ? { organizationId: page.data.organizationId } : {}),
				},
				limit: filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
			},
		})
	})

	const data = $derived({
		rows: $result.data?.tags?.nodes || [],
		total: $result.data?.tags?.totalCount || 0,
	})

	function fetchMore() {
		$result.query.fetchMore({
			variables: {
				filter: {
					...$result.query.variables?.filter,
					offset: $result.query.getCurrentResult().data.tags.nodes?.length,
				},
			},
			updateQuery: (prev, { fetchMoreResult }) => {
				if (!fetchMoreResult) return prev
				return {
					tags: {
						...fetchMoreResult.tags,
						nodes: [...prev.tags.nodes, ...fetchMoreResult.tags.nodes],
					},
				}
			},
		})
	}
</script>

<div class="container mx-auto p-5 sm:p-6 xl:px-0">
	<div class="grid gap-5 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3 xl:gap-8 2xl:grid-cols-4">
		{#each data.rows as tag, index (tag._id)}
			{#if data.rows.length - index === 1 && data.rows.length < $result.query.getCurrentResult().data.tags.totalCount}
				<TagCard use={[[intersect, { callback: fetchMore, threshold: 1 }]]} data={tag} />
			{:else}
				<TagCard data={tag} />
			{/if}
		{:else}
			{#if !$result.loading}
				<div class="text-center col-span-full">
					<p class="text text-gray-500">No products available at the moment.</p>
				</div>
			{/if}
		{/each}
	</div>

	{#if $result.loading}
		<ProgressRing
			value={null}
			classes="ml-2"
			size="size-5"
			meterStroke="stroke-secondary-500"
			trackStroke="stroke-secondary-500/30"
		/>
	{:else if data.total > data.rows.length}
		<button class="preset-filled btn mx-auto my-4 block" onclick={() => fetchMore()}>Load More</button>
	{/if}
</div>
