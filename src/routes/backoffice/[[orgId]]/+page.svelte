<script lang="ts">
	import { page } from '$app/state'

	import {
		AsyncActivationStatsNewPerDay,
		AsyncActivationStatsNewPerMonth,
		AsyncActivationStatsNewPerWeek,
		AsyncActivationStatsPerDay,
		AsyncActivationStatsPerMonth,
		AsyncActivationStatsPerWeek,
		AsyncActivationStatsTotal,
		AsyncOrganizationStatsNewPerDay,
		AsyncOrganizationStatsNewPerMonth,
		AsyncOrganizationStatsNewPerWeek,
		AsyncOrganizationStatsPerDay,
		AsyncOrganizationStatsPerMonth,
		AsyncOrganizationStatsPerWeek,
		AsyncOrganizationStatsTotal,
		AsyncPeopleStatsNewPerDay,
		AsyncPeopleStatsNewPerMonth,
		AsyncPeopleStatsNewPerWeek,
		AsyncPeopleStatsPerDay,
		AsyncPeopleStatsPerMonth,
		AsyncPeopleStatsPerWeek,
		AsyncPeopleStatsTotal,
		AsyncProductStatsNewPerDay,
		AsyncProductStatsNewPerMonth,
		AsyncProductStatsNewPerWeek,
		AsyncProductStatsPerDay,
		AsyncProductStatsPerMonth,
		AsyncProductStatsPerWeek,
		AsyncProductStatsTotal,
		AsyncProgramStatsNewPerDay,
		AsyncProgramStatsNewPerMonth,
		AsyncProgramStatsNewPerWeek,
		AsyncProgramStatsPerDay,
		AsyncProgramStatsPerMonth,
		AsyncProgramStatsPerWeek,
		AsyncProgramStatsTotal,
		AsyncTagStatsNewPerDay,
		AsyncTagStatsNewPerMonth,
		AsyncTagStatsNewPerWeek,
		AsyncTagStatsPerDay,
		AsyncTagStatsPerMonth,
		AsyncTagStatsPerWeek,
		AsyncTagStatsTotal,
		AsyncUserStatsNewPerDay,
		AsyncUserStatsNewPerMonth,
		AsyncUserStatsNewPerWeek,
		AsyncUserStatsPerDay,
		AsyncUserStatsPerMonth,
		AsyncUserStatsPerWeek,
		AsyncUserStatsTotal,
		type StatsInput,
	} from '$lib/graphql/generated/gateway'
	import Dashboard, {
		CardType,
		ChartType,
		type CardNumberData,
		type DashboardCard,
		type DashboardOptions,
		type NumberData,
		type ScopeOptions,
	} from '$lib/modules/Dashboard'
	import { scopeResolution, scopeToPreviousScopeQuery, scopeToScopeQuery } from '$lib/utils/entity-stats'

	import { getAuth } from '$lib/services/auth.svelte'

	const auth = getAuth()

	const config: DashboardOptions = {
		scope: {
			default: 'currentMonth',
		},
		showToolbox: false,
		showScopeSelect: true,
	}

	let scope = $state<ScopeOptions>(config.scope?.default ?? 'currentMonth')

	const statsUsersQueryMap = (query: StatsInput) => ({
		day: AsyncUserStatsPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.userStats.perDay),
		week: AsyncUserStatsPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.userStats.perMonth),
		month: AsyncUserStatsPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.userStats.perWeek),
	})
	const statsUsersNewQueryMap = (query: StatsInput) => ({
		day: AsyncUserStatsNewPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.userStats.newPerDay),
		week: AsyncUserStatsNewPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.userStats.newPerMonth),
		month: AsyncUserStatsNewPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.userStats.newPerWeek),
	})

	const statsOrganizationsQueryMap = (query: StatsInput) => ({
		day: AsyncOrganizationStatsPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.perDay),
		week: AsyncOrganizationStatsPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.perMonth),
		month: AsyncOrganizationStatsPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.perWeek),
	})
	const statsOrganizationsNewQueryMap = (query: StatsInput) => ({
		day: AsyncOrganizationStatsNewPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.newPerDay),
		week: AsyncOrganizationStatsNewPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.newPerMonth),
		month: AsyncOrganizationStatsNewPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.newPerWeek),
	})

	const statsPeopleQueryMap = (query: StatsInput) => ({
		day: AsyncPeopleStatsPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.perDay),
		week: AsyncPeopleStatsPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.perMonth),
		month: AsyncPeopleStatsPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.perWeek),
	})
	const statsPeopleNewQueryMap = (query: StatsInput) => ({
		day: AsyncPeopleStatsNewPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.newPerDay),
		week: AsyncPeopleStatsNewPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.newPerMonth),
		month: AsyncPeopleStatsNewPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.newPerWeek),
	})

	const statsTagsQueryMap = (query: StatsInput) => ({
		day: AsyncTagStatsPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.perDay),
		week: AsyncTagStatsPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.perMonth),
		month: AsyncTagStatsPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.perWeek),
	})
	const statsTagsNewQueryMap = (query: StatsInput) => ({
		day: AsyncTagStatsNewPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.newPerDay),
		week: AsyncTagStatsNewPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.newPerMonth),
		month: AsyncTagStatsNewPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.newPerWeek),
	})

	const statsProgramsQueryMap = (query: StatsInput) => ({
		day: AsyncProgramStatsPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.programStats.perDay),
		week: AsyncProgramStatsPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.programStats.perMonth),
		month: AsyncProgramStatsPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.programStats.perWeek),
	})
	const statsProgramsNewQueryMap = (query: StatsInput) => ({
		day: AsyncProgramStatsNewPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.programStats.newPerDay),
		week: AsyncProgramStatsNewPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.programStats.newPerMonth),
		month: AsyncProgramStatsNewPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.programStats.newPerWeek),
	})

	const statsProductsQueryMap = (query: StatsInput) => ({
		day: AsyncProductStatsPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.productStats.perDay),
		week: AsyncProductStatsPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.productStats.perMonth),
		month: AsyncProductStatsPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.productStats.perWeek),
	})
	const statsProductsNewQueryMap = (query: StatsInput) => ({
		day: AsyncProductStatsNewPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.productStats.newPerDay),
		week: AsyncProductStatsNewPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.productStats.newPerMonth),
		month: AsyncProductStatsNewPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.productStats.newPerWeek),
	})

	const statsActivationsQueryMap = (query: StatsInput) => ({
		day: AsyncActivationStatsPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.activationStats.perDay),
		week: AsyncActivationStatsPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.activationStats.perMonth),
		month: AsyncActivationStatsPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.activationStats.perWeek),
	})

	const statsActivationsNewQueryMap = (query: StatsInput) => ({
		day: AsyncActivationStatsNewPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.activationStats.newPerDay),
		week: AsyncActivationStatsNewPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.activationStats.newPerMonth),
		month: AsyncActivationStatsNewPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.activationStats.newPerWeek),
	})

	const totalUsers = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: statsUsersQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then(async (result) => {
				const currentSum = result?.y?.reduce((acc, val) => acc + val, 0) || 0

				const total = await AsyncUserStatsTotal({
					variables: {
						...((page.params.orgId ?? page.data.organizationId)
							? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
							: {}),
					},
				}).then(({ data }) => data?.userStats.total)

				isLoading = false

				return {
					value: total,
					variation: (currentSum / (total || 1)) * 100,
				}
			}),
		}
	})

	const newUser = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsUsersNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
				statsUsersNewQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)],
				statsUsersQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
			]).then(async ([currentScope, previousScope, totalUsers]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0
				const previousSum = previousScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false

				return {
					value: currentSum,
					variation: (currentSum - previousSum) / previousSum,
					// objective: 4,
					chart: {
						xAxis: {
							type: 'category',
							data: totalUsers?.x || [],
						},
						series: [
							{
								type: ChartType.LINE,
								name: 'Total',
								data: totalUsers?.y || [],
							},
						],
					},
				} satisfies NumberData
			}),
		}
	})

	const totalOrganizations = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: statsOrganizationsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then(async (result) => {
				const currentSum = result?.y?.reduce((acc, val) => acc + val, 0) || 0

				const total = await AsyncOrganizationStatsTotal({
					variables: {
						...((page.params.orgId ?? page.data.organizationId)
							? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
							: {}),
					},
				}).then(({ data }) => data?.organizationStats.total)

				isLoading = false

				return {
					value: total,
					variation: (currentSum / (total || 1)) * 100,
				}
			}),
		}
	})

	const newOrganizations = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsOrganizationsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
				statsOrganizationsNewQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)],
				statsOrganizationsQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
			]).then(async ([currentScope, previousScope, totalOrganizations]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0
				const previousSum = previousScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false

				return {
					value: currentSum,
					variation: (currentSum - previousSum) / previousSum,
					// objective: 4,
					chart: {
						xAxis: {
							type: 'category',
							data: totalOrganizations?.x || [],
						},
						series: [
							{
								type: ChartType.LINE,
								name: 'Total',
								data: totalOrganizations?.y || [],
							},
						],
					},
				} satisfies NumberData
			}),
		}
	})

	const totalPeople = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: statsPeopleNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then(async (result) => {
				const currentSum = result?.y?.reduce((acc, val) => acc + val, 0) || 0

				const total = await AsyncPeopleStatsTotal({
					variables: {
						...((page.params.orgId ?? page.data.organizationId)
							? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
							: {}),
					},
				}).then(({ data }) => data?.peopleStats.total)

				isLoading = false

				return {
					value: total,
					variation: (currentSum / (total || 1)) * 100,
				}
			}),
		}
	})

	const newPeople = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsPeopleNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
				statsPeopleNewQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)],
				statsPeopleQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
			]).then(async ([currentScope, previousScope, totalPeople]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0
				const previousSum = previousScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false

				return {
					value: currentSum,
					variation: (currentSum - previousSum) / previousSum,
					// objective: 4,
					chart: {
						xAxis: {
							type: 'category',
							data: totalPeople?.x || [],
						},
						series: [
							{
								type: ChartType.LINE,
								name: 'Total',
								data: totalPeople?.y || [],
							},
						],
					},
				} satisfies NumberData
			}),
		}
	})

	const totalPrograms = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: statsProgramsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then(async (result) => {
				const currentSum = result?.y?.reduce((acc, val) => acc + val, 0) || 0

				const total = await AsyncProgramStatsTotal({
					variables: {
						...((page.params.orgId ?? page.data.organizationId)
							? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
							: {}),
					},
				}).then(({ data }) => data?.programStats.total)

				isLoading = false

				return {
					value: total,
					variation: (currentSum / (total || 1)) * 100,
				}
			}),
		}
	})

	const newPrograms = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsProgramsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
				statsProgramsNewQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)],
				statsProgramsQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
			]).then(async ([currentScope, previousScope, totalPrograms]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0
				const previousSum = previousScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false

				return {
					value: currentSum,
					variation: (currentSum - previousSum) / previousSum,
					// objective: 4,
					chart: {
						xAxis: {
							type: 'category',
							data: totalPrograms?.x || [],
						},
						series: [
							{
								type: ChartType.LINE,
								name: 'Total',
								data: totalPrograms?.y || [],
							},
						],
					},
				} satisfies NumberData
			}),
		}
	})

	const totalProducts = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: statsProductsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then(async (result) => {
				const currentSum = result?.y?.reduce((acc, val) => acc + val, 0) || 0

				const total = await AsyncProductStatsTotal({
					variables: {
						...((page.params.orgId ?? page.data.organizationId)
							? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
							: {}),
					},
				}).then(({ data }) => data?.productStats.total)

				isLoading = false

				return {
					value: total,
					variation: (currentSum / (total || 1)) * 100,
				}
			}),
		}
	})

	const newProducts = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsProductsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
				statsProductsNewQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)],
				statsProductsQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
			]).then(async ([currentScope, previousScope, totalProducts]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0
				const previousSum = previousScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false

				return {
					value: currentSum,
					variation: (currentSum - previousSum) / previousSum,
					// objective: 4,
					chart: {
						xAxis: {
							type: 'category',
							data: totalProducts?.x || [],
						},
						series: [
							{
								type: ChartType.LINE,
								name: 'Total',
								data: totalProducts?.y || [],
							},
						],
					},
				} satisfies NumberData
			}),
		}
	})

	const totalTags = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: statsTagsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then(async (result) => {
				const currentSum = result?.y?.reduce((acc, val) => acc + val, 0) || 0

				const total = await AsyncTagStatsTotal({
					variables: {
						...((page.params.orgId ?? page.data.organizationId)
							? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
							: {}),
					},
				}).then(({ data }) => data?.tagStats.total)

				isLoading = false

				return {
					value: total,
					variation: (currentSum / (total || 1)) * 100,
				}
			}),
		}
	})

	const newTags = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsTagsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
				statsTagsNewQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)],
				statsTagsQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
			]).then(async ([currentScope, previousScope, totalTags]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0
				const previousSum = previousScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false

				return {
					value: currentSum,
					variation: (currentSum - previousSum) / previousSum,
					// objective: 4,
					chart: {
						xAxis: {
							type: 'category',
							data: totalTags?.x || [],
						},
						series: [
							{
								type: ChartType.LINE,
								name: 'Total',
								data: totalTags?.y || [],
							},
						],
					},
				} satisfies NumberData
			}),
		}
	})

	const totalActivations = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: statsActivationsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then(async (result) => {
				const currentSum = result?.y?.reduce((acc, val) => acc + val, 0) || 0

				const total = await AsyncActivationStatsTotal({
					variables: {
						...((page.params.orgId ?? page.data.organizationId)
							? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
							: {}),
					},
				}).then(({ data }) => data?.activationStats.total)

				isLoading = false

				return {
					value: total,
					variation: (currentSum / (total || 1)) * 100,
				}
			}),
		}
	})

	const newActivations = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsActivationsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
				statsActivationsNewQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)],
				statsActivationsQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
			]).then(async ([currentScope, previousScope, totalActivations]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0
				const previousSum = previousScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false

				return {
					value: currentSum,
					variation: (currentSum - previousSum) / previousSum,
					// objective: 4,
					chart: {
						xAxis: {
							type: 'category',
							data: totalActivations?.x || [],
						},
						series: [
							{
								type: ChartType.LINE,
								name: 'Total',
								data: totalActivations?.y || [],
							},
						],
					},
				} satisfies NumberData
			}),
		}
	})

	const cards = $derived<DashboardCard[]>([
		{
			type: CardType.NUMBER,
			header: {
				title: 'Total Users',
			},
			options: {},
			class: 'col-50 row-1',
			getter: totalUsers,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'Total Organizations',
			},
			options: {},
			class: 'col-50 row-1',
			getter: totalOrganizations,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'Total People',
			},
			options: {},
			class: 'col-50 row-1',
			getter: totalPeople,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'Total Programs',
			},
			options: {},
			class: 'col-50 row-1',
			getter: totalPrograms,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'Total Products',
			},
			options: {},
			class: 'col-50 row-1',
			getter: totalProducts,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'Total Tags',
			},
			options: {},
			class: 'col-50 row-1',
			getter: totalTags,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'Total Activations',
			},
			options: {},
			class: 'col-50 row-1',
			getter: totalActivations,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.BREAK,
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'New Users',
			},
			class: 'col-50 row-1',
			getter: newUser,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'New Organizations',
			},
			class: 'col-50 row-1',
			getter: newOrganizations,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'New People',
			},
			class: 'col-50 row-1',
			getter: newPeople,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'New Programs',
			},
			class: 'col-50 row-1',
			getter: newPrograms,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'New Products',
			},
			class: 'col-50 row-1',
			getter: newProducts,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'New Tags',
			},
			class: 'col-50 row-1',
			getter: newTags,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'New Activations',
			},
			class: 'col-50 row-1',
			getter: newActivations,
			hide: () => !auth.isInternalUser || !!(page.params.orgId ?? page.data.organizationId),
		},
	])
</script>

{#key page.params.orgId ?? page.data.organizationId}
	<Dashboard bind:scope {cards} {config} />
{/key}
