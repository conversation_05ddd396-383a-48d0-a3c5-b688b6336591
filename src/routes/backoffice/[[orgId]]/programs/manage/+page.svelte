<script lang="ts">
	import { MediaQuery } from 'svelte/reactivity'

	import { page } from '$app/state'

	import { createProgramForm } from '$lib/forms'
	import { Programs, CreateProgram, UpdateProgram, type ProgramsQuery } from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { clearFormData, openFormDrawer } from '$lib/modules/Form'

	import type { QueryReturnType } from '$lib/utils'

	const matches = new MediaQuery('max-width: 1024px')

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	type ProgramsResult = QueryReturnType<ProgramsQuery['programs']>

	const config = $derived<DataTableConfig<ProgramsResult>>({
		columns: [
			{
				type: ColumnType.String,
				label: 'Name',
				property: 'name',
				sortable: 'name',
				href: (row) => `/backoffice/programs/${row._id}`,
			},
			{
				type: ColumnType.Profile,
				label: 'Organization',
				image: 'organization.logo',
				name: 'organization.name',
				href: (row) => row.organizationId && `/backoffice/organizations/${row.organizationId}`,
				hide: !!(page.params.orgId ?? page.data.organizationId),
			},
			{
				type: ColumnType.Date,
				label: 'Updated',
				property: 'updatedAt',
				sortable: 'updatedAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Created',
				property: 'createdAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				sortable: 'createdAt',
				hide: matches.current,
			},
		],
		actions: [
			{
				action: (items) => {
					window.open(`/program/${items[0]._id}`)
				},
				icon: 'radix-icons:external-link',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
			},
			{
				action: (items) => {
					const fields = createProgramForm(items[0].organizationId)

					openFormDrawer({
						title: 'Update Program',
						submitLabel: 'Update',
						fields,
						data: clearFormData(items[0], fields),
						onSubmit: async (partialEntity) => {
							await UpdateProgram({
								variables: {
									partialEntity: {
										...partialEntity,
										_id: items[0]._id,
									},
								},
								refetchQueries: ['Programs'],
							})
						},
					})
				},
				icon: 'radix-icons:pencil-1',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
			},
		],
	})

	const query = () =>
		Programs({
			variables: {
				filter: {
					limit: filter.limit,
					offset: filter.page * filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
					where: {
						...(filter.search
							? {
									$or: [
										{ _Id: filter.search },
										{ name: { $regex: filter.search, $options: 'smix' } },
										{ organizationId: filter.search },
									],
								}
							: {}),
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
					},
				},
			},
		})
	const result = query()

	$effect(() => {
		$result.query.refetch({
			filter: {
				limit: filter.limit,
				offset: filter.page * filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				where: {
					...(filter.search
						? {
								$or: [
									{ _Id: filter.search },
									{ name: { $regex: filter.search, $options: 'smix' } },
									{ organizationId: filter.search },
								],
							}
						: {}),
					...((page.params.orgId ?? page.data.organizationId)
						? { organizationId: page.params.orgId ?? page.data.organizationId }
						: {}),
				},
			},
		})
	})

	const data = $derived({
		rows: $result.data?.programs?.nodes || [],
		total: $result.data?.programs?.totalCount || 0,
	})

	function create() {
		const fields = createProgramForm(page?.params.orgId || page.data.organizationId)

		openFormDrawer({
			title: 'Create Program',
			submitLabel: 'Create',
			fields,
			data: {
				organizationId: page.params.orgId ?? page.data.organizationId,
			},
			onSubmit: async (partialEntity) => {
				await CreateProgram({
					variables: {
						partialEntity,
					},
					refetchQueries: ['Programs'],
				})
			},
		})
	}
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}>
	{#snippet filterActions()}
		<button class="btn preset-filled-secondary-500 w-fit" onclick={create}>Create New</button>
	{/snippet}
</DataTable>
