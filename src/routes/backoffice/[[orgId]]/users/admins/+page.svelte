<script lang="ts">
	import {
		<PERSON><PERSON>,
		DeleteRole,
		UserRole,
		AsyncInviteUsersSearch,
		CreateRoles,
		AdminRoles,
	} from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { FieldType, openFormDrawer, type Form } from '$lib/modules/Form'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'
	import ToggleGroup from '$lib/modules/ToggleGroup'

	const modal = getModal()

	const roles = [AdminRoles.SystemAdmin, AdminRoles.Support, AdminRoles.Developer]
	const roleOptions = [
		{ value: AdminRoles.SystemAdmin, label: 'SystemAdmin' },
		{ value: AdminRoles.Support, label: 'Support' },
		{ value: AdminRoles.Developer, label: 'Developer' },
	]

	let selectedRoles = $state<string[]>([])
	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	interface Admins {
		_id: string
		picture?: string | null
		name?: string | null
		roles: Array<{
			_id: string
			role: AdminRoles
			active: boolean
		}>
	}

	const config = $derived<DataTableConfig<Admins>>({
		columns: [
			{
				type: ColumnType.Image,
				label: 'Picture',
				property: 'picture',
				sortable: '_id',
				href: (row) => `/backoffice/users/${row._id}`,
			},
			{
				type: ColumnType.String,
				label: 'Name',
				property: 'name',
				sortable: 'name',
				href: (row) => `/backoffice/users/${row._id}`,
			},
			{
				type: ColumnType.ChipList,
				label: 'Roles',
				property: 'roles',
				labelProperty: 'role',
				onclick: (row, item) => {
					if (item.active) {
						modal.open({
							type: 'confirm',
							title: 'Remove Role',
							submitClasses: 'preset-filled-error-500',
							body: `Are you sure you want to remove the <strong class="text-primary-400">${item.role}</strong> role from <strong class="text-primary-400">${row.name}</strong>?`,
							onSubmit: async (response: boolean) => {
								if (response) {
									await DeleteRole({
										variables: {
											filter: {
												where: {
													role: item.role,
													userId: row._id,
												},
											},
										},
										refetchQueries: ['Admins'],
									})
								}
							},
						})
					} else {
						modal.open({
							type: 'confirm',
							title: 'Add Role',
							submitClasses: 'preset-filled-secondary-500',
							body: `Are you sure you want to add the <strong class="text-success-500">${item.role}</strong> role to <strong class="text-success-500">${row.name}</strong>?`,
							onSubmit: async (response: boolean) => {
								if (response) {
									await CreateRoles({
										variables: {
											partialEntities: [
												{
													role: item.role as unknown as UserRole,
													userId: row._id,
												},
											],
										},
										refetchQueries: ['Admins'],
									})
								}
							},
						})
					}
				},
				color: (_row, item) => {
					if (item?.active) {
						switch (item.role) {
							case AdminRoles.SystemAdmin:
								return 'error'
							default:
								return 'secondary'
						}
					}

					return 'tertiary'
				},
			},
		],
	})

	const query = () =>
		Admins({
			variables: {
				roles: selectedRoles as AdminRoles[],
				filter: {
					limit: filter.limit,
					offset: filter.page * filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
					where: filter.search
						? {
								$or: [
									{ name: { $regex: filter.search, $options: 'smix' } },
									{ givenName: { $regex: filter.search, $options: 'smix' } },
									{ familyName: { $regex: filter.search, $options: 'smix' } },
									{ middleName: { $regex: filter.search, $options: 'smix' } },
									{ nickname: { $regex: filter.search, $options: 'smix' } },
									{ email: { $regex: filter.search, $options: 'smix' } },
									{ contactEmail: { $regex: filter.search, $options: 'smix' } },
									{ phoneNumber: { $regex: filter.search, $options: 'smix' } },
									{ 'address.streetAddress': { $regex: filter.search, $options: 'smix' } },
									{ 'address.locality': { $regex: filter.search, $options: 'smix' } },
									{ 'address.postalCode': { $regex: filter.search, $options: 'smix' } },
									{ 'address.region': { $regex: filter.search, $options: 'smix' } },
									{ 'address.country': { $regex: filter.search, $options: 'smix' } },
									{ locale: { $regex: filter.search, $options: 'smix' } },
									{ zoneinfo: { $regex: filter.search, $options: 'smix' } },
								],
							}
						: {},
				},
			},
		})

	const result = query()

	$effect(() => {
		$result.query.refetch({
			roles: selectedRoles as AdminRoles[],
			filter: {
				limit: filter.limit,
				offset: filter.page * filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				where: filter.search
					? {
							$or: [
								{ name: { $regex: filter.search, $options: 'smix' } },
								{ givenName: { $regex: filter.search, $options: 'smix' } },
								{ familyName: { $regex: filter.search, $options: 'smix' } },
								{ middleName: { $regex: filter.search, $options: 'smix' } },
								{ nickname: { $regex: filter.search, $options: 'smix' } },
								{ email: { $regex: filter.search, $options: 'smix' } },
								{ contactEmail: { $regex: filter.search, $options: 'smix' } },
								{ phoneNumber: { $regex: filter.search, $options: 'smix' } },
								{ 'address.streetAddress': { $regex: filter.search, $options: 'smix' } },
								{ 'address.locality': { $regex: filter.search, $options: 'smix' } },
								{ 'address.postalCode': { $regex: filter.search, $options: 'smix' } },
								{ 'address.region': { $regex: filter.search, $options: 'smix' } },
								{ 'address.country': { $regex: filter.search, $options: 'smix' } },
								{ locale: { $regex: filter.search, $options: 'smix' } },
								{ zoneinfo: { $regex: filter.search, $options: 'smix' } },
							],
						}
					: {},
			},
		})
	})

	const data = $derived({
		rows: ($result.data?.admins?.nodes || []).map((admin) => ({
			_id: admin._id,
			name: admin.name,
			picture: admin.picture,
			roles: roles.map((role) => {
				return {
					_id: admin._id,
					role,
					active: admin.roles?.some((r) => r === role),
				}
			}),
		})),
		total: $result.data?.admins?.totalCount || 0,
	})

	interface CreateRoles {
		userId: string
		roles: UserRole[]
	}

	const fields: Form<CreateRoles> = [
		{
			key: 'userId',
			type: FieldType.AUTOCOMPLETE,
			props: {
				label: 'User',
				placeholder: 'Search by ID, name or email address',
				required: true,
				options: async (search) =>
					AsyncInviteUsersSearch({
						variables: {
							filter: {
								where: {
									$or: [
										{ _id: search },
										{ name: { $regex: search, $options: 'smix' } },
										{ email: { $regex: search, $options: 'smix' } },
									],
									// rolesFilter: { where: { role: { $in: roles } } }
								},
								limit: 50,
							},
						},
						fetchPolicy: 'network-only',
					}).then((result) => {
						return result.data.users?.nodes?.length
							? result.data.users?.nodes.map((user) => ({
									label: `${user.name}${user.email ? ` - ${user.email}` : ''}`,
									value: user._id,
									image: user.picture || undefined,
									meta: {
										_id: user._id,
										email: user.email || undefined,
										img: user.picture || undefined,
										name: user.name || undefined,
										// isAdmin: !!user.roles.length
									},
								}))
							: [
									// {
									// 	isAdmin: true
									// }
								]
					}),
				debounce: 250,
			},
		},
		{
			key: 'roles',
			type: FieldType.SELECT,
			props: {
				label: 'Roles',
				required: true,
				multiple: true,
				options: [
					{ value: UserRole.SystemAdmin, label: 'SystemAdmin' },
					{ value: UserRole.Support, label: 'Support' },
					{ value: UserRole.Developer, label: 'Developer' },
				],
				description: 'You can assign multiple roles to a user by holding SHIFT while selecting.',
			},
		},
	]

	function create() {
		openFormDrawer({
			title: 'Add Admin',
			submitLabel: 'Add Admin',
			fields,
			onSubmit: async (input) => {
				const roles = input.roles.map((role) => ({
					role,
					userId: input.userId,
				}))

				await CreateRoles({
					variables: {
						partialEntities: roles,
					},
					refetchQueries: ['Admins'],
				})
			},
		})
	}
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}>
	{#snippet filterActions()}
		<button class="preset-filled-secondary-500 btn btn-base w-fit" onclick={create}>Add Admin</button>
	{/snippet}

	{#snippet filterExtra()}
		<ToggleGroup value={selectedRoles} onValueChange={(e) => (selectedRoles = e.value)} multiple>
			{#each roleOptions as option (option.label)}
				<ToggleGroup.Item value={option.value}>
					{option.label}
				</ToggleGroup.Item>
			{/each}
		</ToggleGroup>
	{/snippet}
</DataTable>
