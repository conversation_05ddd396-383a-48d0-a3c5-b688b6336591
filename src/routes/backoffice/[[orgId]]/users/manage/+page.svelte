<script lang="ts">
	import { MediaQuery } from 'svelte/reactivity'

	import { editUserForm } from '$lib/forms'
	import { UsersTable, UpdateUser, type UsersTableQuery } from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { clearFormData, openFormDrawer } from '$lib/modules/Form'

	import type { QueryReturnType } from '$lib/utils'

	const matches = new MediaQuery('max-width: 1024px')

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	type UsersResult = QueryReturnType<UsersTableQuery['users']>

	const config = $derived<DataTableConfig<UsersResult>>({
		columns: [
			{
				type: ColumnType.Image,
				label: 'Picture',
				property: 'picture',
				sortable: '_id',
				href: (row) => `/backoffice/users/${row._id}`,
			},
			{
				type: ColumnType.String,
				label: 'Name',
				property: 'name',
				sortable: 'name',
				href: (row) => `/backoffice/users/${row._id}`,
			},
			{
				type: ColumnType.ChipList,
				label: 'Verifications',
				chips: [
					{
						label: 'Email',
						property: 'emailVerified',
					},
					{
						label: 'Contact',
						property: 'contactEmailVerified',
					},
					{
						label: 'Recovery',
						property: 'recoveryEmailVerified',
					},
					{
						label: 'Phone',
						property: 'phoneNumberVerified',
					},
				],
				color: {
					true: 'success',
					false: 'error',
					undefined: 'tertiary',
				},
				hide: matches.current,
			},
			{
				type: ColumnType.String,
				label: 'Locale',
				property: 'locale',
				sortable: 'locale',
				hide: matches.current,
			},
			{
				type: ColumnType.String,
				label: 'Zone Info',
				property: 'zoneinfo',
				sortable: 'zoneinfo',
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Updated',
				property: 'updatedAt',
				sortable: 'updatedAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Joined',
				property: 'createdAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				sortable: 'createdAt',
			},
		],
		actions: [
			{
				action: (items) => {
					const fields = editUserForm()

					openFormDrawer({
						title: 'Update User',
						submitLabel: 'Update',
						fields,
						data: clearFormData(items[0], fields),
						onSubmit: async (partialEntity) => {
							await UpdateUser({
								variables: {
									partialEntity: {
										...partialEntity,
										_id: items[0]?._id,
									},
								},
								refetchQueries: ['UsersTable'],
							})
						},
					})
				},
				url: (item) => item?._id,
				icon: 'radix-icons:pencil-1',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
			},
		],
	})

	const query = () =>
		UsersTable({
			variables: {
				filter: {
					limit: filter.limit,
					offset: filter.page * filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
					where: filter.search
						? {
								$or: [
									{ _id: filter.search },
									{ name: { $regex: filter.search, $options: 'smix' } },
									{ givenName: { $regex: filter.search, $options: 'smix' } },
									{ familyName: { $regex: filter.search, $options: 'smix' } },
									{ middleName: { $regex: filter.search, $options: 'smix' } },
									{ nickname: { $regex: filter.search, $options: 'smix' } },
									{ email: { $regex: filter.search, $options: 'smix' } },
									{ contactEmail: { $regex: filter.search, $options: 'smix' } },
									{ phoneNumber: { $regex: filter.search, $options: 'smix' } },
									{ 'address.streetAddress': { $regex: filter.search, $options: 'smix' } },
									{ 'address.locality': { $regex: filter.search, $options: 'smix' } },
									{ 'address.postalCode': { $regex: filter.search, $options: 'smix' } },
									{ 'address.region': { $regex: filter.search, $options: 'smix' } },
									{ 'address.country': { $regex: filter.search, $options: 'smix' } },
									{ locale: { $regex: filter.search, $options: 'smix' } },
									{ zoneinfo: { $regex: filter.search, $options: 'smix' } },
								],
							}
						: {},
				},
			},
		})

	const result = query()

	$effect(() => {
		$result.query.refetch({
			filter: {
				limit: filter.limit,
				offset: filter.page * filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				where: filter.search
					? {
							$or: [
								{ _id: filter.search },
								{ name: { $regex: filter.search, $options: 'smix' } },
								{ givenName: { $regex: filter.search, $options: 'smix' } },
								{ familyName: { $regex: filter.search, $options: 'smix' } },
								{ middleName: { $regex: filter.search, $options: 'smix' } },
								{ nickname: { $regex: filter.search, $options: 'smix' } },
								{ email: { $regex: filter.search, $options: 'smix' } },
								{ contactEmail: { $regex: filter.search, $options: 'smix' } },
								{ phoneNumber: { $regex: filter.search, $options: 'smix' } },
								{ 'address.streetAddress': { $regex: filter.search, $options: 'smix' } },
								{ 'address.locality': { $regex: filter.search, $options: 'smix' } },
								{ 'address.postalCode': { $regex: filter.search, $options: 'smix' } },
								{ 'address.region': { $regex: filter.search, $options: 'smix' } },
								{ 'address.country': { $regex: filter.search, $options: 'smix' } },
								{ locale: { $regex: filter.search, $options: 'smix' } },
								{ zoneinfo: { $regex: filter.search, $options: 'smix' } },
							],
						}
					: {},
			},
		})
	})

	const data = $derived({
		rows: $result.data?.users?.nodes ?? [],
		total: $result.data?.users?.totalCount ?? 0,
	})
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}></DataTable>
