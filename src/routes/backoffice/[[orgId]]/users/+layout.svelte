<script lang="ts">
	import { Tabs } from '@skeletonlabs/skeleton-svelte'

	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import type { Snippet } from 'svelte'

	const { children }: { children?: Snippet } = $props()
</script>

<Tabs value={page.url.pathname} onValueChange={(e) => goto(e.value)} fluid classes="mt-2">
	{#snippet list()}
		<Tabs.Control value="/backoffice/users/dashboard">Dashboard</Tabs.Control>
		<Tabs.Control value="/backoffice/users">Manage</Tabs.Control>
		<Tabs.Control value="/backoffice/users/invitations">Invitations</Tabs.Control>
		<Tabs.Control value="/backoffice/users/admins">Admins</Tabs.Control>
	{/snippet}

	{#snippet content()}
		{@render children?.()}
	{/snippet}
</Tabs>
