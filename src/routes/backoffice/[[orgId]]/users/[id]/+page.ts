import { error, redirect } from '@sveltejs/kit'

import { AsyncFullUserProfile } from '$lib/graphql/generated/gateway'

import type { PageLoad } from './$types'

export const load: PageLoad = async ({ params, parent, url }) => {
	const parentData = await parent()

	if (!parentData?.session) {
		redirect(303, '/unauthorized?postLoginRedirect=' + encodeURIComponent(url.toString().replace(url.origin, '')))
	}

	const { data, loading } = await AsyncFullUserProfile({
		variables: {
			filter: { id: params.id },
		},
		context: {
			session: parentData.session,
		},
	})

	if (!data?.user) {
		throw error(404, 'User not found')
	}

	return {
		user: data.user,
		loading,
		meta: {
			title: `Backoffice · User · ${data.user.name}`,
		},
	}
}
