<script lang="ts">
	import { Accordion, Avatar } from '@skeletonlabs/skeleton-svelte'

	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import { editUserForm } from '$lib/forms'
	import { UpdateFullUser, type FullUserProfileQuery, type UpdateUserInput } from '$lib/graphql/generated/gateway'
	import { clearFormData, openFormDrawer } from '$lib/modules/Form'
	import { getLocaleForDateFormat } from '$lib/utils'

	import { clipboard } from '$lib/actions/clipboard.svelte'

	let user: FullUserProfileQuery['user'] = $state(page.data.user)

	function gotoManagePage() {
		goto('/backoffice/users/manage')
	}

	function openEditDrawer() {
		const fields = editUserForm()

		openFormDrawer({
			title: 'Update User',
			submitLabel: 'Update',
			fields,
			data: clearFormData(user, fields),
			onSubmit: async (partialEntity: UpdateUserInput) => {
				const result = await UpdateFullUser({
					variables: {
						partialEntity: {
							...partialEntity,
							_id: user._id,
						},
					},
				})

				if (result.data) {
					user = result.data.updateUser
				}
			},
		})
	}

	let main = $state<string[]>([])
	let footerLeft = $state<string[]>([])
	let footerRight = $state<string[]>([])
</script>

<div class="container p-6">
	<div class="flex items-center justify-between">
		<button type="button" class="btn preset-tonal" onclick={gotoManagePage}>
			<iconify-icon icon="radix-icons:chevron-left" inline></iconify-icon>
			<span>Manage</span>
		</button>

		<button type="button" class="btn preset-filled-secondary-500" onclick={openEditDrawer}
			><iconify-icon icon="radix-icons:pencil-1" inline></iconify-icon><span>Edit</span></button
		>
	</div>

	<br />

	<div class="flex items-center space-x-4">
		<h1 class="h1">{user.name}</h1>

		{#if user}
			<Avatar src={user.picture || undefined} name={sessionStorage.profile?.name || ''} />
		{/if}
	</div>

	<hr class="mt-4" />

	<p class="mb-4 flex items-center">
		Entity ID: {user._id}

		<button use:clipboard={user._id} class="btn-icon" aria-label="Copy Entity ID">
			<iconify-icon icon="radix-icons:copy" inline></iconify-icon>
		</button>
	</p>

	<div class="card preset-outlined-surface-500 p-4">
		<Accordion value={main} onValueChange={(e) => (main = e.value)} multiple collapsible>
			<Accordion.Item value="details">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:info-circled" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<span class="font-bold">Details</span>
				{/snippet}

				{#snippet panel()}
					<div class="table-wrap rounded-xl">
						<table class="table border-separate border-spacing-[1px]">
							<tbody>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Joined</td>
									<td class="preset-tonal"
										>{user.createdAt
											? new Date(user.createdAt).toLocaleString(getLocaleForDateFormat(), {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Updated</td>
									<td class="preset-tonal"
										>{user.updatedAt
											? new Date(user.updatedAt).toLocaleString(getLocaleForDateFormat(), {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Locale</td>
									<td class="preset-tonal">{user.locale || ''}</td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Zone Info</td>
									<td class="preset-tonal">{user.zoneinfo || ''}</td>
								</tr>
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>

			<Accordion.Item value="contacts">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:id-card" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<span class="font-bold">Contacts</span>
				{/snippet}

				{#snippet panel()}
					<div class="table-wrap rounded-xl">
						<table class="table border-separate border-spacing-[1px]">
							<tbody>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Phone</td>
									<td class="preset-tonal"><span use:clipboard={user.phoneNumber}>{user.phoneNumber || ''}</span></td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Email</td>
									<td class="preset-tonal"><span use:clipboard={user.email}>{user.email || ''}</span></td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Contact Email</td>
									<td class="preset-tonal"><span use:clipboard={user.contactEmail}>{user.contactEmail || ''}</span></td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Recovery Email</td>
									<td class="preset-tonal"
										><span use:clipboard={user.recoveryEmail}>{user.recoveryEmail || ''}</span></td
									>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Additional Email</td>
									<td class="preset-tonal"
										><span use:clipboard={user.additionalEmail}>{user.additionalEmail || ''}</span></td
									>
								</tr>
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>

			<Accordion.Item value="address">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:home" inline></iconify-icon>
				{/snippet}
				{#snippet control()}
					<span class="font-bold">Address</span>
				{/snippet}
				{#snippet panel()}
					<div class="table-wrap rounded-xl">
						<table class="table border-separate border-spacing-[1px]">
							<tbody>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium"></td>
									<td class="preset-tonal">
										{#if user.address}
											<address class="not-italic">
												<span>{user.address.streetAddress || ''}</span><br />
												<span>{user.address.region || ''}</span><br />
												<span>{user.address.postalCode || ''}</span><br />
												<span>{user.address.country || ''}</span>
											</address>
										{:else}
											<span>No address available</span>
										{/if}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>

			<Accordion.Item value="identities">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:person" inline></iconify-icon>
				{/snippet}
				{#snippet control()}
					<span class="font-bold">Identities</span>
				{/snippet}
				{#snippet panel()}
					<div class="table-wrap rounded-xl">
						<table class="table border-separate border-spacing-[1px]">
							<tbody>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">TODO</td>
									<td class="preset-tonal">TODO</td>
								</tr>
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>

			<Accordion.Item value="organizations">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:backpack" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<span class="font-bold">Organizations</span>
				{/snippet}

				{#snippet panel()}
					<div class="table-wrap rounded-xl">
						<table class="table border-separate border-spacing-[1px]">
							<tbody>
								{#if user.organizations && user.organizations.length > 0}
									{#each user.organizations as organization (organization._id)}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">
												<a href={`/backoffice/organizations/${organization._id}`}>{organization.name || ''}</a>
											</td>
											<td class="preset-tonal">
												{organization.role || ''}
											</td>
										</tr>
									{/each}
								{:else}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium"></td>
										<td class="preset-tonal">
											<span>No organizations available</span>
										</td>
									</tr>
								{/if}
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>
		</Accordion>
	</div>

	<div class="mt-8 columns-1 gap-4 md:columns-2">
		<Accordion
			classes="card p-2 preset-tonal break-inside-avoid-column"
			value={footerLeft}
			onValueChange={(e) => (footerLeft = e.value)}
			multiple
			collapsible
		>
			<Accordion.Item value="notes">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:file-text" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<div class="flex w-full items-center justify-between">
						<span class="font-bold">Notes</span>
					</div>
				{/snippet}

				{#snippet panel()}
					<p class="text-sm">TODO</p>
				{/snippet}
			</Accordion.Item>
		</Accordion>

		<Accordion
			classes="card p-2 preset-tonal break-inside-avoid-column"
			value={footerRight}
			onValueChange={(e) => (footerRight = e.value)}
			multiple
			collapsible
		>
			<Accordion.Item value="activity-stream">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:activity-log" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<div class="flex w-full items-center justify-between">
						<span class="font-bold">Activity Stream</span>
					</div>
				{/snippet}

				{#snippet panel()}
					<p class="text-sm">TODO</p>
				{/snippet}
			</Accordion.Item>
		</Accordion>
	</div>
</div>
