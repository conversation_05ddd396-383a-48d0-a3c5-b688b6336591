<script lang="ts">
	import {
		AsyncUserStatsAverageNewPerWeekDay,
		AsyncUserStatsMaxNewPerWeekDay,
		AsyncUserStatsMinNewPerWeekDay,
		AsyncUserStatsNewPerDay,
		AsyncUserStatsNewPerMonth,
		AsyncUserStatsNewPerWeek,
		AsyncUserStatsPerDay,
		AsyncUserStatsPerMonth,
		AsyncUserStatsPerWeek,
		AsyncUserStatsTotal,
		type StatsInput,
	} from '$lib/graphql/generated/gateway'
	import Dashboard, {
		CardType,
		ChartType,
		type CardNumberData,
		type ChartCardData,
		type DashboardCard,
		type DashboardOptions,
		type ScopeOptions,
	} from '$lib/modules/Dashboard'
	import {
		fillMissingData,
		scopeResolution,
		scopeToPreviousScopeQuery,
		scopeToScopeQuery,
		translateWeekDays,
	} from '$lib/utils/entity-stats'

	const config: DashboardOptions = {
		scope: {
			default: 'currentMonth',
		},
		showToolbox: false,
		showScopeSelect: true,
	}

	let scope = $state<ScopeOptions>(config.scope?.default ?? 'currentMonth')

	const total = AsyncUserStatsTotal({}).then(({ data }) => data?.userStats.total)

	const statsQueryMap = (query: StatsInput) => ({
		day: AsyncUserStatsPerDay({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.userStats.perDay),
		week: AsyncUserStatsPerMonth({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.userStats.perMonth),
		month: AsyncUserStatsPerWeek({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.userStats.perWeek),
	})
	const statsNewQueryMap = (query: StatsInput) => ({
		day: AsyncUserStatsNewPerDay({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.userStats.newPerDay),
		week: AsyncUserStatsNewPerMonth({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.userStats.newPerMonth),
		month: AsyncUserStatsNewPerWeek({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.userStats.newPerWeek),
	})

	const perWeekDayQueryMap = (query: StatsInput) => ({
		day: AsyncUserStatsAverageNewPerWeekDay({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.userStats.averageNewPerWeekDay),
		week: AsyncUserStatsMinNewPerWeekDay({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.userStats.minNewPerWeekDay),
		month: AsyncUserStatsMaxNewPerWeekDay({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.userStats.maxNewPerWeekDay),
	})

	const totalGetter = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([statsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)], total]).then(
				async ([currentScope, total]) => {
					const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0

					isLoading = false
					return {
						value: total,
						variation: (currentSum / (total || 1)) * 100,
					}
				},
			),
		}
	})

	const newGetter = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
				statsNewQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)],
				statsQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
			]).then(async ([currentScope, previousScope, totalUsers]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0
				const previousSum = previousScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false
				return {
					value: currentSum,
					variation: (currentSum - previousSum) / previousSum,
					// objective: 4,
					chart: {
						xAxis: {
							type: 'category',
							data: totalUsers?.x || [],
						},
						series: [
							{
								type: ChartType.LINE,
								name: 'Total',
								data: totalUsers?.y || [],
							},
						],
					},
				}
			}),
		}
	})

	const perDay = $derived.by<ChartCardData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then((result) =>
					fillMissingData(result, scope, scopeToScopeQuery(scope)),
				),
				statsQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)].then((result) =>
					fillMissingData(result, scope, scopeToScopeQuery(scope)),
				),
			]).then(async ([newUsers, totalUsers]) => {
				isLoading = false

				return {
					xAxis: {
						type: 'category',
						data: newUsers.x || [],
					},
					series: [
						{
							type: ChartType.BAR,
							name: 'New',
							data: newUsers.y || [],
						},
						{
							type: ChartType.LINE,
							name: 'Total',
							data: totalUsers.y || [],
						},
					],
				}
			}),
		}
	})

	const newPerDay = $derived.by<ChartCardData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: perWeekDayQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then(async (result) => {
				if (!result?.x?.length) {
					isLoading = false
					return {}
				}

				const data = fillMissingData(result, 'weekDays')

				isLoading = false

				return {
					xAxis: {
						type: 'category',
						name: 'Users',
						data: data?.x ? translateWeekDays(data.x) : [],
					},
					series: [
						{
							type: ChartType.BAR,
							name: 'Users',
							data: data?.y || [],
						},
					],
				}
			}),
		}
	})

	const cards = $derived<DashboardCard[]>([
		{
			type: CardType.NUMBER,
			header: {
				title: 'Total Users',
			},
			options: {},
			class: 'col-50 row-1',
			getter: totalGetter,
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'New Users',
			},
			class: 'col-50 row-1',
			getter: newGetter,
		},
		{
			type: CardType.CHART,
			chartType: ChartType.LINE,
			options: {},
			header: {
				title: 'Users per Day',
			},
			class: 'col-100 row-3',
			getter: perDay,
		},
		{
			type: CardType.CHART,
			chartType: ChartType.BAR,
			options: {},
			header: {
				title: 'New Users per week day',
			},
			class: 'col-100 row-3',
			getter: newPerDay,
		},
	])
</script>

<Dashboard bind:scope {cards} {config} />
