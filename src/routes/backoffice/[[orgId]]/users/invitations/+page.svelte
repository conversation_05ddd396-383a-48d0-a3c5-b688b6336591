<script lang="ts">
	import { Segment } from '@skeletonlabs/skeleton-svelte'
	import { MediaQuery } from 'svelte/reactivity'

	import { page } from '$app/state'

	import { selectOrganization } from '$lib/forms'
	import {
		Invitations,
		DeleteInvitation,
		type CreateInvitationInput,
		type InvitationsQuery,
		UserRole,
		AsyncInviteUsersSearch,
		UpdateInvitation,
		CreateInvitation,
	} from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { openFormDrawer } from '$lib/modules/Form'
	import { applyChanges, clearFormData, FieldType, type Form } from '$lib/modules/Form/form.interface'
	import { getModal } from '$lib/modules/Modal/state.svelte.js'
	import ToggleGroup from '$lib/modules/ToggleGroup'
	import { validateEmail, type QueryReturnType } from '$lib/utils'

	const matches = new MediaQuery('max-width: 1024px')
	const modal = getModal()

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	let used = $state<string | null>('false')
	let type = $state<string[]>([])

	type InvitationResult = QueryReturnType<InvitationsQuery['invitations']>

	const config = $derived<DataTableConfig<InvitationResult>>({
		columns: [
			{
				type: ColumnType.String,
				label: 'Code',
				property: 'code',
				sortable: 'code',
			},
			{
				type: ColumnType.String,
				label: 'Type',
				property: 'type',
				sortable: 'type',
			},
			// {
			//   label: 'Used',
			//   property: 'used',
			//   type: 'boolean',
			//   sortable: 'used',
			//   hide: matches.current
			// },
			{
				type: ColumnType.String,
				label: 'Limit',
				property: 'limit',
				sortable: 'limit',
				hide: matches.current,
			},
			{
				type: ColumnType.String,
				label: 'Used Count',
				property: 'usedCount',
				sortable: 'usedCount',
				fallback: 0,
				hide: matches.current,
			},
			{
				type: ColumnType.String,
				label: 'Roles',
				property: 'roles',
				sortable: 'roles',
				hide: matches.current,
			},
			// {
			//   label: 'Email or Phone',
			//   type: 'emailOrPhone',
			//   hide: matches.current
			// },
			{
				type: ColumnType.Profile,
				label: 'Organization',
				image: 'organization.logo',
				name: 'organization.name',
				href: (item) => item.organizationId && `/backoffice/organizations/${item.organizationId}`,
				hide: matches.current,
			},
			// {
			// 	type: ColumnType.ExpandedDetail,
			// 	label: 'Users',
			// 	type: 'expandedDetail',
			// },
			{
				type: ColumnType.Profile,
				label: 'Created By',
				image: 'createdBy.picture',
				name: 'createdBy.name',
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Updated',
				property: 'updatedAt',
				sortable: 'updatedAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Created',
				property: 'createdAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				sortable: 'createdAt',
				hide: true,
			},
		],
		actions: [
			{
				action: (items) => {
					openFormDrawer({
						title: 'Update Invitation',
						submitLabel: 'Update',
						fields: applyChanges(fields, [
							{
								key: 'organizationId',
								change: 'readOnly',
							},
						]),
						data: clearFormData(items[0], fields),
						onSubmit: async (partialEntity) => {
							await UpdateInvitation({
								variables: {
									partialEntity: {
										...partialEntity,
										_id: items[0]?._id,
									},
									upsert: false,
								},
								refetchQueries: ['Invitations'],
							})
						},
					})
				},
				icon: 'radix-icons:pencil-1',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
			},
			{
				action: (items) => {
					modal.open({
						type: 'confirm-list',
						title: 'Delete Invitation',
						submitClasses: 'preset-filled-error-500',
						body: `Are you sure you want to delete the selected ${items.length > 1 ? 'invitations' : 'invitation'}?`,
						meta: { items: items.map((item) => ({ label: item.code, value: item._id })), disabled: true },
						onSubmit: async (response: boolean) => {
							if (response) {
								await DeleteInvitation({
									variables: {
										filter: {
											where: {
												_id: {
													$in: items.map((item) => item?._id),
												},
											},
										},
									},
									refetchQueries: ['Invitations'],
								})
							}
						},
					})
				},
				icon: 'radix-icons:circle-backslash',
				classes: 'text-primary-500 hover:preset-filled-error-500',
				multiple: true,
			},
		],
	})

	const query = () =>
		Invitations({
			variables: {
				filter: {
					limit: filter.limit,
					offset: filter.page * filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
					where: {
						...(filter.search
							? {
									$or: [
										{ _id: filter.search },
										{ organizationId: filter.search },
										{ role: { $regex: filter.search, $options: 'smix' } },
										{ code: { $regex: filter.search, $options: 'smix' } },
										{ emails: { $regex: filter.search, $options: 'smix' } },
										{ phones: { $regex: filter.search, $options: 'smix' } },
										{ userIds: filter.search },
										{ consumerIds: filter.search },
										{ createdById: filter.search },
										{ limit: { $regex: filter.search, $options: 'smix' } },
										{ usedCount: { $regex: filter.search, $options: 'smix' } },
									],
								}
							: {}),
						...(used === 'true' ? { used: true } : { used: { $nin: [true] } }),
						...(type?.length ? { type: { $in: type } } : {}),
					},
				},
			},
		})

	const result = query()

	$effect(() => {
		$result.query.refetch({
			filter: {
				limit: filter.limit,
				offset: filter.page * filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				where: {
					...(filter.search
						? {
								$or: [
									{ _id: filter.search },
									{ organizationId: filter.search },
									{ role: { $regex: filter.search, $options: 'smix' } },
									{ code: { $regex: filter.search, $options: 'smix' } },
									{ emails: { $regex: filter.search, $options: 'smix' } },
									{ phones: { $regex: filter.search, $options: 'smix' } },
									{ userIds: filter.search },
									{ consumerIds: filter.search },
									{ createdById: filter.search },
									{ limit: { $regex: filter.search, $options: 'smix' } },
									{ usedCount: { $regex: filter.search, $options: 'smix' } },
								],
							}
						: {}),
					...(used === 'true' ? { used: true } : { used: { $nin: [true] } }),
					...(type?.length ? { type: { $in: type } } : {}),
				},
			},
		})
	})

	const data = $derived({
		rows: $result.data?.invitations?.nodes || [],
		total: $result.data?.invitations?.totalCount || 0,
	})

	const fields = $derived<Form<CreateInvitationInput>>([
		{
			...selectOrganization,
			hide: !!(page.params.orgId ?? page.data.organizationId),
		},
		{
			tabs: [
				{
					title: 'Generic',
					fields: [
						{
							key: 'limit',
							type: FieldType.INPUT,
							props: {
								label: 'Limit',
								type: 'number',
								required: true,
								min: '1',
							},
						},
					],
				},
				{
					title: 'Users',
					fields: [
						{
							key: 'userIds',
							type: FieldType.AUTOCOMPLETE,
							props: {
								label: 'User',
								placeholder: 'Search by ID, name or email address',
								required: true,
								options: async (search, formData) => {
									if (!formData.organizationId) {
										return []
									}

									return AsyncInviteUsersSearch({
										variables: {
											filter: {
												where: {
													...(search
														? {
																$or: [
																	{ _id: search },
																	{ name: { $regex: search, $options: 'smix' } },
																	{ email: { $regex: search, $options: 'smix' } },
																],
															}
														: {}),
													// rolesFilter: { where: { organizationId: formData.organizationId } }
												},
												limit: 50,
											},
										},
										fetchPolicy: 'network-only',
									}).then((result) => {
										return result.data.users?.nodes?.length
											? result.data.users?.nodes.map((user) => ({
													label: `${user.name}${user.email ? ` - ${user.email}` : ''}`,
													value: user._id,
													image: user.picture || undefined,
													meta: {
														_id: user._id,
														email: user.email || undefined,
														img: user.picture || undefined,
														name: user.name || undefined,
														// isAdmin: !!user.roles.length
													},
												}))
											: validateEmail(search)
												? [
														{
															label: search,
															value: search,
															meta: {
																email: search,
															},
														},
													]
												: []
									})
								},
								debounce: 250,
							},
							dependent: ['organizationId'],
						},
					],
				},
			],
		},
		{
			key: 'roles',
			type: FieldType.SELECT,
			props: {
				label: 'Roles',
				multiple: true,
				options: [
					{ value: UserRole.Owner, label: 'Owner' },
					{ value: UserRole.Admin, label: 'Admin' },
					{ value: UserRole.User, label: 'User' },
				],
				description: 'You can assign multiple roles to a user by holding SHIFT while selecting.',
			},
		},
	])

	function create() {
		openFormDrawer({
			title: 'Invite Members',
			submitLabel: 'Invite',
			fields,
			data: {
				organizationId: page.params.orgId ?? page.data.organizationId,
				limit: 1,
				roles: [],
			},
			onSubmit: async (partialEntity) => {
				await CreateInvitation({
					variables: {
						partialEntity: {
							...partialEntity,
							...(!validateEmail(partialEntity.userIds as unknown as string)
								? { userIds: [partialEntity.userIds] }
								: { userIds: undefined }),
							...(validateEmail(partialEntity.userIds as unknown as string) && { emails: [partialEntity.userIds] }),
							// ...(validatePhone(partialEntity.userIds) && { phones: [partialEntity.userIds] }),
						},
					},
					refetchQueries: ['Invitations'],
				})
			},
		})
	}
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}>
	{#snippet filterActions()}
		<button class="preset-filled-secondary-500 btn btn-base w-fit" onclick={create}>Create New</button>
	{/snippet}

	{#snippet filterExtra()}
		<ToggleGroup value={type} onValueChange={(e) => (type = e.value)} multiple>
			<ToggleGroup.Item value="INVITATION_UNIQUE">Unique</ToggleGroup.Item>
			<ToggleGroup.Item value="INVITATION_MULTIPLE">Multiple</ToggleGroup.Item>
		</ToggleGroup>

		<Segment value={used} onValueChange={(e) => (used = e.value)}>
			<Segment.Item value="false">Unused</Segment.Item>
			<Segment.Item value="true">Used</Segment.Item>
		</Segment>
	{/snippet}
</DataTable>
