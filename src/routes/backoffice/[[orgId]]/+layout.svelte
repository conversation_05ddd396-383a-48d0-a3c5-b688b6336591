<script lang="ts">
	import { signIn } from '@auth/sveltekit/client'
	import { AppBar } from '@skeletonlabs/skeleton-svelte'

	import { page } from '$app/state'

	import { Hamburger } from '$lib/components/SideBarMenu'
	import AppShell from '$lib/modules/AppShell'
	import { NotificationBell } from '$lib/modules/Notifications'
	import Search from '$lib/modules/Search'

	import BackofficeSelectOrganization from '$lib/components/Backoffice/BackofficeSelectOrganization.svelte'
	import Logo from '$lib/components/Logo.svelte'
	import SideBarSubMenu from '$lib/components/SideBarMenu/SubMenu.svelte'
	import { getApp } from '$lib/services/app.svelte'
	import { getAuth } from '$lib/services/auth.svelte'

	import type { Snippet } from 'svelte'

	const { children }: { children?: Snippet } = $props()

	const app = getApp()
	const auth = getAuth()
</script>

<AppShell sticky="header sidebarLeft">
	<!-- Header -->
	{#snippet header()}
		<AppBar background="bg-surface-50 dark:bg-surface-900" shadow="shadow-xs">
			{#snippet lead()}
				<!-- Logo -->
				<a rel="prefetch" class="brand flex items-center" href={app.homePath}>
					{#if page?.data?.organization?.logo}
						<img src={page?.data?.organization?.logo} alt="" class="h-[40px]" />
					{:else}
						<Logo class="mx-auto h-10 w-auto fill-[#15171f] dark:fill-white" type="logo" />
					{/if}
					<span class="ml-4 hidden text-xl font-extralight tracking-wider md:block"
						>{page?.data?.organization?.name || ''}</span
					>
				</a>
			{/snippet}

			{#snippet trail()}
				<Search />

				{#if auth.isAuthenticated}
					<NotificationBell />
				{/if}

				{#if !auth.isAuthenticated}
					<button class="btn hover:preset-tonal-primary" onclick={() => signIn('oidc')}>Log in</button>
				{/if}

				<Hamburger drawer="side-menu-backoffice" />
			{/snippet}
		</AppBar>
	{/snippet}

	{#snippet sidebarLeft()}
		<SideBarSubMenu class="flex grow overflow-hidden">
			{#snippet header()}
				<BackofficeSelectOrganization />
			{/snippet}
		</SideBarSubMenu>
	{/snippet}

	{@render children?.()}
</AppShell>
