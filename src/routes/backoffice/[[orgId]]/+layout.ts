import { redirect, error } from '@sveltejs/kit'

import { AsyncMe, AsyncFullOrganization } from '$lib/graphql/generated/gateway'

import { internalUsersRoles } from '$lib/services/auth.svelte'

import type { LayoutLoad } from './$types'

export const ssr = false

export const load: LayoutLoad = async ({ parent, params, url, depends }) => {
	depends('full:organization')
	const parentData = await parent()

	if (!parentData?.session) {
		redirect(303, '/unauthorized?postLoginRedirect=' + encodeURIComponent(url.toString().replace(url.origin, '')))
	}

	if (parentData.session.error === 'RefreshAccessTokenError') {
		redirect(303, '/signin?postLoginRedirect=' + encodeURIComponent(url.toString().replace(url.origin, '')))
	}

	try {
		const { data } = await AsyncMe({
			context: {
				session: parentData.session,
			},
		})

		if (
			!params.orgId &&
			!data?.me?.roles?.some((role) => internalUsersRoles.includes(role.role) && role.organizationId === null)
		) {
			if (data.me.organizations[0]?._id) {
				redirect(303, `/backoffice/${data.me.organizations[0]._id}`)
			} else {
				redirect(303, '/unauthorized')
			}
		}

		if (params.orgId) {
			const result = await AsyncFullOrganization({
				variables: {
					filter: { id: params.orgId },
				},
				context: {
					session: parentData.session,
				},
			})

			if (!result.data?.organization) {
				throw error(404, 'Organization not found')
			}

			return {
				profile: data?.me,
				organization: result.data.organization,
				loading: result.loading,
				meta: {
					title: `Organization · ${result.data.organization.name}`,
				},
			}
		}

		return {
			profile: data?.me,
		}
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
	} catch (error: any) {
		if (error.networkError?.result?.errors?.[0]?.message === 'Invalid Authorization Token') {
			redirect(303, '/sign-out')
		}

		throw error
	}
}
