<script lang="ts">
	import { page } from '$app/state'

	import {
		AsyncPeopleStatsAverageNewPerWeekDay,
		AsyncPeopleStatsMaxNewPerWeekDay,
		AsyncPeopleStatsMinNewPerWeekDay,
		AsyncPeopleStatsNewPerDay,
		AsyncPeopleStatsNewPerMonth,
		AsyncPeopleStatsNewPerWeek,
		AsyncPeopleStatsPerDay,
		AsyncPeopleStatsPerMonth,
		AsyncPeopleStatsPerWeek,
		AsyncPeopleStatsTotal,
		AsyncProducts,
		AsyncPrograms,
		type StatsInput,
	} from '$lib/graphql/generated/gateway'
	import Dashboard, {
		CardType,
		ChartType,
		type CardNumberData,
		type ChartCardData,
		type DashboardCard,
		type DashboardOptions,
		type ScopeOptions,
	} from '$lib/modules/Dashboard'
	import { SearchAutocomplete } from '$lib/modules/Form'
	import {
		fillMissingData,
		scopeResolution,
		scopeToPreviousScopeQuery,
		scopeToScopeQuery,
		translateWeekDays,
	} from '$lib/utils/entity-stats'

	const config: DashboardOptions = {
		scope: {
			default: 'currentMonth',
		},
		showToolbox: false,
		showScopeSelect: true,
	}

	let scope = $state<ScopeOptions>(config.scope?.default ?? 'currentMonth')

	const total = AsyncPeopleStatsTotal({}).then(({ data }) => data?.peopleStats.total)

	let programs = $state<string[]>([])
	let products = $state<string[]>([])

	const statsQueryMap = (query: StatsInput, programs: string[], products: string[]) => ({
		day: AsyncPeopleStatsPerDay({
			variables: {
				filter: {
					where: {
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
						...(programs?.length ? { programId: { $in: programs } } : {}),
						...(products?.length ? { productId: { $in: products } } : {}),
					},
				},
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.perDay),
		week: AsyncPeopleStatsPerMonth({
			variables: {
				filter: {
					where: {
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
						...(programs?.length ? { programId: { $in: programs } } : {}),
						...(products?.length ? { productId: { $in: products } } : {}),
					},
				},
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.perMonth),
		month: AsyncPeopleStatsPerWeek({
			variables: {
				filter: {
					where: {
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
						...(programs?.length ? { programId: { $in: programs } } : {}),
						...(products?.length ? { productId: { $in: products } } : {}),
					},
				},
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.perWeek),
	})
	const statsNewQueryMap = (query: StatsInput, programs: string[], products: string[]) => ({
		day: AsyncPeopleStatsNewPerDay({
			variables: {
				filter: {
					where: {
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
						...(programs?.length ? { programId: { $in: programs } } : {}),
						...(products?.length ? { productId: { $in: products } } : {}),
					},
				},
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.newPerDay),
		week: AsyncPeopleStatsNewPerMonth({
			variables: {
				filter: {
					where: {
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
						...(programs?.length ? { programId: { $in: programs } } : {}),
						...(products?.length ? { productId: { $in: products } } : {}),
					},
				},
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.newPerMonth),
		month: AsyncPeopleStatsNewPerWeek({
			variables: {
				filter: {
					where: {
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
						...(programs?.length ? { programId: { $in: programs } } : {}),
						...(products?.length ? { productId: { $in: products } } : {}),
					},
				},
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.newPerWeek),
	})

	const perWeekDayQueryMap = (query: StatsInput, programs: string[], products: string[]) => ({
		day: AsyncPeopleStatsAverageNewPerWeekDay({
			variables: {
				filter: {
					where: {
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
						...(programs?.length ? { programId: { $in: programs } } : {}),
						...(products?.length ? { productId: { $in: products } } : {}),
					},
				},
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.averageNewPerWeekDay),
		week: AsyncPeopleStatsMinNewPerWeekDay({
			variables: {
				filter: {
					where: {
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
						...(programs?.length ? { programId: { $in: programs } } : {}),
						...(products?.length ? { productId: { $in: products } } : {}),
					},
				},
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.minNewPerWeekDay),
		month: AsyncPeopleStatsMaxNewPerWeekDay({
			variables: {
				filter: {
					where: {
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
						...(programs?.length ? { programId: { $in: programs } } : {}),
						...(products?.length ? { productId: { $in: products } } : {}),
					},
				},
				scope: query,
			},
		}).then(({ data }) => data?.peopleStats.maxNewPerWeekDay),
	})

	const programsQuery = async (search: string) =>
		AsyncPrograms({
			variables: {
				filter: {
					where: {
						...(search
							? {
									$or: [{ _id: search }, { name: { $regex: search, $options: 'smix' } }, { organizationId: search }],
								}
							: {}),
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
					},
					limit: 50,
				},
			},
			fetchPolicy: 'network-only',
		}).then(({ data }) =>
			data?.programs?.nodes.map((program) => ({ meta: program, value: program._id, label: program.name })),
		)

	const productsQuery = async (search: string) =>
		AsyncProducts({
			variables: {
				filter: {
					where: {
						...(search
							? {
									$or: [{ _id: search }, { name: { $regex: search, $options: 'smix' } }, { organizationId: search }],
								}
							: {}),
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
					},
					limit: 50,
				},
			},
			fetchPolicy: 'network-only',
		}).then(({ data }) =>
			data?.products?.nodes.map((product) => ({ meta: product, value: product._id, label: product.name })),
		)

	const totalGetter = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsNewQueryMap(scopeToScopeQuery(scope), programs, products)[scopeResolution(scope)],
				total,
			]).then(async ([currentScope, total]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false

				return {
					value: total,
					variation: (currentSum / (total || 1)) * 100,
				}
			}),
		}
	})

	const newGetter = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsNewQueryMap(scopeToScopeQuery(scope), programs, products)[scopeResolution(scope)],
				statsNewQueryMap(scopeToPreviousScopeQuery(scope), programs, products)[scopeResolution(scope)],
				statsQueryMap(scopeToScopeQuery(scope), programs, products)[scopeResolution(scope)],
			]).then(async ([currentScope, previousScope, totalPeople]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0
				const previousSum = previousScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false

				return {
					value: currentSum,
					variation: (currentSum - previousSum) / previousSum,
					// objective: 4,
					chart: {
						xAxis: {
							type: 'category',
							data: totalPeople?.x || [],
						},
						series: [
							{
								type: ChartType.LINE,
								name: 'Total',
								data: totalPeople?.y || [],
							},
						],
					},
				}
			}),
		}
	})

	const perDay = $derived.by<ChartCardData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsNewQueryMap(scopeToScopeQuery(scope), programs, products)[scopeResolution(scope)].then((result) =>
					fillMissingData(result, scope, scopeToScopeQuery(scope)),
				),
				statsQueryMap(scopeToPreviousScopeQuery(scope), programs, products)[scopeResolution(scope)].then((result) =>
					fillMissingData(result, scope, scopeToScopeQuery(scope)),
				),
			]).then(async ([newPeople, totalPeople]) => {
				isLoading = false

				return {
					xAxis: {
						type: 'category',
						data: newPeople.x || [],
					},
					series: [
						{
							type: ChartType.BAR,
							name: 'New',
							data: newPeople.y || [],
						},
						{
							type: ChartType.LINE,
							name: 'Total',
							data: totalPeople.y || [],
						},
					],
				}
			}),
		}
	})

	const newPerDay = $derived.by<ChartCardData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: perWeekDayQueryMap(scopeToScopeQuery(scope), programs, products)[scopeResolution(scope)].then(
				async (result) => {
					if (!result?.x?.length) {
						isLoading = false
						return {}
					}

					const data = fillMissingData(result, 'weekDays')

					isLoading = false

					return {
						xAxis: {
							type: 'category',
							name: 'People',
							data: data?.x ? translateWeekDays(data.x) : [],
						},
						series: [
							{
								type: ChartType.BAR,
								name: 'People',
								data: data?.y || [],
							},
						],
					}
				},
			),
		}
	})

	const cards = $derived<DashboardCard[]>([
		{
			type: CardType.NUMBER,
			header: {
				title: 'Total People',
			},
			options: {},
			class: 'col-50 row-1',
			getter: totalGetter,
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'New People',
			},
			class: 'col-50 row-1',
			getter: newGetter,
		},
		{
			type: CardType.CHART,
			chartType: ChartType.LINE,
			options: {},
			header: {
				title: 'People per Day',
			},
			class: 'col-100 row-3',
			getter: perDay,
		},
		{
			type: CardType.CHART,
			chartType: ChartType.BAR,
			options: {},
			header: {
				title: 'New People per week day',
			},
			class: 'col-100 row-3',
			getter: newPerDay,
		},
	])
</script>

{#key page.params.orgId ?? page.data.organizationId}
	<Dashboard bind:scope {cards} {config}>
		{#snippet filter()}
			<div class="flex space-x-5">
				<SearchAutocomplete
					placeholder="Programs"
					options={programsQuery}
					multiple
					onValueChange={(value?: string[]) => (programs = value || [])}
				/>
				<SearchAutocomplete
					placeholder="Products"
					options={productsQuery}
					multiple
					onValueChange={(value?: string[]) => (products = value || [])}
				/>
			</div>
		{/snippet}
	</Dashboard>
{/key}
