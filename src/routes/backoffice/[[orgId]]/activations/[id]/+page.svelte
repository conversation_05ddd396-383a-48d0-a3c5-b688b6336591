<script lang="ts">
	import { Accordion } from '@skeletonlabs/skeleton-svelte'

	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import { createActivationForm } from '$lib/forms'
	import { UpdateActivation, type ActivationQuery } from '$lib/graphql/generated/gateway'
	import { clearFormData, openFormDrawer } from '$lib/modules/Form'
	import { durationFormatterShort } from '$lib/utils'

	import { clipboard } from '$lib/actions/clipboard.svelte'

	let activation: ActivationQuery['activation'] = $derived(page.data.activation)

	function gotoManagePage() {
		goto('/backoffice/activations/manage')
	}

	function openEditDrawer() {
		const fields = createActivationForm(activation.organizationId)

		openFormDrawer({
			title: 'Update Activation',
			submitLabel: 'Update',
			fields,
			data: clearFormData(
				{
					organizationId: activation.organizationId,
					name: activation.name,
					url: activation.url,
					picture: activation.picture || undefined,
					description: activation.description || undefined,
					type: activation.type || undefined,
					date: activation.date,
					location: activation.location || undefined,
					range: {
						start: activation.runFrom,
						end: activation.runTo,
					},
					limit: activation.limit || undefined,
					labels: activation.filter.labels || undefined,
					productIds: activation.filter.productIds || undefined,
					programIds: activation.filter.programIds || undefined,
					filterRange: {
						start: activation.filter.registeredMinDate,
						end: activation.filter.registeredMaxDate,
					},
				},
				fields,
			),
			onSubmit: async (data) => {
				const result = await UpdateActivation({
					variables: {
						partialEntity: {
							_id: activation._id,
							organizationId: data.organizationId,
							name: data.name,
							url: data.url,
							picture: data.picture,
							description: data.description,
							type: data.type,
							date: data.date,
							location: data.location,
							runFrom: data.range.start,
							runTo: data.range.end,
							limit: data.limit,
							filter: {
								labels: data.labels,
								productIds: data.productIds,
								programIds: data.programIds,
								registeredMinDate: data.filterRange.start,
								registeredMaxDate: data.filterRange.end,
							},
						},
					},
				})

				if (result.data) {
					activation = result.data.updateActivation
				}
			},
		})
	}

	const status = $derived(
		new Date(activation.runFrom) > new Date()
			? 'Upcoming'
			: new Date(activation.runTo) < new Date()
				? 'Finished'
				: 'Running',
	)

	const statusColor = $derived(
		status === 'Upcoming'
			? 'preset-filled-secondary'
			: status === 'Finished'
				? 'preset-filled'
				: 'preset-filled-success',
	)
	const filledPercentage = $derived(
		Math.round(((activation.numberOfParticipants || 0) / (activation.limit || 0)) * 100),
	)
	const filledPercentageColor = $derived(
		filledPercentage > 90
			? 'preset-filled-success'
			: filledPercentage > 70
				? 'preset-filled-warning'
				: 'preset-filled-error',
	)

	let main = $state<string[]>([])
	let footerLeft = $state<string[]>([])
	let footerRight = $state<string[]>([])
</script>

<div class="container mx-auto p-2 lg:mx-0 lg:p-6">
	<div class="flex items-center justify-between">
		<button type="button" class="preset-tonal btn" onclick={gotoManagePage}>
			<iconify-icon icon="radix-icons:chevron-left" inline></iconify-icon>
			<span>Manage</span>
		</button>

		<button type="button" class="preset-filled-secondary-500 btn" onclick={openEditDrawer}
			><iconify-icon icon="radix-icons:pencil-1" inline></iconify-icon><span>Edit</span></button
		>
	</div>

	<br />

	<div class="flex items-end justify-between space-x-4">
		<h1 class="h2">{activation.name}</h1>

		{#if activation.picture}
			<img src={activation.picture || undefined} alt="" class="max-h-32" />
		{/if}
	</div>

	<hr class="mt-4" />

	<p class="mb-4 flex items-center">
		Entity ID: {activation._id}

		<button use:clipboard={activation._id} class="btn-icon" aria-label="Copy Entity ID">
			<iconify-icon icon="radix-icons:copy" inline></iconify-icon>
		</button>
	</p>

	<div class="card preset-outlined-surface-500 p-4">
		<Accordion value={main} onValueChange={(e) => (main = e.value)} multiple collapsible>
			<Accordion.Item value="details">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:info-circled" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<span class="font-bold">Details</span>
				{/snippet}

				{#snippet panel()}
					<div class="table-wrap rounded-xl">
						<table class="table border-separate border-spacing-[1px]">
							<tbody>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Description</td>
									<td class="preset-tonal"><div class="whitespace-pre-line">{activation.description || ''}</div></td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">External Url</td>
									<td class="preset-tonal"><a href={activation.url} target="_blank">{activation.url}</a></td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Limit</td>
									<td class="preset-tonal">{activation.limit || 'No Limit'}</td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Type</td>
									<td class="preset-tonal">{activation.type || ''}</td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Location</td>
									<td class="preset-tonal">{activation.location || ''}</td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Runs from</td>
									<td class="preset-tonal"
										>{activation.runFrom
											? new Date(activation.runFrom).toLocaleString(undefined, {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Runs to</td>
									<td class="preset-tonal"
										>{activation.runTo
											? new Date(activation.runTo).toLocaleString(undefined, {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Created</td>
									<td class="preset-tonal"
										>{activation.createdAt
											? new Date(activation.createdAt).toLocaleString(undefined, {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Updated</td>
									<td class="preset-tonal"
										>{activation.updatedAt
											? new Date(activation.updatedAt).toLocaleString(undefined, {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>

			<Accordion.Item value="filter">
				{#snippet lead()}
					<iconify-icon icon="ion:filter-outline" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<span class="font-bold">Filter</span>
				{/snippet}

				{#snippet panel()}
					<div class="table-wrap rounded-xl">
						<table class="table border-separate border-spacing-[1px]">
							<tbody>
								{#if activation.filter?.labels?.length || activation.filterProducts?.length || activation.filterPrograms?.length || activation.filter.registeredMinDate || activation.filter.registeredMaxDate}
									{#if activation.filterProducts?.length}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Products</td>
											<td class="preset-tonal flex flex-wrap gap-2">
												{#each activation.filterProducts as product (product._id)}
													<a
														href={`/backoffice/products/${product._id}`}
														class="preset-soft chip flex items-center gap-1"
													>
														{#if product.picture}
															<img src={product.picture} alt="" class="max-h-6" />
														{/if}
														<span>{product.name}</span>
													</a>
												{/each}
											</td>
										</tr>
									{/if}
									{#if activation.filterPrograms?.length}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Programs</td>
											<td class="preset-tonal flex flex-wrap gap-2">
												{#each activation.filterPrograms as program (program._id)}
													<a
														href={`/backoffice/programs/${program._id}`}
														class="preset-soft chip flex items-center gap-1">{program.name}</a
													>
												{/each}
											</td>
										</tr>
									{/if}
									{#if activation.filter.labels?.length}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Labels</td>
											<td class="preset-tonal flex flex-wrap gap-2">
												{#each activation.filter.labels as label (label)}
													<span class="preset-soft chip">{label}</span>
												{/each}
											</td>
										</tr>
									{/if}
									{#if activation.filter.registeredMinDate}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium"
												>Registration Starts at</td
											>
											<td class="preset-tonal"
												>{new Date(activation.filter.registeredMinDate).toLocaleString(undefined, {
													dateStyle: 'full',
													timeStyle: 'long',
												})}</td
											>
										</tr>
									{/if}
									{#if activation.filter.registeredMaxDate}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium"
												>Registration Ends at</td
											>
											<td class="preset-tonal"
												>{new Date(activation.filter.registeredMaxDate).toLocaleString(undefined, {
													dateStyle: 'full',
													timeStyle: 'long',
												})}</td
											>
										</tr>
									{/if}
								{:else}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium"></td>
										<td class="preset-tonal"><div class="whitespace-pre-line">No filter</div></td>
									</tr>
								{/if}
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>

			<Accordion.Item value="status">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:update" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<span class="font-bold">Status</span>
				{/snippet}

				{#snippet panel()}
					<div class="table-wrap rounded-xl">
						<table class="table border-separate border-spacing-[1px]">
							<tbody>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Status</td>
									<td class="preset-tonal">
										<span class="badge {statusColor}">{status}</span>
									</td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Participants</td>
									<td class="preset-tonal">
										{activation.numberOfParticipants || 0} of {activation.limit}
										{#if activation.limit}
											<span class="badge {filledPercentageColor} ml-2">{filledPercentage}%</span>
										{/if}
									</td>
								</tr>

								{#if status === 'Running'}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Remaining</td>
										<td class="preset-tonal"
											>{durationFormatterShort(new Date(activation.runTo).getTime() - new Date().getTime())}</td
										>
									</tr>
								{:else if status === 'Upcoming'}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Starts in</td>
										<td class="preset-tonal"
											>{durationFormatterShort(new Date(activation.runFrom).getTime() - new Date().getTime())}</td
										>
									</tr>
								{/if}
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>

			{#if !(page.params.orgId ?? page.data.organizationId)}
				<Accordion.Item value="organization">
					{#snippet lead()}
						<iconify-icon icon="radix-icons:backpack" inline></iconify-icon>
					{/snippet}

					{#snippet control()}
						<span class="font-bold">Organization</span>
					{/snippet}

					{#snippet panel()}
						<div class="table-container">
							<table class="table-compact table">
								<tbody>
									{#if activation.organization}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">
												{#if activation.organization.logo}
													<a href={`/backoffice/organizations/${activation.organization._id}`}>
														<img src={activation.organization.logo} alt="" class="inline max-h-6" />
													</a>
												{/if}
											</td>
											<td class="preset-tonal">
												<a href={`/backoffice/organizations/${activation.organization._id}`}
													>{activation.organization.name || ''}</a
												>
											</td>
										</tr>
									{:else}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium"></td>
											<td class="preset-tonal">
												<span>No organizations available</span>
											</td>
										</tr>
									{/if}
								</tbody>
							</table>
						</div>
					{/snippet}
				</Accordion.Item>
			{/if}
		</Accordion>
	</div>

	<div class="mt-4 columns-1 gap-4 md:columns-2">
		<Accordion
			classes="card p-2 preset-tonal break-inside-avoid-column"
			value={footerLeft}
			onValueChange={(e) => (footerLeft = e.value)}
			multiple
			collapsible
		>
			<Accordion.Item value="notes">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:file-text" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<div class="flex w-full items-center justify-between">
						<span class="font-bold">Notes</span>
					</div>
				{/snippet}

				{#snippet panel()}
					<p class="text-sm">TODO</p>
				{/snippet}
			</Accordion.Item>
		</Accordion>

		<Accordion
			classes="card p-2 preset-tonal break-inside-avoid-column"
			value={footerRight}
			onValueChange={(e) => (footerRight = e.value)}
			multiple
			collapsible
		>
			<Accordion.Item value="activity-stream">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:activity-log" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<div class="flex w-full items-center justify-between">
						<span class="font-bold">Activity Stream</span>
					</div>
				{/snippet}

				{#snippet panel()}
					<p class="text-sm">TODO</p>
				{/snippet}
			</Accordion.Item>
		</Accordion>
	</div>
</div>
