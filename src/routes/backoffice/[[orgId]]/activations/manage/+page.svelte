<script lang="ts">
	import { MediaQuery } from 'svelte/reactivity'

	import { page } from '$app/state'

	import { createActivationForm } from '$lib/forms/activations.form'
	import {
		Activations,
		CreateActivation,
		UpdateActivation,
		type ActivationsQuery,
	} from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { clearFormData, openFormDrawer } from '$lib/modules/Form'

	import type { QueryReturnType } from '$lib/utils'

	const matches = new MediaQuery('max-width: 1024px')

	let starts = $state<Date | undefined>()
	let ends = $state<Date | undefined>()

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	type ActivationsResult = QueryReturnType<ActivationsQuery['activations']>

	const config = $derived<DataTableConfig<ActivationsResult>>({
		columns: [
			{
				type: ColumnType.String,
				label: 'Name',
				property: 'name',
				sortable: 'name',
				href: (row) => `/backoffice/activations/${row._id}`,
			},
			{
				type: ColumnType.String,
				label: 'Type',
				property: 'type',
				sortable: 'type',
			},
			{
				type: ColumnType.Profile,
				label: 'Organization',
				image: 'organization.logo',
				name: 'organization.name',
				href: (row) => row.organizationId && `/backoffice/organizations/${row.organizationId}`,
				hide: !!(page.params.orgId ?? page.data.organizationId) || matches.current,
			},
			{
				type: ColumnType.String,
				label: 'Limit',
				property: 'limit',
				sortable: 'limit',
			},
			{
				type: ColumnType.String,
				label: 'Participants',
				property: 'numberOfParticipants',
				sortable: 'numberOfParticipants',
				fallback: 0,
				hide: matches.current,
			},
			{
				type: ColumnType.Interval,
				label: 'Run Between',
				start: 'runFrom',
				end: 'runTo',
				sortable: 'runFrom',
				options: {
					dateStyle: 'medium',
				},
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Date',
				property: 'date',
				sortable: 'date',
				options: {
					dateStyle: 'medium',
				},
				hide: matches.current,
			},
			{
				type: ColumnType.String,
				label: 'Location',
				property: 'location',
				sortable: 'location',
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Updated',
				property: 'updatedAt',
				sortable: 'updatedAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				hide: true,
			},
			{
				type: ColumnType.Date,
				label: 'Created',
				property: 'createdAt',
				sortable: 'createdAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				hide: true,
			},
		],
		actions: [
			{
				action: (items) => {
					window.open(items[0].url, '_blank')
				},
				icon: 'radix-icons:external-link',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
			},
			{
				action: (items) => {
					const fields = createActivationForm(items[0].organizationId)

					openFormDrawer({
						title: 'Update Activation',
						submitLabel: 'Update',
						fields: fields,
						data: clearFormData(
							{
								organizationId: items[0].organizationId,
								name: items[0].name,
								url: items[0].url,
								picture: items[0].picture || undefined,
								description: items[0].description || undefined,
								type: items[0].type || undefined,
								date: items[0].date,
								location: items[0].location || undefined,
								range: {
									start: items[0].runFrom,
									end: items[0].runTo,
								},
								limit: items[0].limit || undefined,
								labels: items[0].filter.labels || undefined,
								productIds: items[0].filter.productIds || undefined,
								programIds: items[0].filter.programIds || undefined,
								filterRange: {
									start: items[0].filter.registeredMinDate,
									end: items[0].filter.registeredMaxDate,
								},
							},
							fields,
						),
						onSubmit: async (data) => {
							await UpdateActivation({
								variables: {
									partialEntity: {
										_id: items[0]._id,
										organizationId: data.organizationId,
										name: data.name,
										url: data.url,
										picture: data.picture,
										description: data.description,
										type: data.type,
										date: data.date,
										location: data.location,
										runFrom: data.range.start,
										runTo: data.range.end,
										limit: data.limit,
										filter: {
											labels: data.labels,
											productIds: data.productIds,
											programIds: data.programIds,
											registeredMinDate: data.filterRange.start,
											registeredMaxDate: data.filterRange.end,
										},
									},
								},
								refetchQueries: ['Activations'],
							})
						},
					})
				},
				icon: 'radix-icons:pencil-1',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
			},
		],
	})

	const query = () =>
		Activations({
			variables: {
				search: {
					...(filter.search
						? {
								$or: [
									{ _Id: filter.search },
									{ name: { $regex: filter.search, $options: 'smix' } },
									{ organizationId: filter.search },
								],
							}
						: {}),
					...(starts || ends
						? {
								$and: [
									...(starts ? [{ runFrom: { $gte: starts } }] : []),
									...(ends ? [{ runTo: { $lte: ends } }] : []),
								],
							}
						: {}),
					...((page.params.orgId ?? page.data.organizationId)
						? { organizationId: page.params.orgId ?? page.data.organizationId }
						: {}),
				},
				options: {
					limit: filter.limit,
					offset: filter.page * filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				},
			},
		})
	const result = query()

	$effect(() => {
		$result.query.refetch({
			search: {
				...(filter.search
					? {
							$or: [
								{ _Id: filter.search },
								{ name: { $regex: filter.search, $options: 'smix' } },
								{ organizationId: filter.search },
							],
						}
					: {}),
				...(starts || ends
					? {
							$and: [...(starts ? [{ runFrom: { $gte: starts } }] : []), ...(ends ? [{ runTo: { $lte: ends } }] : [])],
						}
					: {}),
				...((page.params.orgId ?? page.data.organizationId)
					? { organizationId: page.params.orgId ?? page.data.organizationId }
					: {}),
			},
			options: {
				limit: filter.limit,
				offset: filter.page * filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
			},
		})
	})

	const data = $derived({
		rows: $result.data?.activations?.nodes || [],
		total: $result.data?.activations?.totalCount || 0,
	})

	function create() {
		openFormDrawer({
			title: 'Create Activation',
			submitLabel: 'Create',
			fields: createActivationForm(page?.params.orgId || page.data.organizationId),
			data: {
				organizationId: page.params.orgId ?? page.data.organizationId,
			},
			onSubmit: async (data) => {
				await CreateActivation({
					variables: {
						data: {
							organizationId: data.organizationId,
							name: data.name,
							url: data.url,
							picture: data.picture,
							description: data.description,
							type: data.type,
							date: data.date,
							location: data.location,
							runFrom: data.range.start,
							runTo: data.range.end,
							limit: data.limit,
							filter: {
								labels: data.labels,
								productIds: data.productIds,
								programIds: data.programIds,
								registeredMinDate: data.filterRange.start,
								registeredMaxDate: data.filterRange.end,
							},
						},
					},
					refetchQueries: ['Activations'],
				})
			},
		})
	}
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}>
	{#snippet filterActions()}
		<button class="btn preset-filled-secondary-500 w-fit" onclick={create}>Create New</button>
	{/snippet}

	{#snippet filterExtra()}
		<input class="input" title="Run from" type="datetime-local" bind:value={starts} />
		<input class="input" title="Run to" type="datetime-local" bind:value={ends} />
	{/snippet}
</DataTable>
