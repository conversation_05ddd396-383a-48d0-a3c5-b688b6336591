<script lang="ts">
	import { Tabs } from '@skeletonlabs/skeleton-svelte'

	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import type { Snippet } from 'svelte'

	const { children }: { children?: Snippet } = $props()
</script>

<Tabs value={page.url.pathname} onValueChange={(e) => goto(e.value)} fluid classes="mt-2">
	{#snippet list()}
		<Tabs.Control value="/backoffice/activations/dashboard">Dashboard</Tabs.Control>

		<Tabs.Control value="/backoffice/activations">Manage</Tabs.Control>
	{/snippet}

	{#snippet content()}
		{@render children?.()}
	{/snippet}
</Tabs>
