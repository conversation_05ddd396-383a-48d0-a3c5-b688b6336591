<script lang="ts">
	import { Accordion } from '@skeletonlabs/skeleton-svelte'

	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import { createTagsForm, attachProgram, attachProduct, detachProgram, detachProduct } from '$lib/forms'
	import { type TagQuery, UpdateTag } from '$lib/graphql/generated/gateway'
	import { applyChanges, openFormDrawer, clearFormData } from '$lib/modules/Form'

	import { clipboard } from '$lib/actions/clipboard.svelte'

	let tag: TagQuery['tag'] = $derived(page.data.tag)

	function gotoManagePage() {
		goto('/backoffice/tags/manage')
	}

	function openEditDrawer() {
		const fields = applyChanges(createTagsForm(tag.organizationId), [
			{
				group: 'Chips Details',
				change: 'hide',
			},
			{
				key: 'programId',
				change: 'readOnly',
			},
			{
				key: 'productId',
				change: 'readOnly',
			},
		])

		openFormDrawer({
			title: 'Update Tag',
			submitLabel: 'Update',
			fields,
			data: clearFormData(
				{
					...tag,
					labels: tag.labels || undefined,
					productDetails: tag.productDetails || undefined,
				},
				fields,
			),
			onSubmit: async (partialEntity) => {
				const result = await UpdateTag({
					variables: {
						partialEntity: {
							...partialEntity,
							_id: tag._id,
						},
					},
				})

				if (result.data) {
					tag = result.data.updateTag
				}
			},
		})
	}

	let main = $state<string[]>([])
	let footerLeft = $state<string[]>([])
	let footerRight = $state<string[]>([])
</script>

<div class="container mx-auto p-2 lg:mx-0 lg:p-6">
	<div class="flex items-center justify-between">
		<button type="button" class="preset-tonal btn" onclick={gotoManagePage}>
			<iconify-icon icon="radix-icons:chevron-left" inline></iconify-icon>
			<span>Manage</span>
		</button>

		<button type="button" class="preset-filled-secondary-500 btn" onclick={openEditDrawer}
			><iconify-icon icon="radix-icons:pencil-1" inline></iconify-icon><span>Edit</span></button
		>
	</div>

	<br />

	<div class="flex items-end justify-between">
		<h1 class="h2" use:clipboard={tag.chip.serialNumber}>{tag.chip.serialNumber}</h1>

		<div class="flex space-x-3">
			{#if !tag.program}
				<button
					type="button"
					class="preset-ghost-success btn"
					onclick={() => attachProgram(page.params.orgId ?? page.data.organizationId ?? tag.organizationId, [tag])}
					>Attach Program</button
				>
			{/if}

			{#if !tag.product}
				<button
					type="button"
					class="preset-ghost-secondary btn"
					onclick={() => attachProduct(page.params.orgId ?? page.data.organizationId ?? tag.organizationId, [tag])}
					>Attach Product</button
				>
			{/if}
		</div>
	</div>

	<hr class="mt-4" />

	<p class="mb-4 flex items-center">
		Entity ID: {tag._id}

		<button use:clipboard={tag._id} class="btn-icon" aria-label="Copy Entity ID">
			<iconify-icon icon="radix-icons:copy" inline></iconify-icon>
		</button>
	</p>

	<div class="card preset-outlined-surface-500 p-4">
		<Accordion value={main} onValueChange={(e) => (main = e.value)} multiple collapsible>
			<Accordion.Item value="details">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:info-circled" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<span class="font-bold">Details</span>
				{/snippet}

				{#snippet panel()}
					<div class="table-container">
						<table class="table-compact table">
							<tbody>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Chip Type</td>
									<td class="preset-tonal"><span use:clipboard={tag.chip?.type || ''}>{tag.chip?.type || ''}</span></td>
								</tr>

								{#if tag.labels?.length}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Labels</td>
										<td class="preset-tonal flex flex-wrap gap-2">
											{#each tag.labels as label (label)}
												<span class="preset-soft chip">{label}</span>
											{/each}
										</td>
									</tr>
								{/if}

								{#if tag.program}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Program</td>
										<td class="preset-tonal flex flex-wrap justify-between gap-2">
											<a
												href={`/backoffice/programs/${tag.program._id}`}
												class="preset-soft chip flex items-center gap-1"
											>
												<span>{tag.program.name}</span>
											</a>

											<button
												type="button"
												class="preset-soft btn btn-sm"
												onclick={() => detachProgram([tag])}
												aria-label="Detach Program"
												><iconify-icon icon="radix-icons:cross-2" inline></iconify-icon></button
											>
										</td>
									</tr>
								{/if}

								{#if tag.product}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Product</td>
										<td class="preset-tonal flex flex-wrap justify-between gap-2">
											<a
												href={`/backoffice/products/${tag.product._id}`}
												class="preset-soft chip flex items-center gap-1"
											>
												{#if tag.product.picture}
													<img src={tag.product.picture} alt="" class="max-h-6" />
												{/if}
												<span>{tag.product.name}</span>
											</a>
											<button
												type="button"
												class="preset-soft btn btn-sm"
												onclick={() => detachProduct([tag])}
												aria-label="Detach Product"
												><iconify-icon icon="radix-icons:cross-2" inline></iconify-icon></button
											>
										</td>
									</tr>
								{/if}
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Created</td>
									<td class="preset-tonal"
										>{tag.createdAt
											? new Date(tag.createdAt).toLocaleString(undefined, {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Updated</td>
									<td class="preset-tonal"
										>{tag.updatedAt
											? new Date(tag.updatedAt).toLocaleString(undefined, {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>

			{#if tag.userId}
				<Accordion.Item value="registered">
					{#snippet lead()}
						<iconify-icon icon="radix-icons:person" inline></iconify-icon>
					{/snippet}

					{#snippet control()}
						<span class="font-bold">Registered</span>
					{/snippet}

					{#snippet panel()}
						<div class="table-container">
							<table class="table-compact table">
								<tbody>
									{#if tag.user}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">
												{#if tag.user.picture}
													<a href={`/backoffice/users/${tag.user._id}`}>
														<img src={tag.user.picture} alt="" class="inline max-h-6" />
													</a>
												{/if}
											</td>
											<td class="preset-tonal">
												<a href={`/backoffice/users/${tag.user._id}`}>{tag.user.name || ''}</a>
											</td>
										</tr>
									{/if}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Registered At</td>
										<td class="preset-tonal"
											>{tag.registeredAt
												? new Date(tag.registeredAt).toLocaleString(undefined, {
														dateStyle: 'full',
														timeStyle: 'long',
													})
												: ''}</td
										>
									</tr>
								</tbody>
							</table>
						</div>
					{/snippet}
				</Accordion.Item>
			{/if}

			{#if !(page.params.orgId ?? page.data.organizationId)}
				<Accordion.Item value="organization">
					{#snippet lead()}
						<iconify-icon icon="radix-icons:backpack" inline></iconify-icon>
					{/snippet}

					{#snippet control()}
						<span class="font-bold">Organization</span>
					{/snippet}

					{#snippet panel()}
						<div class="table-container">
							<table class="table-compact table">
								<tbody>
									{#if tag.organization}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">
												{#if tag.organization.logo}
													<a href={`/backoffice/organizations/${tag.organization._id}`}>
														<img src={tag.organization.logo} alt="" class="inline max-h-6" />
													</a>
												{/if}
											</td>
											<td class="preset-tonal">
												<a href={`/backoffice/organizations/${tag.organization._id}`}>{tag.organization.name || ''}</a>
											</td>
										</tr>
									{:else}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium"></td>
											<td class="preset-tonal">
												<span>No organizations available</span>
											</td>
										</tr>
									{/if}
								</tbody>
							</table>
						</div>
					{/snippet}
				</Accordion.Item>
			{/if}

			<!-- TODO -->
			<!-- {#if activations}
				<AccordionItem>
					<svelte:fragment slot="lead">
						<iconify-icon icon="radix-icons:lightning-bolt" inline />
					</svelte:fragment>

					<svelte:fragment slot="summary">
						<span class="font-bold">Activations</span>
					</svelte:fragment>

					<svelte:fragment slot="content">
						<div class="table-container">
							<table class="table table-compact">
								<tbody>
									{#each activations as activation}
										<tr>
											<td class="dark:bg-surface-700 bg-surface-100 font-medium w-1/4 text-right">
												{#if activation.picture}
													<a href={`/backoffice/activations/${activation._id}`}>
														<img src={activation.picture} alt="" class="max-h-6 inline" />
													</a>
												{/if}
											</td>
											<td class="dark:bg-surface-800 bg-surface-200">
												<a href={`/backoffice/activations/${activation._id}`}>{activation.name || ''}</a>
											</td>
										</tr>
									{/each}
								</tbody>
							</table>
						</div>
					</svelte:fragment>
				</AccordionItem>
			{/if} -->
		</Accordion>
	</div>

	<div class="mt-4 columns-1 gap-4 md:columns-2">
		<Accordion
			classes="card p-2 preset-tonal break-inside-avoid-column"
			value={footerLeft}
			onValueChange={(e) => (footerLeft = e.value)}
			multiple
			collapsible
		>
			<Accordion.Item value="notes">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:file-text" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<div class="flex w-full items-center justify-between">
						<span class="font-bold">Notes</span>
					</div>
				{/snippet}

				{#snippet panel()}
					<p class="text-sm">TODO</p>
				{/snippet}
			</Accordion.Item>
		</Accordion>

		<Accordion
			classes="card p-2 preset-tonal break-inside-avoid-column"
			value={footerRight}
			onValueChange={(e) => (footerRight = e.value)}
			multiple
			collapsible
		>
			<Accordion.Item value="activity-stream">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:activity-log" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<div class="flex w-full items-center justify-between">
						<span class="font-bold">Activity Stream</span>
					</div>
				{/snippet}

				{#snippet panel()}
					<p class="text-sm">TODO</p>
				{/snippet}
			</Accordion.Item>
		</Accordion>
	</div>
</div>
