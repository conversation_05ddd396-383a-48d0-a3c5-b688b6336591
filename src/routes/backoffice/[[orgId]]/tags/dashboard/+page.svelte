<script lang="ts">
	import { page } from '$app/state'

	import {
		type StatsInput,
		AsyncTagStatsAverageNewPerWeekDay,
		AsyncTagStatsMaxNewPerWeekDay,
		AsyncTagStatsMinNewPerWeekDay,
		AsyncTagStatsNewPerDay,
		AsyncTagStatsNewPerMonth,
		AsyncTagStatsNewPerWeek,
		AsyncTagStatsPerDay,
		AsyncTagStatsPerMonth,
		AsyncTagStatsPerWeek,
		AsyncTagStatsTotal,
	} from '$lib/graphql/generated/gateway'
	import Dashboard, {
		type DashboardOptions,
		type ScopeOptions,
		type ChartCardData,
		type DashboardCard,
		type CardNumberData,
		ChartType,
		CardType,
	} from '$lib/modules/Dashboard'
	import {
		scopeToScopeQuery,
		scopeResolution,
		fillMissingData,
		scopeToPreviousScopeQuery,
		translateWeekDays,
	} from '$lib/utils/entity-stats'
	const config: DashboardOptions = {
		scope: {
			default: 'currentMonth',
		},
		showToolbox: false,
		showScopeSelect: true,
	}

	let scope = $state<ScopeOptions>(config.scope?.default ?? 'currentMonth')

	const total = AsyncTagStatsTotal({}).then(({ data }) => data?.tagStats.total)

	const statsQueryMap = (query: StatsInput) => ({
		day: AsyncTagStatsPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.perDay),
		week: AsyncTagStatsPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.perMonth),
		month: AsyncTagStatsPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.perWeek),
	})
	const statsNewQueryMap = (query: StatsInput) => ({
		day: AsyncTagStatsNewPerDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.newPerDay),
		week: AsyncTagStatsNewPerMonth({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.newPerMonth),
		month: AsyncTagStatsNewPerWeek({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.newPerWeek),
	})

	const perWeekDayQueryMap = (query: StatsInput) => ({
		day: AsyncTagStatsAverageNewPerWeekDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.averageNewPerWeekDay),
		week: AsyncTagStatsMinNewPerWeekDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.minNewPerWeekDay),
		month: AsyncTagStatsMaxNewPerWeekDay({
			variables: {
				...((page.params.orgId ?? page.data.organizationId)
					? { filter: { where: { organizationId: page.params.orgId ?? page.data.organizationId } } }
					: {}),
				scope: query,
			},
		}).then(({ data }) => data?.tagStats.maxNewPerWeekDay),
	})

	const totalGetter = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([statsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)], total]).then(
				async ([currentScope, total]) => {
					const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0

					isLoading = false

					return {
						value: total,
						variation: (currentSum / (total || 1)) * 100,
					}
				},
			),
		}
	})

	const newGetter = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
				statsNewQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)],
				statsQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
			]).then(async ([currentScope, previousScope, totalOrganizations]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0
				const previousSum = previousScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false

				return {
					value: currentSum,
					variation: (currentSum - previousSum) / previousSum,
					// objective: 4,
					chart: {
						xAxis: {
							type: 'category',
							data: totalOrganizations?.x || [],
						},
						series: [
							{
								type: ChartType.LINE,
								name: 'Total',
								data: totalOrganizations?.y || [],
							},
						],
					},
				}
			}),
		}
	})

	const perDay = $derived.by<ChartCardData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then((result) =>
					fillMissingData(result, scope, scopeToScopeQuery(scope)),
				),
				statsQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)].then((result) =>
					fillMissingData(result, scope, scopeToScopeQuery(scope)),
				),
			]).then(async ([newTags, totalTags]) => {
				isLoading = false

				return {
					xAxis: {
						type: 'category',
						data: newTags.x || [],
					},
					series: [
						{
							type: ChartType.BAR,
							name: 'New',
							data: newTags.y || [],
						},
						{
							type: ChartType.LINE,
							name: 'Total',
							data: totalTags.y || [],
						},
					],
				}
			}),
		}
	})

	const newPerDay = $derived.by<ChartCardData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: perWeekDayQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then(async (result) => {
				if (!result?.x?.length) {
					isLoading = false
					return {}
				}

				const data = fillMissingData(result, 'weekDays')

				isLoading = false

				return {
					xAxis: {
						type: 'category',
						name: 'Tags',
						data: data?.x ? translateWeekDays(data.x) : [],
					},
					series: [
						{
							type: ChartType.BAR,
							name: 'Tags',
							data: data?.y || [],
						},
					],
				}
			}),
		}
	})

	const cards = $derived<DashboardCard[]>([
		{
			type: CardType.NUMBER,
			header: {
				title: 'Total Tags',
			},
			options: {},
			class: 'col-50 row-1',
			getter: totalGetter,
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'New Tags',
			},
			class: 'col-50 row-1',
			getter: newGetter,
		},
		{
			type: CardType.CHART,
			chartType: ChartType.LINE,
			options: {},
			header: {
				title: 'Tags per Day',
			},
			class: 'col-100 row-3',
			getter: perDay,
		},
		{
			type: CardType.CHART,
			chartType: ChartType.BAR,
			options: {},
			header: {
				title: 'New Tags per week day',
			},
			class: 'col-100 row-3',
			getter: newPerDay,
		},
	])
</script>

{#key page.params.orgId ?? page.data.organizationId}
	<Dashboard bind:scope {cards} {config} />
{/key}
