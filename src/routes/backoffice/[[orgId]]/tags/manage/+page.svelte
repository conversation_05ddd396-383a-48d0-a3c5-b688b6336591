<script lang="ts">
	import { MediaQuery } from 'svelte/reactivity'
	import { inferSchema, initParser } from 'udsv'

	import { page } from '$app/state'

	import {
		attachProduct,
		attachProgram,
		createTagsForm,
		detachProduct,
		detachProgram,
		type TagsResult,
	} from '$lib/forms'
	import {
		Tags,
		UpdateTag,
		CreateTags,
		type CreateChip,
		NxpChipType,
		type CreateNxpChip,
		type CreateEm4425Chip,
	} from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { applyChanges, clearFormData, openFormDrawer } from '$lib/modules/Form'
	import { toaster } from '$lib/modules/Notifications'
	import ToggleGroup from '$lib/modules/ToggleGroup'

	import { getAuth } from '$lib/services/auth.svelte'

	const matches = new MediaQuery('max-width: 1024px')
	const auth = getAuth()

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	let registered = $state<string[]>([])

	let selectedItems = $state<TagsResult[]>([])

	const config = $derived<DataTableConfig<TagsResult>>({
		columns: [
			{
				type: ColumnType.String,
				label: 'ID',
				property: '_id',
				sortable: '_id',
				href: (row) => `/backoffice/tags/${row._id}`,
				hide: true,
			},
			{
				type: ColumnType.String,
				label: 'Chip SN',
				property: 'chip.serialNumber',
				sortable: 'chip.serialNumber',
				href: (row) => `/backoffice/tags/${row._id}`,
			},
			{
				type: ColumnType.String,
				label: 'Chip Type',
				property: 'chip.type',
				sortable: 'chip.type',
				hide: true,
			},
			{
				type: ColumnType.Profile,
				label: 'Organization',
				image: 'organization.logo',
				name: 'organization.name',
				href: (row) => row.organizationId && `/backoffice/organizations/${row.organizationId}`,
				hide: !!(page.params.orgId ?? page.data.organizationId) || matches.current,
			},
			{
				type: ColumnType.Profile,
				label: 'User',
				image: 'user.picture',
				name: 'user.name',
				href: (row) => `/backoffice/users/${row.userId}`,
			},
			{
				type: ColumnType.Profile,
				label: 'Product',
				image: 'product.picture',
				name: 'product.name',
				href: (row) => `/backoffice/products/${row.productId}`,
				hide: matches.current,
			},
			{
				type: ColumnType.Profile,
				label: 'Program',
				name: 'program.name',
				href: (row) => `/backoffice/programs/${row.programId}`,
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Registered',
				property: 'registeredAt',
				sortable: 'registeredAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				hide: matches.current,
			},
			{
				type: ColumnType.ChipList,
				label: 'Labels',
				property: 'labels',
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Updated',
				property: 'updatedAt',
				sortable: 'updatedAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Created',
				property: 'createdAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				sortable: 'createdAt',
				hide: true,
			},
		],
		actions: [
			{
				action: (items) => {
					const fields = createTagsForm(items[0].organizationId)

					openFormDrawer({
						title: 'Update Tag',
						submitLabel: 'Update',
						fields: applyChanges(fields, [
							{
								group: 'Chips Details',
								change: 'hide',
							},
							{
								key: 'programId',
								change: 'readOnly',
							},
							{
								key: 'productId',
								change: 'readOnly',
							},
						]),
						data: clearFormData(
							{
								...items[0],
								labels: items[0].labels || undefined,
								productDetails: items[0].productDetails || undefined,
							},
							fields,
						),
						onSubmit: async (partialEntity) => {
							await UpdateTag({
								variables: {
									partialEntity: {
										...partialEntity,
										_id: items[0]._id,
									},
								},
								refetchQueries: ['Tags'],
							})
						},
					})
				},
				icon: 'radix-icons:pencil-1',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
			},
			{
				action: (items) => {
					window.open(`/tag/${items[0]._id}`)
				},
				icon: 'radix-icons:external-link',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
			},
		],
		onSelection: (items) => {
			selectedItems = items
		},
	})

	const query = () =>
		Tags({
			variables: {
				filter: {
					limit: filter.limit,
					offset: filter.page * filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
					where: {
						...(filter.search
							? {
									$or: [
										{ _Id: filter.search },
										{ organizationId: filter.search },
										{ userId: filter.search },
										{ programId: filter.search },
										{ productId: filter.search },
										{ tags: { $regex: filter.search, $options: 'smix' } },
										{ 'chip.serialNumber': { $regex: filter.search, $options: 'smix' } },
										{ 'chip.type': { $regex: filter.search, $options: 'smix' } },
									],
								}
							: {}),
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
						...(registered?.length === 1 ? { userId: { $exists: registered.includes('registered') } } : {}),
					},
				},
			},
		})
	const result = query()

	$effect(() => {
		$result.query.refetch({
			filter: {
				limit: filter.limit,
				offset: filter.page * filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				where: {
					...(filter.search
						? {
								$or: [
									{ _Id: filter.search },
									{ organizationId: filter.search },
									{ userId: filter.search },
									{ programId: filter.search },
									{ productId: filter.search },
									{ tags: { $regex: filter.search, $options: 'smix' } },
									{ 'chip.serialNumber': { $regex: filter.search, $options: 'smix' } },
									{ 'chip.type': { $regex: filter.search, $options: 'smix' } },
								],
							}
						: {}),
					...((page.params.orgId ?? page.data.organizationId)
						? { organizationId: page.params.orgId ?? page.data.organizationId }
						: {}),
					...(registered?.length === 1 ? { userId: { $exists: registered.includes('registered') } } : {}),
				},
			},
		})
	})

	const data = $derived({
		rows: $result.data?.tags?.nodes || [],
		total: $result.data?.tags?.totalCount || 0,
	})

	function parseCSV(text: string): CreateChip[] {
		const schema = inferSchema(text, { trim: true })
		const parser = initParser(schema)

		const chips = parser.typedDeep<CreateEm4425Chip | CreateNxpChip>(text)

		for (const chip of chips) {
			if (!chip?.type) {
				throw new Error('Chip Type is required')
			}

			if (!(chip as CreateEm4425Chip).serialNumber && !(chip as CreateNxpChip).serialNumber) {
				throw new Error('Required fields are missing')
			}
		}

		return chips.map((chip) => {
			if (Object.values(NxpChipType).includes(chip.type as unknown as NxpChipType)) {
				return {
					nxp: Object.fromEntries(Object.entries(chip).filter(([_, v]) => v != null)) as CreateNxpChip,
				}
			}

			return {
				em: Object.fromEntries(Object.entries(chip).filter(([_, v]) => v != null)) as CreateEm4425Chip,
			}
		})
	}

	async function csvToChips(csv: string | FileList) {
		const chips: CreateChip[] = []
		if (typeof csv !== 'string') {
			for (const file of csv as FileList) {
				await new Promise<void>((resolve, reject) => {
					const reader = new FileReader()
					reader.onload = async (e) => {
						if (e.target?.result && typeof e.target.result === 'string') {
							chips.push(...parseCSV(e.target.result))
							resolve()
						}
					}
					reader.onerror = (error) => {
						reject(error)
					}
					reader.readAsText(file)
				})
			}
		} else {
			chips.push(...parseCSV(csv))
		}

		return chips
	}

	function create() {
		const fields = createTagsForm(page?.params.orgId || page.data.organizationId)

		openFormDrawer({
			title: 'Create Tag',
			submitLabel: 'Create',
			fields,
			data: {
				organizationId: page.params.orgId ?? page.data.organizationId,
			},
			onSubmit: async (data) => {
				try {
					const chips = await csvToChips(data.csv)

					const result = await CreateTags({
						variables: {
							data: {
								organizationId: data.organizationId,
								chips,
								...(data.labels && { labels: data.labels }),
								...(data.programId && { programId: data.programId }),
								...(data.productId && {
									productId: data.productId,
									...(data.productDetails && { productDetails: data.productDetails }),
								}),
							},
						},
						refetchQueries: ['Tags'],
					})

					if (result.data) {
						toaster.info({
							description: `${result.data.createTags} tag${result.data.createTags > 1 ? 's' : ''} created`,
						})
					}
				} catch (error) {
					toaster.error({
						title: (error as Error).name,
						description: (error as Error).message,
					})
				}
			},
		})
	}

	const multipleSelectActions: Array<{ label: string; action: () => void; variant?: string; disabled?: boolean }> = [
		{
			label: 'Attach Product',
			action: () =>
				attachProduct(page.params.orgId ?? page.data.organizationId ?? selectedItems[0]?.organizationId, selectedItems),
			variant: 'secondary',
		},
		{
			label: 'Detach Product',
			action: () => detachProduct(selectedItems),
			variant: 'warning',
		},
		{
			label: 'Attach Program',
			action: () =>
				attachProgram(page.params.orgId ?? page.data.organizationId ?? selectedItems[0]?.organizationId, selectedItems),
			variant: 'success',
		},
		{
			label: 'Detach Program',
			action: () => detachProgram(selectedItems),
			variant: 'error',
		},
	]

	const multiActions = $derived.by(() =>
		selectedItems.length > 0 && [...new Set(selectedItems.map((action) => action.organizationId))].length === 1
			? multipleSelectActions.filter((action) => {
					switch (action.label) {
						case 'Attach Product':
							return selectedItems.every((tag) => !tag.productId)
						case 'Detach Product':
							return selectedItems.every((tag) => tag.productId)
						case 'Attach Program':
							return selectedItems.every((tag) => !tag.programId)
						case 'Detach Program':
							return selectedItems.every((tag) => tag.programId)
						default:
							return false
					}
				})
			: [
					{
						label: 'No common actions',
						action: () => {
							return
						},
						disabled: true,
					},
				],
	)
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}>
	{#snippet filterActions()}
		<button class="btn preset-filled-secondary-500 w-fit" onclick={create} disabled={!auth.isSystemAdmin}
			>Create New</button
		>
	{/snippet}

	{#snippet filterExtra()}
		<div class="flex w-full flex-wrap items-center justify-between gap-2">
			<ToggleGroup value={registered} onValueChange={(e) => (registered = e.value)} multiple>
				<ToggleGroup.Item value="registered">Registered</ToggleGroup.Item>
				<ToggleGroup.Item value="unregistered">Not Registered</ToggleGroup.Item>
			</ToggleGroup>

			<div class="flex flex-wrap items-center justify-end gap-2">
				{#if selectedItems.length > 0}
					<span class="text-secondary-500 text-sm">
						{selectedItems.length} item{selectedItems.length > 1 ? 's' : ''} selected
					</span>

					{#each multiActions as action (action)}
						<button
							class="btn preset-ghost{action.variant ? '-' + action.variant : ''} btn-base w-fit"
							onclick={action.action}
							disabled={action.disabled}
							style="margin-left: 0.5rem;"
						>
							{action.label}
						</button>
					{/each}
				{/if}
			</div>
		</div>
	{/snippet}
</DataTable>
