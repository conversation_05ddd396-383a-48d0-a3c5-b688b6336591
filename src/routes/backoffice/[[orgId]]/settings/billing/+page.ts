import { redirect, error } from '@sveltejs/kit'

import { AsyncCustomer } from '$lib/graphql/generated/gateway'

import type { PageLoad } from './$types'

export const load: PageLoad = async ({ parent, params }) => {
	const parentData = await parent()

	if (!params.orgId) {
		redirect(303, '/unauthorized')
	}

	const { data, loading } = await AsyncCustomer({
		variables: {
			organizationId: params.orgId,
		},
		context: {
			session: parentData.session,
		},
	})

	if (!data?.customer) {
		throw error(404, 'Customer not found')
	}

	return {
		customer: data.customer,
		loading,
		meta: {
			title: `Organization · ${data.customer.name}`,
		},
	}
}
