<script lang="ts">
	import { Tabs } from '@skeletonlabs/skeleton-svelte'

	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import type { Snippet } from 'svelte'

	const { children }: { children?: Snippet } = $props()
</script>

<Tabs value={page.url.pathname} onValueChange={(e) => goto(e.value)} fluid classes="mt-2">
	{#snippet list()}
		<Tabs.Control
			value="/backoffice{(page.params.orgId ?? page.data.organizationId)
				? '/' + (page.params.orgId ?? page.data.organizationId)
				: ''}/settings">Users</Tabs.Control
		>
		<Tabs.Control
			value="/backoffice{(page.params.orgId ?? page.data.organizationId)
				? '/' + (page.params.orgId ?? page.data.organizationId)
				: ''}/settings/applications">Applications</Tabs.Control
		>
		<Tabs.Control
			value="/backoffice{(page.params.orgId ?? page.data.organizationId)
				? '/' + (page.params.orgId ?? page.data.organizationId)
				: ''}/settings/billing">Billing</Tabs.Control
		>
		<Tabs.Control
			value="/backoffice{(page.params.orgId ?? page.data.organizationId)
				? '/' + (page.params.orgId ?? page.data.organizationId)
				: ''}/settings/template">Template</Tabs.Control
		>
		<Tabs.Control
			value="/backoffice{(page.params.orgId ?? page.data.organizationId)
				? '/' + (page.params.orgId ?? page.data.organizationId)
				: ''}/settings/settings">Settings</Tabs.Control
		>
		<Tabs.Control
			value="/backoffice{(page.params.orgId ?? page.data.organizationId)
				? '/' + (page.params.orgId ?? page.data.organizationId)
				: ''}/settings/webhooks">Webhooks</Tabs.Control
		>
	{/snippet}

	{#snippet content()}
		{@render children?.()}
	{/snippet}
</Tabs>
