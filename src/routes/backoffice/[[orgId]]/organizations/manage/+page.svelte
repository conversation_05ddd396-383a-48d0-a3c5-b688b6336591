<script lang="ts">
	import { MediaQuery } from 'svelte/reactivity'

	import { createOrganizationForm } from '$lib/forms'
	import {
		OrganizationsTable,
		CreateOrganization,
		UpdateOrganization,
		type OrganizationsTableQuery,
	} from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { openFormDrawer } from '$lib/modules/Form'
	import { clearFormData } from '$lib/modules/Form'

	import { getAuth } from '$lib/services/auth.svelte'

	import type { QueryReturnType } from '$lib/utils'

	type OrganizationResult = QueryReturnType<OrganizationsTableQuery['organizations']>

	const matches = new MediaQuery('max-width: 1024px')
	const auth = getAuth()

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	const config = $derived<DataTableConfig<OrganizationResult>>({
		columns: [
			{
				type: ColumnType.Image,
				label: 'Logo',
				property: 'logo',
				sortable: '_id',
				href: (row) => `/backoffice/organizations/${row._id}`,
			},
			{
				type: ColumnType.String,
				label: 'Name',
				property: 'name',
				sortable: 'name',
				href: (row) => `/backoffice/organizations/${row._id}`,
			},
			{
				type: ColumnType.ChipList,
				label: 'Verifications',
				chips: [
					{
						label: 'Billing Email',
						property: 'billingVerified',
					},
					{
						label: 'Contact Email',
						property: 'contactEmailVerified',
					},
					{
						label: 'Contact Phone',
						property: 'contactPhoneNumberVerified',
					},
				],
				color: {
					true: 'success',
					false: 'error',
					undefined: 'tertiary',
				},
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Updated',
				property: 'updatedAt',
				sortable: 'updatedAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Created',
				property: 'createdAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				sortable: 'createdAt',
				hide: true,
			},
		],
		actions: [
			{
				action: (items) => {
					if (!items[0]) return

					const fields = createOrganizationForm()

					openFormDrawer({
						title: 'Update Organization',
						submitLabel: 'Update',
						fields,
						data: clearFormData(items[0], fields),
						onSubmit: async (partialEntity) => {
							await UpdateOrganization({
								variables: {
									partialEntity: {
										...partialEntity,
										_id: items[0]?._id,
									},
								},
								refetchQueries: ['OrganizationsTable'],
							})
						},
					})
				},
				icon: 'radix-icons:pencil-1',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
			},
		],
	})

	const query = () =>
		OrganizationsTable({
			variables: {
				filter: {
					limit: filter.limit,
					offset: filter.page * filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
					where: filter.search
						? {
								$or: [
									{ _id: filter.search },
									{ name: { $regex: filter.search, $options: 'smix' } },
									{ website: { $regex: filter.search, $options: 'smix' } },
									{ tag: { $regex: filter.search, $options: 'smix' } },
								],
							}
						: {},
				},
			},
		})

	const result = query()

	$effect(() => {
		$result.query.refetch({
			filter: {
				limit: filter.limit,
				offset: filter.page * filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				where: filter.search
					? {
							$or: [
								{ _id: filter.search },
								{ name: { $regex: filter.search, $options: 'smix' } },
								{ website: { $regex: filter.search, $options: 'smix' } },
								{ tag: { $regex: filter.search, $options: 'smix' } },
							],
						}
					: {},
			},
		})
	})

	const data = $derived({
		rows: $result.data?.organizations?.nodes ?? [],
		total: $result.data?.organizations?.totalCount ?? 0,
	})

	function create() {
		openFormDrawer({
			title: 'Create Organization',
			submitLabel: 'Create',
			fields: createOrganizationForm(),
			onSubmit: async (partialEntity) => {
				await CreateOrganization({
					variables: {
						partialEntity,
					},
					refetchQueries: ['OrganizationsTable'], // This correctly triggers a refetch after creation
				})
			},
		})
	}
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}>
	{#snippet filterActions()}
		<button class="preset-filled-secondary-500 btn btn-base w-fit" onclick={create} disabled={!auth.isSystemAdmin}
			>Create New</button
		>
	{/snippet}
</DataTable>
