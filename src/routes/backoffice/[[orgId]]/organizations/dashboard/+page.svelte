<script lang="ts">
	import {
		AsyncOrganizationStatsAverageNewPerWeekDay,
		AsyncOrganizationStatsMaxNewPerWeekDay,
		AsyncOrganizationStatsMinNewPerWeekDay,
		AsyncOrganizationStatsNewPerDay,
		AsyncOrganizationStatsNewPerMonth,
		AsyncOrganizationStatsNewPerWeek,
		AsyncOrganizationStatsPerDay,
		AsyncOrganizationStatsPerMonth,
		AsyncOrganizationStatsPerWeek,
		AsyncOrganizationStatsTotal,
		type StatsInput,
	} from '$lib/graphql/generated/gateway'
	import Dashboard, {
		CardType,
		ChartType,
		type CardNumberData,
		type ChartCardData,
		type DashboardCard,
		type DashboardOptions,
		type ScopeOptions,
	} from '$lib/modules/Dashboard'
	import {
		fillMissingData,
		scopeResolution,
		scopeToPreviousScopeQuery,
		scopeToScopeQuery,
		translateWeekDays,
	} from '$lib/utils/entity-stats'

	const config: DashboardOptions = {
		scope: {
			default: 'currentMonth',
		},
		showToolbox: false,
		showScopeSelect: true,
	}

	let scope = $state<ScopeOptions>(config.scope?.default ?? 'currentMonth')

	const total = AsyncOrganizationStatsTotal({}).then(({ data }) => data?.organizationStats.total)

	const statsQueryMap = (query: StatsInput) => ({
		day: AsyncOrganizationStatsPerDay({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.perDay),
		week: AsyncOrganizationStatsPerMonth({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.perMonth),
		month: AsyncOrganizationStatsPerWeek({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.perWeek),
	})
	const statsNewQueryMap = (query: StatsInput) => ({
		day: AsyncOrganizationStatsNewPerDay({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.newPerDay),
		week: AsyncOrganizationStatsNewPerMonth({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.newPerMonth),
		month: AsyncOrganizationStatsNewPerWeek({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.newPerWeek),
	})

	const perWeekDayQueryMap = (query: StatsInput) => ({
		day: AsyncOrganizationStatsAverageNewPerWeekDay({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.averageNewPerWeekDay),
		week: AsyncOrganizationStatsMinNewPerWeekDay({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.minNewPerWeekDay),
		month: AsyncOrganizationStatsMaxNewPerWeekDay({
			variables: {
				scope: query,
			},
		}).then(({ data }) => data?.organizationStats.maxNewPerWeekDay),
	})

	const totalGetter = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([statsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)], total]).then(
				async ([currentScope, total]) => {
					const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0

					isLoading = false

					return {
						value: total,
						variation: (currentSum / (total || 1)) * 100,
					}
				},
			),
		}
	})

	const newGetter = $derived.by<CardNumberData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
				statsNewQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)],
				statsQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)],
			]).then(async ([currentScope, previousScope, totalOrganizations]) => {
				const currentSum = currentScope?.y?.reduce((acc, val) => acc + val, 0) || 0
				const previousSum = previousScope?.y?.reduce((acc, val) => acc + val, 0) || 0

				isLoading = false

				return {
					value: currentSum,
					variation: (currentSum - previousSum) / previousSum,
					// objective: 4,
					chart: {
						xAxis: {
							type: 'category',
							data: totalOrganizations?.x || [],
						},
						series: [
							{
								type: ChartType.LINE,
								name: 'Total',
								data: totalOrganizations?.y || [],
							},
						],
					},
				}
			}),
		}
	})

	const perDay = $derived.by<ChartCardData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: Promise.all([
				statsNewQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then((result) =>
					fillMissingData(result, scope, scopeToScopeQuery(scope)),
				),
				statsQueryMap(scopeToPreviousScopeQuery(scope))[scopeResolution(scope)].then((result) =>
					fillMissingData(result, scope, scopeToScopeQuery(scope)),
				),
			]).then(async ([newOrganizations, totalOrganizations]) => {
				isLoading = false

				return {
					xAxis: {
						type: 'category',
						data: newOrganizations.x || [],
					},
					series: [
						{
							type: ChartType.BAR,
							name: 'New',
							data: newOrganizations.y || [],
						},
						{
							type: ChartType.LINE,
							name: 'Total',
							data: totalOrganizations.y || [],
						},
					],
				}
			}),
		}
	})

	const newPerDay = $derived.by<ChartCardData>(() => {
		let isLoading = $state(true)

		return {
			get isLoading() {
				return isLoading
			},
			data: perWeekDayQueryMap(scopeToScopeQuery(scope))[scopeResolution(scope)].then(async (result) => {
				if (!result?.x?.length) {
					isLoading = false
					return {}
				}

				const data = fillMissingData(result, 'weekDays')

				isLoading = false

				return {
					xAxis: {
						type: 'category',
						name: 'Organizations',
						data: data?.x ? translateWeekDays(data.x) : [],
					},
					series: [
						{
							type: ChartType.BAR,
							name: 'Organizations',
							data: data?.y || [],
						},
					],
				}
			}),
		}
	})

	const cards = $derived<DashboardCard[]>([
		{
			type: CardType.NUMBER,
			header: {
				title: 'Total Organizations',
			},
			options: {},
			class: 'col-50 row-1',
			getter: totalGetter,
		},
		{
			type: CardType.NUMBER,
			header: {
				title: 'New Organizations',
			},
			class: 'col-50 row-1',
			getter: newGetter,
		},
		{
			type: CardType.CHART,
			chartType: ChartType.LINE,
			options: {},
			header: {
				title: 'Organizations per Day',
			},
			class: 'col-100 row-3',
			getter: perDay,
		},
		{
			type: CardType.CHART,
			chartType: ChartType.BAR,
			options: {},
			header: {
				title: 'New Organizations per week day',
			},
			class: 'col-100 row-3',
			getter: newPerDay,
		},
	])
</script>

<Dashboard bind:scope {cards} {config} />
