import { error, redirect } from '@sveltejs/kit'

import { AsyncFullOrganization } from '$lib/graphql/generated/gateway'

import type { PageLoad } from './$types'

export const load: PageLoad = async ({ params, parent, url }) => {
	const parentData = await parent()

	if (!parentData?.session) {
		redirect(303, '/unauthorized?postLoginRedirect=' + encodeURIComponent(url.toString().replace(url.origin, '')))
	}

	const { data, loading } = await AsyncFullOrganization({
		variables: {
			filter: { id: params.id },
		},
		context: {
			session: parentData.session,
		},
	})

	if (!data?.organization) {
		throw error(404, 'Organization not found')
	}

	return {
		organization: data.organization,
		loading,
		meta: {
			title: `Backoffice · Organization · ${data.organization.name}`,
		},
	}
}
