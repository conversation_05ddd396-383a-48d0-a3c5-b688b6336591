<script lang="ts">
	import { Accordion } from '@skeletonlabs/skeleton-svelte'

	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import { createOrganizationForm } from '$lib/forms'
	import {
		UpdateOrganization,
		type FullOrganizationQuery,
	} from '$lib/graphql/generated/gateway'
	import { clearFormData, openFormDrawer } from '$lib/modules/Form'
	import { getLocaleForDateFormat } from '$lib/utils'

	import { clipboard } from '$lib/actions/clipboard.svelte'
	import ApplicationsTable from '$lib/components/Backoffice/ApplicationsTable.svelte'
	import UserRolesTable from '$lib/components/Backoffice/UserRolesTable.svelte'

	let organization: FullOrganizationQuery['organization'] = $state(page.data.organization)

	function gotoManagePage() {
		goto('/backoffice/organizations/manage')
	}


	function openEditDrawer() {
		const fields = createOrganizationForm()

		openFormDrawer({
			title: 'Update Organization',
			submitLabel: 'Update',
			fields,
			data: clearFormData(organization, fields),
			onSubmit: async (partialEntity) => {
				const result = await UpdateOrganization({
					variables: {
						partialEntity: {
							...partialEntity,
							_id: organization._id,
						},
					},
				})

				if (result.data) {
					organization = result.data.updateOrganization
				}
			},
		})
	}

	let main = $state<string[]>([])
	let users = $state<string[]>([])
	let applications = $state<string[]>([])
	let footerLeft = $state<string[]>([])
	let footerRight = $state<string[]>([])
</script>

<div class="container mx-auto p-2 lg:mx-0 lg:p-6">
	<div class="flex items-center justify-between">
		<button type="button" class="preset-tonal btn" onclick={gotoManagePage}>
			<iconify-icon icon="radix-icons:chevron-left" inline></iconify-icon>
			<span>Manage</span>
		</button>

		<button type="button" class="preset-filled-secondary-500 btn" onclick={openEditDrawer}
			><iconify-icon icon="radix-icons:pencil-1" inline></iconify-icon><span>Edit</span></button
		>
	</div>

	<br />

	<div class="flex items-end justify-between space-x-4">
		<h1 class="h2">{organization.name}</h1>

		{#if organization.logo}
			<img src={organization.logo} alt="" class="max-h-32" />
		{/if}
	</div>

	<hr class="mt-4" />

	<p class="mb-4 flex items-center">
		Entity ID: {organization._id}

		<button use:clipboard={organization._id} class="btn-icon" aria-label="Copy Entity ID">
			<iconify-icon icon="radix-icons:copy" inline></iconify-icon>
		</button>
	</p>

	<div class="card preset-outlined-surface-500 p-4">
		<Accordion value={main} onValueChange={(e) => (main = e.value)} multiple collapsible>
			<Accordion.Item value="details">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:info-circled" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<span class="font-bold">Details</span>
				{/snippet}

				{#snippet panel()}
					<div class="table-wrap rounded-xl">
						<table class="table border-separate border-spacing-[1px]">
							<tbody>
								{#if organization.customerId}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Customer Id</td>
										<td class="preset-tonal"
											><span use:clipboard={organization?.customerId || ''}>{organization?.customerId || ''}</span></td
										>
									</tr>
								{/if}
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Tag</td>
									<td class="preset-tonal">{organization.tag || ''}</td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Description</td>
									<td class="preset-tonal"><div class="whitespace-pre-line">{organization.description || ''}</div></td>
								</tr>
								{#if organization.website}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Website</td>
										<td class="preset-tonal"
											><a href={organization.website} target="_blank">{organization.website}</a></td
										>
									</tr>
								{/if}
								{#if organization.customDomains?.length}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Custom Domains</td>
										<td class="preset-tonal flex flex-wrap gap-2">
											{#each organization.customDomains as customDomain (customDomain)}
												<span class="preset-tonal chip">{customDomain}</span>
											{/each}
										</td>
									</tr>
								{/if}
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Created</td>
									<td class="preset-tonal"
										>{organization.createdAt
											? new Date(organization.createdAt).toLocaleString(getLocaleForDateFormat(), {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Updated</td>
									<td class="preset-tonal"
										>{organization.updatedAt
											? new Date(organization.updatedAt).toLocaleString(getLocaleForDateFormat(), {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>

			<Accordion.Item value="contacts">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:id-card" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<span class="font-bold">Contacts</span>
				{/snippet}

				{#snippet panel()}
					<div class="table-wrap rounded-xl">
						<table class="table border-separate border-spacing-[1px]">
							<tbody>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Contact Email</td>
									<td class="preset-tonal"
										><span
											class={organization.contactEmailVerified ? '' : 'text-error-500'}
											use:clipboard={organization.contactEmail}>{organization.contactEmail || ''}</span
										></td
									>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Billing Email</td>
									<td class="preset-tonal"
										><span
											class={organization.billingVerified ? '' : 'text-error-500'}
											use:clipboard={organization.billing}>{organization.billing || ''}</span
										></td
									>
								</tr>
								{#if organization.contactPhoneNumber}
									<tr>
										<td class="bg-surface-100 dark:bg-surface-600 w-1/4 text-right font-medium">Contact Phone Number</td
										>
										<td class="preset-tonal"
											><span
												class={organization.contactPhoneNumberVerified ? '' : 'text-error-500'}
												use:clipboard={organization.contactPhoneNumber}>{organization.contactPhoneNumber || ''}</span
											></td
										>
									</tr>
								{/if}
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>
		</Accordion>
	</div>

	<hr class="mt-4" />

	<Accordion
		classes="card p-2 preset-tonal mt-4"
		value={users}
		onValueChange={(e) => (users = e.value)}
		multiple
		collapsible
	>
		<Accordion.Item value="users" panelPadding="p-0">
			{#snippet lead()}
				<iconify-icon icon="ion:people-outline" inline></iconify-icon>
			{/snippet}

			{#snippet control()}
				<div class="flex w-full items-center justify-between">
					<span class="font-bold">Users</span>
				</div>
			{/snippet}

			{#snippet panel()}
				<UserRolesTable organizationId={organization._id} />
			{/snippet}
		</Accordion.Item>
	</Accordion>

	<Accordion
		classes="card p-2 preset-tonal mt-4"
		value={applications}
		onValueChange={(e) => (applications = e.value)}
		multiple
		collapsible
	>
		<Accordion.Item value="applications" panelPadding="p-0">
			{#snippet lead()}
				<iconify-icon icon="logos:oauth" inline></iconify-icon>
			{/snippet}

			{#snippet control()}
				<div class="flex w-full items-center justify-between">
					<span class="font-bold">Applications</span>
				</div>
			{/snippet}

			{#snippet panel()}
				<ApplicationsTable organizationId={organization._id} />
			{/snippet}
		</Accordion.Item>
	</Accordion>

	<div class="mt-4 columns-1 gap-4 md:columns-2">
		<Accordion
			classes="card p-2 preset-tonal break-inside-avoid-column"
			value={footerLeft}
			onValueChange={(e) => (footerLeft = e.value)}
			multiple
			collapsible
		>
			<Accordion.Item value="notes">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:file-text" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<div class="flex w-full items-center justify-between">
						<span class="font-bold">Notes</span>
					</div>
				{/snippet}

				{#snippet panel()}
					<p class="text-sm">TODO</p>
				{/snippet}
			</Accordion.Item>
		</Accordion>

		<Accordion
			classes="card p-2 preset-tonal break-inside-avoid-column"
			value={footerRight}
			onValueChange={(e) => (footerRight = e.value)}
			multiple
			collapsible
		>
			<Accordion.Item value="activity-stream">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:activity-log" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<div class="flex w-full items-center justify-between">
						<span class="font-bold">Activity Stream</span>
					</div>
				{/snippet}

				{#snippet panel()}
					<p class="text-sm">TODO</p>
				{/snippet}
			</Accordion.Item>
		</Accordion>
	</div>
</div>
