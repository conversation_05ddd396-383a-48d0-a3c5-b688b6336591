<script lang="ts">
	import { Accordion } from '@skeletonlabs/skeleton-svelte'

	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import { createProductForm } from '$lib/forms'
	import { type ProductQuery, UpdateProduct } from '$lib/graphql/generated/gateway'
	import { openFormDrawer, clearFormData } from '$lib/modules/Form'

	import { clipboard } from '$lib/actions/clipboard.svelte'

	let product: ProductQuery['product'] = $derived(page.data.product)

	function gotoManagePage() {
		goto('/backoffice/products/manage')
	}

	function openEditDrawer() {
		const fields = createProductForm(product.organizationId)

		openFormDrawer({
			title: 'Update Product',
			submitLabel: 'Update',
			fields,
			data: clearFormData(product, fields),
			onSubmit: async (partialEntity) => {
				const result = await UpdateProduct({
					variables: {
						partialEntity: {
							...partialEntity,
							_id: product._id,
						},
					},
				})

				if (result.data) {
					product = result.data.updateProduct
				}
			},
		})
	}

	let main = $state<string[]>([])
	let footerLeft = $state<string[]>([])
	let footerRight = $state<string[]>([])
</script>

<div class="container mx-auto p-2 lg:mx-0 lg:p-6">
	<div class="flex items-center justify-between">
		<button type="button" class="preset-tonal btn" onclick={gotoManagePage}>
			<iconify-icon icon="radix-icons:chevron-left" inline></iconify-icon>
			<span>Manage</span>
		</button>

		<button type="button" class="preset-filled-secondary-500 btn" onclick={openEditDrawer}
			><iconify-icon icon="radix-icons:pencil-1" inline></iconify-icon><span>Edit</span></button
		>
	</div>

	<br />

	<div class="flex items-end justify-between space-x-4">
		<h1 class="h2">{product.name}</h1>

		{#if product.picture}
			<img src={product.picture || undefined} alt="" class="max-h-32" />
		{/if}
	</div>

	<hr class="mt-4" />

	<p class="mb-4 flex items-center">
		Entity ID: {product._id}

		<button use:clipboard={product._id} class="btn-icon" aria-label="Copy Entity ID">
			<iconify-icon icon="radix-icons:copy" inline></iconify-icon>
		</button>
	</p>

	<div class="card preset-outlined-surface-500 p-4">
		<Accordion value={main} onValueChange={(e) => (main = e.value)} multiple collapsible>
			<Accordion.Item value="details">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:info-circled" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<span class="font-bold">Details</span>
				{/snippet}

				{#snippet panel()}
					<div class="table-container">
						<table class="table-compact table">
							<tbody>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Description</td>
									<td class="preset-tonal"><div class="whitespace-pre-line">{product.description || ''}</div></td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">External Url</td>
									<td class="preset-tonal"><a href={product.url} target="_blank">{product.url}</a></td>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Created</td>
									<td class="preset-tonal"
										>{product.createdAt
											? new Date(product.createdAt).toLocaleString(undefined, {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
								<tr>
									<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">Updated</td>
									<td class="preset-tonal"
										>{product.updatedAt
											? new Date(product.updatedAt).toLocaleString(undefined, {
													dateStyle: 'full',
													timeStyle: 'long',
												})
											: ''}</td
									>
								</tr>
							</tbody>
						</table>
					</div>
				{/snippet}
			</Accordion.Item>

			{#if !(page.params.orgId ?? page.data.organizationId)}
				<Accordion.Item value="organization">
					{#snippet lead()}
						<iconify-icon icon="radix-icons:backpack" inline></iconify-icon>
					{/snippet}

					{#snippet control()}
						<span class="font-bold">Organization</span>
					{/snippet}

					{#snippet panel()}
						<div class="table-container">
							<table class="table-compact table">
								<tbody>
									{#if product.organization}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium">
												{#if product.organization.logo}
													<a href={`/backoffice/organizations/${product.organization._id}`}>
														<img src={product.organization.logo} alt="" class="inline max-h-6" />
													</a>
												{/if}
											</td>
											<td class="preset-tonal">
												<a href={`/backoffice/organizations/${product.organization._id}`}
													>{product.organization.name || ''}</a
												>
											</td>
										</tr>
									{:else}
										<tr>
											<td class="bg-surface-100 dark:bg-surface-700 w-1/4 text-right font-medium"></td>
											<td class="preset-tonal">
												<span>No organizations available</span>
											</td>
										</tr>
									{/if}
								</tbody>
							</table>
						</div>
					{/snippet}
				</Accordion.Item>
			{/if}
		</Accordion>
	</div>

	<div class="mt-4 columns-1 gap-4 md:columns-2">
		<Accordion
			classes="card p-2 preset-tonal break-inside-avoid-column"
			value={footerLeft}
			onValueChange={(e) => (footerLeft = e.value)}
			multiple
			collapsible
		>
			<Accordion.Item value="notes">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:file-text" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<div class="flex w-full items-center justify-between">
						<span class="font-bold">Notes</span>
					</div>
				{/snippet}

				{#snippet panel()}
					<p class="text-sm">TODO</p>
				{/snippet}
			</Accordion.Item>
		</Accordion>

		<Accordion
			classes="card p-2 preset-tonal break-inside-avoid-column"
			value={footerRight}
			onValueChange={(e) => (footerRight = e.value)}
			multiple
			collapsible
		>
			<Accordion.Item value="activity-stream">
				{#snippet lead()}
					<iconify-icon icon="radix-icons:activity-log" inline></iconify-icon>
				{/snippet}

				{#snippet control()}
					<div class="flex w-full items-center justify-between">
						<span class="font-bold">Activity Stream</span>
					</div>
				{/snippet}

				{#snippet panel()}
					<p class="text-sm">TODO</p>
				{/snippet}
			</Accordion.Item>
		</Accordion>
	</div>
</div>
