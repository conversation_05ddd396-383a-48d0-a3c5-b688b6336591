<script lang="ts">
	import { MediaQuery } from 'svelte/reactivity'

	import { page } from '$app/state'

	import { createProductForm } from '$lib/forms'
	import { Products, CreateProduct, UpdateProduct, type ProductsQuery } from '$lib/graphql/generated/gateway'
	import DataTable, { ColumnType, type DataTableConfig, type DataTableFilter } from '$lib/modules/DataTable'
	import { clearFormData, openFormDrawer } from '$lib/modules/Form'

	import type { QueryReturnType } from '$lib/utils'

	const matches = new MediaQuery('max-width: 1024px')

	let filter = $state<DataTableFilter>({
		page: 0,
		limit: 25,
	})

	type ProductsResult = QueryReturnType<ProductsQuery['products']>

	const config = $derived<DataTableConfig<ProductsResult>>({
		columns: [
			{
				type: ColumnType.Image,
				label: 'Picture',
				property: 'picture',
				sortable: '_id',
				href: (row) => `/backoffice/products/${row._id}`,
			},
			{
				type: ColumnType.String,
				label: 'Name',
				property: 'name',
				sortable: 'name',
				href: (row) => `/backoffice/products/${row._id}`,
			},
			{
				type: ColumnType.Profile,
				label: 'Organization',
				image: 'organization.logo',
				name: 'organization.name',
				href: (row) => row.organizationId && `/backoffice/organizations/${row.organizationId}`,
				hide: !!(page.params.orgId ?? page.data.organizationId),
			},
			{
				type: ColumnType.Date,
				label: 'Updated',
				property: 'updatedAt',
				sortable: 'updatedAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				hide: matches.current,
			},
			{
				type: ColumnType.Date,
				label: 'Created',
				property: 'createdAt',
				options: {
					dateStyle: 'medium',
					timeStyle: 'medium',
				},
				relative: true,
				sortable: 'createdAt',
				hide: matches.current,
			},
		],
		actions: [
			{
				action: (items) => {
					if (items[0].url) {
						window.open(items[0].url, '_blank')
					}
				},
				icon: 'radix-icons:external-link',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
				hide: '!item.url',
			},
			{
				action: (items) => {
					const fields = createProductForm(items[0].organizationId)

					openFormDrawer({
						title: 'Update Product',
						submitLabel: 'Update',
						fields,
						data: clearFormData(items[0], fields),
						onSubmit: async (partialEntity) => {
							await UpdateProduct({
								variables: {
									partialEntity: {
										...partialEntity,
										_id: items[0]._id,
									},
								},
								refetchQueries: ['Products'],
							})
						},
					})
				},
				icon: 'radix-icons:pencil-1',
				classes: 'text-secondary-500 hover:preset-filled-secondary-500',
			},
		],
	})

	const query = () =>
		Products({
			variables: {
				filter: {
					limit: filter.limit,
					offset: filter.page * filter.limit,
					...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
					where: {
						...(filter.search
							? {
									$or: [
										{ _Id: filter.search },
										{ name: { $regex: filter.search, $options: 'smix' } },
										{ organizationId: filter.search },
									],
								}
							: {}),
						...((page.params.orgId ?? page.data.organizationId)
							? { organizationId: page.params.orgId ?? page.data.organizationId }
							: {}),
					},
				},
			},
		})
	const result = query()

	$effect(() => {
		$result.query.refetch({
			filter: {
				limit: filter.limit,
				offset: filter.page * filter.limit,
				...(filter?.sort ? { sort: { [filter.sort.key]: filter.sort.direction } } : {}),
				where: {
					...(filter.search
						? {
								$or: [
									{ _Id: filter.search },
									{ name: { $regex: filter.search, $options: 'smix' } },
									{ organizationId: filter.search },
								],
							}
						: {}),
					...((page.params.orgId ?? page.data.organizationId)
						? { organizationId: page.params.orgId ?? page.data.organizationId }
						: {}),
				},
			},
		})
	})

	const data = $derived({
		rows: $result.data?.products?.nodes || [],
		total: $result.data?.products?.totalCount || 0,
	})

	function create() {
		const fields = createProductForm(page?.params.orgId || page.data.organizationId)

		openFormDrawer({
			title: 'Create Product',
			submitLabel: 'Create',
			fields,
			data: {
				organizationId: page.params.orgId ?? page.data.organizationId,
			},
			onSubmit: async (partialEntity) => {
				await CreateProduct({
					variables: {
						partialEntity,
					},
					refetchQueries: ['Products'],
				})
			},
		})
	}
</script>

<DataTable bind:filter {config} {data} isLoading={$result.loading}>
	{#snippet filterActions()}
		<button class="btn preset-filled-secondary-500 w-fit" onclick={create}>Create New</button>
	{/snippet}
</DataTable>
