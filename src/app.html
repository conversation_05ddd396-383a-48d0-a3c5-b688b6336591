<!doctype html>
<html lang="en" data-theme="custom-theme">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width" />
		%sveltekit.head%

		<script>
			const condLocalStorageUser = localStorage.getItem('modeUserPrefers') === 'false'
			const condLocalStorageUserExists = !('modeUserPrefers' in localStorage)
			const condMatchMedia = window.matchMedia('(prefers-color-scheme: dark)').matches

			if (condLocalStorageUser || (!condLocalStorageUserExists && condMatchMedia)) {
				document.documentElement.setAttribute('data-mode', 'dark')
			} else {
				document.documentElement.setAttribute('data-mode', 'light')
			}
		</script>
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
