env:
  common:
    PORT: "80"
    DEFAULT_CACHE_HINT: "300"
    CACHE_TTL: "5"
    REDIS_PASSWORD: EIqqycRwUb
    # S3_KEY: N7d/zxKYU9kmKB6X5WcqjDwEOExKJzAYF1rXpowf
    # S3_KEY_ID: ********************
    # AWS_SECRET_ACCESS_KEY: N7d/zxKYU9kmKB6X5WcqjDwEOExKJzAYF1rXpowf
    # AWS_ACCESS_KEY_ID: ********************
    # ELASTIC_APM_SERVER_URL: "http://cluster-apm-server.default.svc.cluster.local:8200"
    # ELASTIC_APM_ASYNC_HOOKS: "true"
    FIREBASE_CREDENTIAL: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    FIREBASE_VAPID: BOwSRIgSkWyUbWUNKh9h07p-vbSlZuGV8K3Vj5r5L-Gf235A7aVJdhO84no6ObjfhmyWcfcdlpg0_kfZuvTwE6g
    TWILIO_ACCOUNT_SID: **********************************
    TWILIO_AUTH_TOKEN: 34723bff3f747c785cbc9446abad7acb
    RC_FEATURES: audit-events,comments,issues,labels,ratings,reactions,reviews,social,stats,webhooks

  gateway:
    GLOBAL_PREFIX: gateway
    PUBLIC: "true"
    APOLLO_KEY: service:DapTap:gibvzWTa73rF3WHq0-smtQ
    APOLLO_SCHEMA_REPORTING: "false"

  oidc:
    GLOBAL_PREFIX: oidc
    PUBLIC: "true"
    # ACCOUNTS_REQUIRE_INVITATION: "true"
    AUTH_PROVIDERS: email
    OIDC_USER_REQUIRED_FIELDS: "givenName:firstName,familyName:lastName"

  api:
    GLOBAL_PREFIX: api
    PUBLIC: "true"
    # ACCOUNTS_REQUIRE_INVITATION: "true"

  subscriptions:
    GLOBAL_PREFIX: subscriptions
    PUBLIC: "true"

  services-core:
    GLOBAL_PREFIX: services/core

  services-ecommerce:
    GLOBAL_PREFIX: services/ecommerce

  services-tags:
    GLOBAL_PREFIX: services/tags

  workers-notifications:
    GLOBAL_PREFIX: workers/notifications

  workers-webhooks:
    GLOBAL_PREFIX: workers/webhooks

  workers-cleaner:
    GLOBAL_PREFIX: workers/cleaner

  temp:
    common:
      MONGODB_URI: mongodb+srv://temp:<EMAIL>/daptap-temp?retryWrites=true&w=majority
      MONGODB_DATABASE: daptap-temp
      REDIS_HOSTNAME: redis-master.redis.svc.cluster.local
      REDIS_NAMESPACE: daptap_temp
      MAILGUN_API_KEY: **************************************************
      MAILGUN_DOMAIN: daptapgo.io
      OIDC_ISSUER: https://daptap-oidc-temp.daptapgo.io
      OIDC_CLIENT_ID: api
      OIDC_CLIENT_SECRET: qQwTUbax5lmGW94zk80zWGsUK7QrSAaczHUyrq62PK4VsdtZHtZiAzcqIjFoHPh3Hq5c9b5VRMmIcOi8VQVaoA
      OIDC_DEFAULT_SECRET: j8KtLmNpOqRrSsTtUuVvWwXxYyZz0123456789
      MAGIC_LINK_SECRET: c46tbctxn2k1n4cx4^£Qr213tc42tv122xv36
      ISSUER: https://daptap-oidc-temp.daptapgo.io
      FRONTEND_URL: https://temp.daptapgo.io
      STRIPE_API_KEY: sk_test_1zis8ryI2mxqvPG8pS2Fu2yW

    gateway:
      APOLLO_GRAPH_REF: DapTap@temp

    oidc:

    api:

    subscriptions:

    services-core:

    services-notifications:

    services-ecommerce:

    services-tags:

    workers-notifications:

    workers-webhooks:

    workers-cleaner:

  develop:
    common:
      MONGODB_URI: mongodb+srv://develop:<EMAIL>/daptap-develop?retryWrites=true&w=majority
      MONGODB_DATABASE: daptap-develop
      REDIS_HOSTNAME: redis-master.redis.svc.cluster.local
      REDIS_NAMESPACE: daptap_develop
      MAILGUN_API_KEY: **************************************************
      MAILGUN_DOMAIN: daptapgo.io
      OIDC_ISSUER: https://daptap-oidc-develop.daptapgo.io
      OIDC_CLIENT_ID: api
      OIDC_CLIENT_SECRET: qQwTUbax5lmGW94zk80zWGsUK7QrSAaczHUyrq62PK4VsdtZHtZiAzcqIjFoHPh3Hq5c9b5VRMmIcOi8VQVaoA
      OIDC_DEFAULT_SECRET: j8KtLmNpOqRrSsTtUuVvWwXxYyZz0123456789
      MAGIC_LINK_SECRET: c46tbctxn2k1n4cx4^£Qr213tc42tv122xv36
      ISSUER: https://daptap-oidc-develop.daptapgo.io
      FRONTEND_URL: https://develop.daptapgo.io
      STRIPE_API_KEY: sk_test_1zis8ryI2mxqvPG8pS2Fu2yW

    gateway:
      APOLLO_GRAPH_REF: DapTap@develop

    oidc:

    api:

    subscriptions:

    services-core:

    services-notifications:

    services-ecommerce:

    services-tags:

    workers-notifications:

    workers-webhooks:

    workers-cleaner:

  stage:
    common:
      MONGODB_URI: mongodb+srv://stage:<EMAIL>/daptap-stage?retryWrites=true&w=majority
      MONGODB_DATABASE: daptap-stage
      REDIS_HOSTNAME: redis-master.redis.svc.cluster.local
      REDIS_NAMESPACE: daptap_stage
      MAILGUN_API_KEY: **************************************************
      MAILGUN_DOMAIN: daptapgo.io
      OIDC_ISSUER: https:/daptap-oidc-stage.daptapgo.io
      OIDC_CLIENT_ID: api
      OIDC_CLIENT_SECRET: qQwTUbax5lmGW94zk80zWGsUK7QrSAaczHUyrq62PK4VsdtZHtZiAzcqIjFoHPh3Hq5c9b5VRMmIcOi8VQVaoA
      OIDC_DEFAULT_SECRET: j8KtLmNpOqRrSsTtUuVvWwXxYyZz0123456789
      MAGIC_LINK_SECRET: c46tbctxn2k1n4cx4^£Qr213tc42tv122xv36
      ISSUER: https://daptap-oidc-stage.daptapgo.io
      FRONTEND_URL: https://stage.daptapgo.io
      STRIPE_API_KEY: sk_test_1zis8ryI2mxqvPG8pS2Fu2yW

    gateway:
      APOLLO_GRAPH_REF: DapTap@stage

    oidc:

    api:

    subscriptions:

    services-core:

    services-notifications:

    services-ecommerce:

    services-tags:

    workers-notifications:

    workers-webhooks:

    workers-cleaner:

  production:
    common:
      MONGODB_URI: mongodb+srv://production:<EMAIL>/daptap?retryWrites=true&w=majority
      MONGODB_DATABASE: daptap
      REDIS_HOSTNAME: redis-master.redis.svc.cluster.local
      REDIS_NAMESPACE: daptap
      MAILGUN_API_KEY: **************************************************
      MAILGUN_DOMAIN: daptapgo.io
      OIDC_ISSUER: https://oidc.daptapgo.io
      OIDC_CLIENT_ID: api
      OIDC_CLIENT_SECRET: qQwTUbax5lmGW94zk80zWGsUK7QrSAaczHUyrq62PK4VsdtZHtZiAzcqIjFoHPh3Hq5c9b5VRMmIcOi8VQVaoA
      OIDC_DEFAULT_SECRET: j8KtLmNpOqRrSsTtUuVvWwXxYyZz0123456789
      MAGIC_LINK_SECRET: c46tbctxn2k1n4cx4^£Qr213tc42tv122xv36
      ISSUER: https://oidc.daptapgo.io
      FRONTEND_URL: https://daptapgo.io
      STRIPE_API_KEY: sk_test_1zis8ryI2mxqvPG8pS2Fu2yW

    gateway:
      APOLLO_GRAPH_REF: DapTap@current

    oidc:

    api:

    subscriptions:

    services-core:

    services-notifications:

    services-ecommerce:

    services-tags:

    workers-notifications:

    workers-webhooks:

    workers-cleaner:
