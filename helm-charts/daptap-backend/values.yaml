project: daptap
name: daptap-backend
environment: develop
stage: develop
domain: daptapgo.io
docker_repository: phigital-loyalty

apps:
  gateway:
    type: server
    includeIn: ["temp", "develop", "stage", "main"]
    public: true
    develop:
      replicaCount: 1
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi
    production:
      replicaCount: 2
      # autoscale: true
      # maxReplicas: 5
      extraDomains:
        - gateway.daptapgo.io
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi

  oidc:
    type: server
    includeIn: ["temp", "develop", "stage", "main"]
    public: true
    develop:
      replicaCount: 1
      resources:
        limits:
          cpu: 0.3
          memory: 512Mi
        requests:
          cpu: 30m
          memory: 128Mi
    production:
      replicaCount: 2
      # autoscale: true
      # maxReplicas: 5
      extraDomains:
        - oidc.daptapgo.io
      resources:
        limits:
          cpu: 0.3
          memory: 512Mi
        requests:
          cpu: 30m
          memory: 128Mi

  api:
    type: server
    includeIn: ["temp", "develop", "stage", "main"]
    public: true
    develop:
      replicaCount: 1
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi
    production:
      replicaCount: 2
      # autoscale: true
      # maxReplicas: 5
      extraDomains:
        - api.daptapgo.io
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi

  subscriptions:
    type: server
    includeIn: ["temp", "develop", "stage", "main"]
    public: true
    develop:
      replicaCount: 1
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi
    production:
      replicaCount: 2
      # autoscale: true
      # maxReplicas: 5
      extraDomains:
        - subscriptions.daptapgo.io
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi

  services-core:
    type: server
    includeIn: ["temp", "develop", "stage", "main"]
    public: false
    develop:
      replicaCount: 1
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi
    production:
      replicaCount: 2
      # autoscale: true
      # maxReplicas: 5
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi

  services-ecommerce:
    type: server
    includeIn: ["temp", "develop", "stage", "main"]
    public: false
    develop:
      replicaCount: 1
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi
    production:
      replicaCount: 2
      # autoscale: true
      # maxReplicas: 5
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi

  services-tags:
    type: server
    includeIn: ["temp", "develop", "stage", "main"]
    public: false
    develop:
      replicaCount: 1
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi
    production:
      replicaCount: 2
      # autoscale: true
      # maxReplicas: 5
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi

  workers-notifications:
    type: server
    includeIn: ["temp", "develop", "stage", "main"]
    public: false
    develop:
      replicaCount: 1
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi
    production:
      replicaCount: 1
      # autoscale: true
      # maxReplicas: 5
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi

  workers-webhooks:
    type: server
    includeIn: ["temp", "develop", "stage", "main"]
    public: false
    develop:
      replicaCount: 1
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi
    production:
      replicaCount: 1
      # autoscale: true
      # maxReplicas: 5
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi

  workers-cleaner:
    type: server
    includeIn: ["temp", "develop", "stage", "main"]
    public: false
    develop:
      replicaCount: 1
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi
    production:
      replicaCount: 1
      # autoscale: true
      # maxReplicas: 5
      resources:
        # limits:
        #   cpu: 150m
        #   memory: 256Mi
        requests:
          cpu: 30m
          memory: 128Mi
