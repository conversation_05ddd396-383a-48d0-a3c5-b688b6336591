CHART_REPO := ${shell npx yaml get ./cluster.yaml chart_repo}
CHART_REPO_USER := ${shell npx yaml get ./cluster.yaml chart_repo_user}
CHART_REPO_PASS := ${shell npx yaml get ./cluster.yaml chart_repo_pass}
APP_NAME := ${shell npx yaml get ./cluster.yaml name}
BRANCH ?= temp
# VERSION_TAG := ${shell cat ../../.tags/VERSION_TAG}
ifdef VERSION_TAG
	HELM_VERSION=--version ${VERSION_TAG}
endif
ifndef VERSION_TAG
	HELM_VERSION=
endif
ifeq (${BRANCH}, main)
	FULL_APP_NAME=${APP_NAME}
else
	FULL_APP_NAME=${APP_NAME}-${BRANCH}
endif

build: check-env
	cp -R . ../${FULL_APP_NAME}
	cd ../${FULL_APP_NAME}
	npx merge-yaml-cli -i cluster.yaml variables.yaml values.yaml -o values.yaml
	echo "$(npx yaml set Chart.yaml name ${FULL_APP_NAME})" > Chart.yaml
	echo "$(npx yaml set Chart.yaml version ${VERSION_TAG})" > Chart.yaml
	echo "$(npx yaml set Chart.yaml appVersion ${VERSION_TAG})" > Chart.yaml
	echo "$(npx yaml set values.yaml version_tag ${VERSION_TAG})" > values.yaml
	echo "$(npx yaml set values.yaml environment ${BRANCH})" > values.yaml
	echo "$(npx yaml set values.yaml stage ${BRANCH})" > values.yaml
	rm -rf charts
	rm -rf ${APP_NAME}*.tgz
	rm -rf requirements.lock
	helm init --client-only
	helm dependency build
	helm lint .
	helm package .

install:
	helm install --repo ${CHART_REPO} --username ${CHART_REPO_USER} --password ${CHART_REPO_PASS} ${HELM_VERSION} --namespace ${BRANCH} ${FULL_APP_NAME} ${APP_NAME}/${FULL_APP_NAME}

upgrade:
	helm upgrade --install --repo ${CHART_REPO} --username ${CHART_REPO_USER} --password ${CHART_REPO_PASS} ${HELM_VERSION} --namespace ${BRANCH} ${FULL_APP_NAME} ${APP_NAME}/${FULL_APP_NAME}

delete:
	helm delete --purge ${FULL_APP_NAME}

clean:
	cd ..
	rm -rf ${APP_NAME}-${BRANCH}
	cd deploy

release: build
	curl --fail -u ${CHART_REPO_USER}:${CHART_REPO_PASS} --data-binary "@${FULL_APP_NAME}-${VERSION_TAG}.tgz" ${CHART_REPO}/api/charts
	make clean

tag:
	# git add --all
	# git commit -m "release ${VERSION_TAG}" --allow-empty # if first release then no verion update is performed
	# git tag -fa v${VERSION_TAG} -m "Release version ${VERSION_TAG}"
	# git push origin v${VERSION_TAG}

check-env:
ifndef VERSION_TAG
	$(error VERSION_TAG is undefined)
endif

lint:
	helm lint -f datagatherers-backend/variables.yaml -f datagatherers-backend/values.yaml -f values.yaml .
