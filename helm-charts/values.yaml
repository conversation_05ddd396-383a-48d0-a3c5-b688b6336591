# Default values for node projects.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1
version_tag: develop
image:
  repository: draft
  # pullPolicy: Always
  pullPolicy: IfNotPresent
service:
  type: ClusterIP
  externalPort: 80
  internalPort: 80
  annotations:
    prometheus.io/scrape: "true"
    heartbeat.overridePath: "/health"
ingress:
  enabled: "true"
  tls: "true"
  # annotations:
  #   kubernetes.io/ingress.class: nginx
  #   certmanager.k8s.io/acme-challenge-type: dns01
probePath: /health
probeHeaders:
  httpHeaders:
    - name: Authorization
      value: 'Basic ZnVsbGN1YmU6c3VtbWVyLWF1dHVtbi1tb25zb29u'
livenessProbe:
  initialDelaySeconds: 240
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1
readinessProbe:
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1
terminationGracePeriodSeconds: 10
# dockerconfigjson:

route53ManagedDomains: []

cloudflareManagedDomains:
  - phigitalloyalty.com
  - daptapgo.io
