{{- range $app, $params := .Values.apps }}
{{- if has $.Values.stage $params.includeIn }}
{{- if ne $params.type "cron" }}
apiVersion: v1
kind: Service
metadata:
  name: {{ $.Values.project }}-{{ $app }}
  labels:
    app: {{ $.Values.project }}-{{ $app }}
    chart: "{{ $.Chart.Version | replace "+" "_" }}"
    version: "{{ $.Chart.Version | replace "+" "_" }}"
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
{{- if $.Values.service.annotations }}
  annotations:
{{ toYaml $.Values.service.annotations | indent 4 }}
{{- end }}
spec:
  type: {{ $.Values.service.type }}
  ports:
  - port: {{ $.Values.service.externalPort }}
    name: http
    targetPort: {{ $.Values.service.internalPort }}
    protocol: TCP
  selector:
    app: {{ $.Values.project }}-{{ $app }}
---
{{- end }}
{{- end }}
{{- end }}
