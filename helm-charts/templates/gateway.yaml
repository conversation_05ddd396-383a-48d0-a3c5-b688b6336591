{{- range $app, $params := .Values.apps }}
{{- if has $.Values.stage $params.includeIn }}
{{- if $params.public }}
{{- if ne $params.type "cron" }}
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: {{ $.Values.project }}-{{ $app }}-http-redirect
  labels:
    app: {{ $.Values.project }}-{{ $app }}
    chart: "{{ $.Chart.Version | replace "+" "_" }}"
    version: "{{ $.Chart.Version | replace "+" "_" }}"
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
  annotations:
{{- if $.Values.route }}
{{- if $.Values.route.annotations }}
{{ toYaml $.Values.route.annotations | indent 4 }}
{{- end }}
{{- end }}
{{- if eq $.Values.environment "production" }}
{{- if $params.production.route }}
{{- if $params.production.route.annotations }}
{{ toYaml $params.production.route.annotations | indent 4 }}
{{- end }}
{{- end }}
{{- else }}
{{- if $params.develop.route }}
{{- if $params.develop.route.annotations }}
{{ toYaml $params.develop.route.annotations | indent 4 }}
{{- end }}
{{- end }}
{{- end }}
spec:
  parentRefs:
  - name: cloudflare-gateway
    group: gateway.networking.k8s.io
    namespace: istio-ingress
    port: 80
  hostnames:
{{- if $params.domain }}
  - {{ $params.domain }}
{{- else if eq $.Values.environment "production" }}
  - {{ $.Values.project }}-{{ $app }}.{{ $.Values.domain }}
{{- else }}
  - {{ $.Values.project }}-{{ $app }}-{{ $.Values.stage }}.{{ $.Values.domain }}
{{- end }}
{{- if $params.extraDomains }}
{{- range $i, $domain := $params.extraDomains }}
{{- if eq $.Values.environment "production" }}
  - {{ $domain | replace "-<STAGE>" "" }}
{{- else }}
  - {{ $domain | replace "<STAGE>" $.Values.stage }}
{{- end }}
{{- end }}
{{- end }}

{{- if eq $.Values.environment "production" }}
{{- if $params.production.extraDomains }}
{{- range $i, $domain := $params.production.extraDomains }}
  - {{ $domain | replace "-<STAGE>" "" }}
{{- end }}
{{- end }}
{{- else }}
{{- if $params.develop.extraDomains }}
{{- range $i, $domain := $params.develop.extraDomains }}
  - {{ $domain | replace "<STAGE>" $.Values.stage }}
{{- end }}
{{- end }}
{{- end }}
  rules:
  - filters:
      - type: RequestRedirect
        requestRedirect:
          scheme: https
          statusCode: 301
          port: 443
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: {{ $.Values.project }}-{{ $app }}
  labels:
    app: {{ $.Values.project }}-{{ $app }}
    chart: "{{ $.Chart.Version | replace "+" "_" }}"
    version: "{{ $.Chart.Version | replace "+" "_" }}"
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
  annotations:
{{- if $.Values.route }}
{{- if $.Values.route.annotations }}
{{ toYaml $.Values.route.annotations | indent 4 }}
{{- end }}
{{- end }}
{{- if eq $.Values.environment "production" }}
{{- if $params.production.route }}
{{- if $params.production.route.annotations }}
{{ toYaml $params.production.route.annotations | indent 4 }}
{{- end }}
{{- end }}
{{- else }}
{{- if $params.develop.route }}
{{- if $params.develop.route.annotations }}
{{ toYaml $params.develop.route.annotations | indent 4 }}
{{- end }}
{{- end }}
{{- end }}
spec:
  parentRefs:
  - name: cloudflare-gateway
    group: gateway.networking.k8s.io
    namespace: istio-ingress
    port: 443
  hostnames:
{{- if $params.domain }}
  - {{ $params.domain }}
{{- else if eq $.Values.environment "production" }}
  - {{ $.Values.project }}-{{ $app }}.{{ $.Values.domain }}
{{- else }}
  - {{ $.Values.project }}-{{ $app }}-{{ $.Values.stage }}.{{ $.Values.domain }}
{{- end }}
{{- if $params.extraDomains }}
{{- range $i, $domain := $params.extraDomains }}
{{- if eq $.Values.environment "production" }}
  - {{ $domain | replace "-<STAGE>" "" }}
{{- else }}
  - {{ $domain | replace "<STAGE>" $.Values.stage }}
{{- end }}
{{- end }}
{{- end }}

{{- if eq $.Values.environment "production" }}
{{- if $params.production.extraDomains }}
{{- range $i, $domain := $params.production.extraDomains }}
  - {{ $domain | replace "-<STAGE>" "" }}
{{- end }}
{{- end }}
{{- else }}
{{- if $params.develop.extraDomains }}
{{- range $i, $domain := $params.develop.extraDomains }}
  - {{ $domain | replace "<STAGE>" $.Values.stage }}
{{- end }}
{{- end }}
{{- end }}
  rules:
  - filters:
      - type: RequestHeaderModifier
        requestHeaderModifier:
          set:
            - name: x-forwarded-proto
              value: https
    backendRefs:
    - name: {{ $.Values.project }}-{{ $app }}
      port: {{ $.Values.service.externalPort }}
---
{{- end }}
{{- end }}
{{- end }}
{{- end }}