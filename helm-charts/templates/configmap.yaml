{{- range $app, $params := .Values.apps }}
{{- if has $.Values.stage $params.includeIn }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $.Values.project }}-{{ $app }}
  labels:
    app: {{ $.Values.project }}-{{ $app }}
    chart: "{{ $.Chart.Version | replace "+" "_" }}"
    version: "{{ $.Chart.Version | replace "+" "_" }}"
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
data:
  APP_STAGE: {{ $.Values.environment }}
  APP_VERSION: {{ $.Chart.Version }}
  VERSION: {{ $.Values.version_tag }}
  NODE_ENV: production
  APM_SERVICE_NAME: {{ $app }}
  DD_PROFILING_ENABLED: "true"
  DD_LOGS_INJECTION: "true"
{{ toYaml $.Values.env.common | indent 2 }}
{{- range $app_env, $env := $.Values.env }}
{{- if $env }}
{{- if eq $app_env $app }}
{{ toYaml $env | indent 2 }}
{{- end }}
{{- end }}
{{- end }}

{{- if eq $.Values.stage "main" }}
{{- if $.Values.env.production.common }}
{{ toYaml $.Values.env.production.common | indent 2 }}
{{- end }}
{{- range $app_env, $env := $.Values.env.production }}
{{- if $env }}
{{- if eq $app_env $app }}
{{ toYaml $env | indent 2 }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- if eq $.Values.stage "stage" }}
{{- if $.Values.env.stage.common }}
{{ toYaml $.Values.env.stage.common | indent 2 }}
{{- end }}
{{- range $app_env, $env := $.Values.env.stage }}
{{- if $env }}
{{- if eq $app_env $app }}
{{ toYaml $env | indent 2 }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- if eq $.Values.stage "develop" }}
{{- if $.Values.env.develop.common }}
{{ toYaml $.Values.env.develop.common | indent 2 }}
{{- end }}
{{- range $app_env, $env := $.Values.env.develop }}
{{- if $env }}
{{- if eq $app_env $app }}
{{ toYaml $env | indent 2 }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- if eq $.Values.stage "temp" }}
{{- if $.Values.env.temp.common }}
{{ toYaml $.Values.env.temp.common | indent 2 }}
{{- end }}
{{- range $app_env, $env := $.Values.env.temp }}
{{- if $env }}
{{- if eq $app_env $app }}
{{ toYaml $env | indent 2 }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
---
{{- end }}
{{- end }}
