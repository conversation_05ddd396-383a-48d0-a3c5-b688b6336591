{{- if eq $.Values.environment "production" }}
{{- range $app, $params := .Values.apps }}
{{- if has $.Values.stage $params.includeIn }}
{{- if ne $params.type "cron" }}
{{- if $params.production.autoscale }}
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $.Values.project }}-{{ $app }}
  labels:
    app: {{ $.Values.project }}-{{ $app }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $.Values.project }}-{{ $app }}
  minReplicas: {{ $params.production.replicaCount  }}
  maxReplicas: {{ $params.production.maxReplicas | default 5 }}
  targetCPUUtilizationPercentage: 80
---
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
