{{- range $app, $params := .Values.apps }}
{{- if has $.Values.stage $params.includeIn }}
{{- if ne $params.type "cron" }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $.Values.project }}-{{ $app }}
  labels:
    app: {{ $.Values.project }}-{{ $app }}
    chart: "{{ $.Chart.Version | replace "+" "_" }}"
    version: "{{ $.Chart.Version | replace "+" "_" }}"
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
    tags.datadoghq.com/env: {{ $.Values.stage }}
    tags.datadoghq.com/service: {{ $app }}
    tags.datadoghq.com/version: "{{ $.Chart.Version | replace "+" "_" }}"
spec:
  selector:
    matchLabels:
      app: {{ $.Values.project }}-{{ $app }}
{{- if eq $.Values.environment "production" }}
  replicas: {{ $params.production.replicaCount }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
{{- else }}
  replicas: {{ $params.develop.replicaCount }}
{{- end }}
  strategy:
    type: {{ $params.upgradeStrategy | default "RollingUpdate" }}
  template:
    metadata:
      labels:
        app: {{ $.Values.project }}-{{ $app }}
        tags.datadoghq.com/env: {{ $.Values.stage }}
        tags.datadoghq.com/service: {{ $app }}
        tags.datadoghq.com/version: "{{ $.Chart.Version | replace "+" "_" }}"
        admission.datadoghq.com/enabled: "true"
    spec:
      containers:
      - name: {{ $.Values.project }}-{{ $app }}
        image: "{{$.Values.docker_registry}}/{{ $.Values.projectName | default $.Values.name }}-{{ $app }}:{{ $.Values.version_tag }}"
        imagePullPolicy: {{ $.Values.image.pullPolicy }}
        ports:
        - containerPort: {{ $.Values.service.internalPort }}
        envFrom:
        - configMapRef:
            name: {{ $.Values.project }}-{{ $app }}
        livenessProbe:
          httpGet:
            path: {{ $.Values.probePath }}
            port: {{ $.Values.service.internalPort }}
{{- if $params.probeAuthenticationHeaders }}
{{ toYaml $.Values.probeHeaders | indent 12 }}
{{- end }}
          initialDelaySeconds: {{ $.Values.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ $.Values.livenessProbe.periodSeconds }}
          successThreshold: {{ $.Values.livenessProbe.successThreshold }}
          timeoutSeconds: {{ $.Values.livenessProbe.timeoutSeconds }}
        readinessProbe:
          httpGet:
            path: {{ $.Values.probePath }}
            port: {{ $.Values.service.internalPort }}
{{- if $params.probeAuthenticationHeaders }}
{{ toYaml $.Values.probeHeaders | indent 12 }}
{{- end }}
          periodSeconds: {{ $.Values.readinessProbe.periodSeconds }}
          successThreshold: {{ $.Values.readinessProbe.successThreshold }}
          timeoutSeconds: {{ $.Values.readinessProbe.timeoutSeconds }}
        resources:
{{- if eq $.Values.environment "production" }}
{{ toYaml $params.production.resources | indent 10 }}
{{- else }}
{{ toYaml $params.develop.resources | indent 10 }}
{{- end }}
{{- if $params.podSpec }}
{{ toYaml $params.podSpec | indent 6 }}
{{- end }}
      terminationGracePeriodSeconds: {{ $.Values.terminationGracePeriodSeconds }}
      {{- if $.Values.imagePullSecrets }}
      imagePullSecrets:
        - name: {{ $.Values.imagePullSecrets }}
      {{- end }}
#       affinity:
#         nodeAffinity:
# {{- if eq $.Values.environment "production" }}
#           preferredDuringSchedulingIgnoredDuringExecution:
#           - preference:
#               matchExpressions:
#               - key: environment
#                 operator: In
#                 values:
#                 - production
#             weight: 100
# {{- else }}
#           requiredDuringSchedulingIgnoredDuringExecution:
#             nodeSelectorTerms:
#             - matchExpressions:
#               - key: environment
#                 operator: In
#                 values:
#                 - develop
# {{- end }}
---
{{- end }}
{{- end }}
{{- end }}
