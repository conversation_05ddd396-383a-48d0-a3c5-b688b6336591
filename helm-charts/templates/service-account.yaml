apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ $.Values.project }}-api-user
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ $.Values.project }}-api-user
rules:
  - apiGroups: ['*']
    resources: ['*']
    verbs: ['*']
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ $.Values.project }}-api-user
subjects:
  - kind: ServiceAccount
    name: {{ $.Values.project }}-api-user
roleRef:
  kind: Role
  name: {{ $.Values.project }}-api-user
  apiGroup: rbac.authorization.k8s.io