{{- range $app, $params := .Values.apps }}
{{- if has $.Values.stage $params.includeIn }}
{{- if $params.public }}
{{- if ne $params.type "cron" }}
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {{ $.Values.project }}-{{ $app }}-{{ $.Values.stage }}
  namespace: istio-system
  labels:
    app: {{ $.Values.project }}-{{ $app }}-{{ $.Values.stage }}
    chart: "{{ $.Chart.Version | replace "+" "_" }}"
    version: "{{ $.Chart.Version | replace "+" "_" }}"
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
spec:
  secretName: {{ $.Values.project }}-{{ $app }}-{{ $.Values.stage }}-{{ $.Values.domain | replace "." "-" }}
  issuerRef:
    {{- if $params.issuerRef}}
    name: {{ $params.issuerRef }}
    {{- else if has $.Values.domain $.Values.route53ManagedDomains }}
    {{- if eq $.Values.environment "production" }}
    name: cert-manager-letsencrypt-production-route53
    {{- else }}
    name: cert-manager-letsencrypt-staging-route53
    {{- end }}
    {{- else }}
    name: cert-manager-letsencrypt-production-cloudflare
    {{- end }}
    kind: ClusterIssuer
  dnsNames:
{{- if $params.domain }}
  - {{ $params.domain }}
{{- else if eq $.Values.environment "production" }}
  - {{ $.Values.project }}-{{ $app }}.{{ $.Values.domain }}
{{- else }}
  - {{ $.Values.project }}-{{ $app }}-{{ $.Values.stage }}.{{ $.Values.domain }}
{{- end }}
---
{{- if $params.extraDomains }}
{{- range $i, $domain := $params.extraDomains }}
{{- $rootDomain := $domain | toString | regexFind "([a-z\\-]+)(?:\\.com|\\.fr|\\.co.uk)" -}}
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  {{- if eq $.Values.environment "production" }}
  name: {{ $domain | replace "-<STAGE>" "" | replace "." "-" }}
  {{- else }}
  name: {{ $domain | replace "<STAGE>" $.Values.stage | replace "." "-" }}
  {{- end }}
  namespace: istio-system
  labels:
    app: {{ $.Values.project }}-{{ $app }}-{{ $.Values.stage }}
    chart: "{{ $.Chart.Version | replace "+" "_" }}"
    version: "{{ $.Chart.Version | replace "+" "_" }}"
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
spec:
  {{- if eq $.Values.environment "production" }}
  secretName: {{ $domain | replace "-<STAGE>" "" | replace "." "-" }}
  {{- else }}
  secretName: {{ $domain | replace "<STAGE>" $.Values.stage | replace "." "-" }}
  {{- end }}
  issuerRef:
    {{- if has $rootDomain $.Values.route53ManagedDomains }}
    {{- if eq $.Values.environment "production" }}
    name: cert-manager-letsencrypt-production-route53
    {{- else }}
    name: cert-manager-letsencrypt-staging-route53
    {{- end }}
    {{- else }}
    name: cert-manager-letsencrypt-production-cloudflare
    {{- end }}
    kind: ClusterIssuer
  dnsNames:
  {{- if eq $.Values.environment "production" }}
  - {{ $domain | replace "-<STAGE>" "" }}
  {{- else }}
  - {{ $domain | replace "<STAGE>" $.Values.stage }}
  {{- end }}
---
{{- end }}
{{- end }}

{{- if eq $.Values.environment "production" }}
{{- if $params.production.extraDomains }}
{{- range $i, $domain := $params.production.extraDomains }}
{{- $rootDomain := $domain | toString | regexFind "([a-z\\-]+)(?:\\.com|\\.fr|\\.co.uk)" -}}
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {{ $domain | replace "-<STAGE>" "" | replace "." "-" }}
  namespace: istio-system
  labels:
    app: {{ $.Values.project }}-{{ $app }}-{{ $.Values.stage }}
    chart: "{{ $.Chart.Version | replace "+" "_" }}"
    version: "{{ $.Chart.Version | replace "+" "_" }}"
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
spec:
  secretName: {{ $domain | replace "-<STAGE>" "" | replace "." "-" }}
  issuerRef:
    {{- if has $rootDomain $.Values.route53ManagedDomains }}
    name: cert-manager-letsencrypt-production-route53
    {{- else }}
    name: cert-manager-letsencrypt-production-cloudflare
    {{- end }}
    kind: ClusterIssuer
  dnsNames:
  - {{ $domain | replace "-<STAGE>" "" }}
---
{{- end }}
{{- end }}
{{- else }}

{{- if $params.develop.extraDomains }}
{{- range $i, $domain := $params.develop.extraDomains }}
{{- $rootDomain := $.Values.domain | toString | regexFind "([a-z\\-]+)(?:\\.com|\\.fr|\\.co.uk)" -}}
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {{ $domain | replace "-<STAGE>" "" | replace "." "-" }}
  namespace: istio-system
  labels:
    app: {{ $.Values.project }}-{{ $app }}-{{ $.Values.stage }}
    chart: "{{ $.Chart.Version | replace "+" "_" }}"
    version: "{{ $.Chart.Version | replace "+" "_" }}"
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
spec:
  secretName: {{ $domain | replace "-<STAGE>" "" | replace "." "-" }}
  issuerRef:
    {{- if has $rootDomain $.Values.route53ManagedDomains }}
    name: cert-manager-letsencrypt-staging-route53
    {{- else }}
    name: cert-manager-letsencrypt-production-cloudflare
    {{- end }}
    kind: ClusterIssuer
  dnsNames:
  - {{ $domain | replace "-<STAGE>" "" }}
---
{{- end }}
{{- end }}
{{- end }}

{{- end }}
{{- end }}
{{- end }}
{{- end }}
