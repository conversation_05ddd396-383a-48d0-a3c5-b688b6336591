{{- range $app, $params := .Values.apps }}
{{- if has $.Values.stage $params.includeIn }}
{{- if eq $params.type "cron" }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ $.Values.project }}-{{ $app }}
  labels:
    app: {{ $.Values.project }}-{{ $app }}
    chart: "{{ $.Chart.Version | replace "+" "_" }}"
    version: "{{ $.Chart.Version | replace "+" "_" }}"
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
    tags.datadoghq.com/env: {{ $.Values.stage }}
    tags.datadoghq.com/service: {{ $app }}
    tags.datadoghq.com/version: "{{ $.Chart.Version | replace "+" "_" }}"
spec:
{{- if eq $.Values.environment "production" }}
  schedule: "{{ $params.production.schedule | default "0 6 * * *" }}"
  concurrencyPolicy: {{ $params.production.concurrencyPolicy | default "Forbid" }}
  failedJobsHistoryLimit: {{ $params.production.failedJobsHistoryLimit | default 1 }}
  successfulJobsHistoryLimit: {{ $params.production.successfulJobsHistoryLimit | default 3 }}
  suspend: {{ $params.production.suspend | default false }}
{{- else }}
  schedule: "{{ $params.develop.schedule | default "0 6 * * *" }}"
  concurrencyPolicy: {{ $params.develop.concurrencyPolicy | default "Forbid" }}
  failedJobsHistoryLimit: {{ $params.develop.failedJobsHistoryLimit | default 1 }}
  successfulJobsHistoryLimit: {{ $params.develop.successfulJobsHistoryLimit | default 3 }}
  suspend: {{ $params.develop.suspend | default false }}
{{- end }}
  startingDeadlineSeconds: 604800
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            draft: {{ default "draft-app" $.Values.draft }}
            app: {{ $.Values.project }}-{{ $app }}
            tags.datadoghq.com/env: {{ $.Values.stage }}
            tags.datadoghq.com/service: {{ $app }}
            tags.datadoghq.com/version: "{{ $.Chart.Version | replace "+" "_" }}"
            admission.datadoghq.com/enabled: "true"
        spec:
          containers:
          - name: {{ $.Values.project }}-{{ $app }}
            image: "{{$.Values.docker_registry}}/{{ $.Values.projectName | default $.Values.name }}-{{ $params.container | default $app }}:{{ $.Values.version_tag }}"
            imagePullPolicy: {{ $.Values.image.pullPolicy }}
            envFrom:
            - configMapRef:
                name: {{ $.Values.project }}-{{ $app }}
            resources:
{{- if eq $.Values.environment "production" }}
{{ toYaml $params.production.resources | indent 14 }}
{{- else }}
{{ toYaml $params.develop.resources | indent 14 }}
{{- end }}
          restartPolicy: {{ $params.restartPolicy | default "OnFailure" }}
{{- if $params.podSpec }}
{{ toYaml $params.podSpec | indent 10 }}
{{- end }}
          {{- if $.Values.imagePullSecrets }}
          imagePullSecrets:
            - name: {{ $.Values.imagePullSecrets }}
          {{- end }}
#           affinity:
#             nodeAffinity:
# {{- if eq $.Values.environment "production" }}
#               preferredDuringSchedulingIgnoredDuringExecution:
#               - preference:
#                   matchExpressions:
#                   - key: environment
#                     operator: In
#                     values:
#                     - production
#                 weight: 100
# {{- else }}
#               requiredDuringSchedulingIgnoredDuringExecution:
#                 nodeSelectorTerms:
#                 - matchExpressions:
#                   - key: environment
#                     operator: In
#                     values:
#                     - develop
# {{- end }}
---
{{- end }}
{{- end }}
{{- end }}
