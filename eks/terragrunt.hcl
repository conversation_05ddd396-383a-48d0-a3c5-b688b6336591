#Generate backend.tf file for remote state configs using
#And creates the S3 bucket and DynamoDB table for lock

remote_state {
  backend = "s3"
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }

  config = {
    bucket               = "pl-devops-eks-tfstate"
    dynamodb_table       = "pl-devops-eks-tfstate"
    encrypt              = true
    key                  = "terraform.tfstate"
    region               = "us-east-1"
    workspace_key_prefix = "env"
    profile              = "phigitalloyalty"
  }
}

# END backend.tf file creation