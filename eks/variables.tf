variable "org_name" {
  type        = string
  description = "Organization name"
}

variable "dept_name" {
  type        = string
  description = "Department name (i.e IT, Marketing, Operations)"
  default     = "IT"
}

variable "dept_cc" {
  type        = string
  description = "Department cost center"
}

variable "team_name" {
  type        = string
  description = "Department team name (i.e. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, InfraOps)"
  default     = "DevOps"
}

variable "techstack" {
  type        = string
  description = "Category for the app's tech stack"
}

variable "tech_owner" {
  type        = string
  description = "Application, system, or service technical owner(s)"
  default     = "<EMAIL>"
}

variable "business_owner" {
  type        = string
  description = "Application, system, or service business owner(s)"
  default     = "<EMAIL>"
}

variable "code_author" {
  type        = string
  description = "Author of code and iac stand up"
}

variable "domain_name" {
  type        = map(list(string))
  description = "Domain name"
}

variable "region" {
  type        = string
  description = "AWS Region"
  default     = "us-east-1"
}

variable "priority" {
  type        = map(string)
  description = "Priority tag"
  default = {
    "develop"    = "P3 - Low"
    "stage"      = "P2 - Important"
    "production" = "P1 - Critical"
  }
}

variable "cluster_enabled_log_types" {
  description = "CloudWatch Log Group enabled log types"
  type        = map(list(string))
}

variable "cw_log_group_retention" {
  description = "CloudWatch Log Group retention in days"
  default     = "30"
}

variable "vpc_cidr" {
  type        = string
  description = "List of VPC CIDRs"
}

variable "managed_node_group" {
  type        = map(map(string))
  description = "Number of instance to launch"
}

variable "admin_cidr" {
  type        = list(string)
  description = "List of public IPs to allow for admin purpose"
  default     = [""]
}

variable "admin_port" {
  type        = list(string)
  description = "List of ports to allow for admin purpose"
  default     = [""]
}

variable "cluster_name" {
  description = "EKS Cluster Name"
  type        = string
  default     = ""
}

variable "github_actions_arn" {
  description = "Github Actions User ARN"
  type        = string
}

# variable "datadog_api_key" {
#   description = "Datadog API Key"
#   type        = string
# }

# variable "datadog_app_key" {
#   description = "Datadog App Key"
#   type        = string
# }

variable "letsencrypt_api_endpoint" {
  type = map(string)
  default = {
    develop    = "https://acme-staging-v02.api.letsencrypt.org/directory"
    stage      = "https://acme-staging-v02.api.letsencrypt.org/directory"
    production = "https://acme-v02.api.letsencrypt.org/directory"
  }
  description = "LetsEncrypt API endpoints"
}

variable "cloudflare_api_token" {
  description = "Cloudflare API Token"
  type        = string
}

variable "zone_id_filter" {
  description = "Namespace for the apps"
  type        = map(list(string))
}

variable "apps_namespace" {
  description = "Namespace for the apps"
  type        = list(string)
  default     = ["develop", "stage", "production"]
}
