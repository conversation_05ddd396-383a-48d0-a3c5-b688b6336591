variable "enabled" {
  type        = bool
  default     = true
  description = "Variable indicating whether <PERSON><PERSON><PERSON> is enabled."
}

variable "base_enabled" {
  type        = bool
  default     = true
  description = "Variable indicating whether Istio base CRDs are enabled."
}

variable "istiod_enabled" {
  type        = bool
  default     = true
  description = "Variable indicating whether istiod is enabled."
}

variable "cni_enabled" {
  type        = bool
  default     = true
  description = "Variable indicating whether CNI is enabled."
}

variable "ambient_mode_enabled" {
  type        = bool
  default     = true
  description = "Variable indicating whether Istio Ambient mode is enabled."
}

variable "kiali_enabled" {
  type        = bool
  default     = false
  description = "Variable indicating whether Istio ingress gateway is enabled."
}

variable "helm_chart_repo" {
  type        = string
  default     = "https://istio-release.storage.googleapis.com/charts"
  description = "Istio repository name."
}

variable "helm_chart_version" {
  type        = string
  default     = "1.22.1"
  description = "Istio helm chart version."
}

variable "create_namespace" {
  type        = bool
  default     = true
  description = "Whether to create Kubernetes namespace with name defined by `namespace`."
}

variable "namespace" {
  type        = string
  default     = "istio-system"
  description = "Kubernetes namespace for Istio"
}

variable "base_settings" {
  default     = {}
  description = "Additional settings which will be passed to the Istio Base Helm chart values."
}

variable "istiod_settings" {
  default = {
    "pilot" : {
      "env" : {
        "CA_TRUSTED_NODE_ACCOUNTS" : "istio-system/ztunnel,kube-system/ztunnel"
      }
    },
    "meshConfig" : {
      "outboundTrafficPolicy" : {
        "mode" : "ALLOW_ANY"
      }
    },
    "telemetry" : {
      "enabled" : false,
      "v2" : {
        "enabled" : false
      }
    }
    "istio_cni" : {
      "enabled" : true,
      "chained" : true
    }
  }
  description = "Additional settings which will be passed to the Istio Discovery Helm chart values."
}

variable "cni_settings" {
  default = {
    "cni" : {
      # "logLevel": "info",
      "privileged" : true,
      "excludeNamespaces" : [
        "kube-system"
      ]
    }
  }
  description = "Additional settings which will be passed to the Istio CNI Helm chart values."
}

variable "ingressgateway_settings" {
  default     = []
  description = "Additional settings which will be passed to the Istio Ingress Gateway Helm chart values."
}

variable "ztunnel_settings" {
  default     = {}
  description = "Additional settings which will be passed to the Istio Ztunnel Helm chart values."
}

resource "helm_release" "istio_base" {
  count      = var.enabled && var.base_enabled ? 1 : 0
  name       = "istio-base"
  repository = var.helm_chart_repo
  chart      = "base"
  version    = var.helm_chart_version
  namespace  = var.namespace

  create_namespace = true

  values = [
    yamlencode(var.base_settings)
  ]

  depends_on = [module.addons]
}

resource "helm_release" "cni" {
  count      = var.enabled && var.cni_enabled ? 1 : 0
  name       = "istio-cni"
  repository = var.helm_chart_repo
  chart      = "cni"
  version    = var.helm_chart_version
  namespace  = var.namespace

  set {
    name  = "profile"
    value = "ambient"
  }

  values = [
    yamlencode(var.cni_settings)
  ]

  depends_on = [
    helm_release.istio_base
  ]
}

resource "helm_release" "istiod" {
  count      = var.enabled && var.istiod_enabled ? 1 : 0
  name       = "istio-istiod"
  repository = var.helm_chart_repo
  chart      = "istiod"
  version    = var.helm_chart_version
  namespace  = var.namespace

  set {
    name  = "profile"
    value = "ambient"
  }

  values = [
    yamlencode(var.istiod_settings)
  ]

  depends_on = [
    helm_release.cni
  ]
}

resource "helm_release" "istio_ztunnel" {
  count      = var.enabled && var.ambient_mode_enabled ? 1 : 0
  name       = "istio-ztunnel"
  repository = var.helm_chart_repo
  chart      = "ztunnel"
  version    = var.helm_chart_version
  namespace  = var.namespace

  values = [
    yamlencode(var.ztunnel_settings)
  ]

  depends_on = [
    helm_release.istiod
  ]
}

resource "helm_release" "istio_ingressgateway" {
  count      = var.enabled && !var.ambient_mode_enabled ? length(var.ingressgateway_settings) : 0
  name       = var.ingressgateway_settings[count.index].name
  repository = var.helm_chart_repo
  chart      = "gateway"
  version    = var.helm_chart_version
  namespace  = var.namespace

  set {
    name  = "securityContext.fsGroup"
    value = 1337
  }

  set {
    name  = "securityContext.runAsGroup"
    value = 1337
  }

  set {
    name  = "securityContext.runAsNonRoot"
    value = true
  }

  set {
    name  = "securityContext.runAsUser"
    value = 1337
  }

  values = [
    yamlencode(var.ingressgateway_settings[count.index].settings)
  ]

  depends_on = [
    helm_release.istiod
  ]
}

resource "helm_release" "kiali-operator" {
  count      = var.enabled && var.kiali_enabled ? 1 : 0
  name       = "istio-kiali"
  repository = "https://kiali.org/helm-charts"
  chart      = "kiali-operator"
  version    = "1.85"
  namespace  = "kiali-operator"

  reuse_values     = true
  create_namespace = true

  set {
    name  = "cr.create"
    value = true
  }

  set {
    name  = "cr.namespace"
    value = "istio-system"
  }

  depends_on = [
    helm_release.istiod
  ]
}

