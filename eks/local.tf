locals {
  env           = terraform.workspace
  resource_name = lower("${var.org_name}-${local.env}")

  cluster_name = coalesce(var.cluster_name, local.resource_name)

  managed_node_group = var.managed_node_group[local.env]

  cluster_enabled_log_types = var.cluster_enabled_log_types[local.env]

  azs = slice(data.aws_availability_zones.available.names, 0, 3)

  common_tags = {
    Priority      = var.priority[local.env]
    Env           = local.env
    Dept          = var.dept_name
    Dept_CC       = var.dept_cc
    Team          = var.team_name
    Tech_Owner    = var.tech_owner
    BusinessOwner = var.business_owner
    TechStack     = var.techstack
    Organization  = var.org_name
    Created_by    = var.code_author
  }

  # datadog_api_key = var.datadog_api_key

  letsencrypt_api_endpoint = var.letsencrypt_api_endpoint[local.env]

  apps_namespace = var.apps_namespace
}
