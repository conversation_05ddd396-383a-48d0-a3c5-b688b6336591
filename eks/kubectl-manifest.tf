# DataDog
# resource "kubectl_manifest" "datadog_agent" {
#   yaml_body = <<-EOF
#     apiVersion: datadoghq.com/v1alpha1
#     kind: DatadogAgent
#     metadata:
#       name: datadog-agent
#       namespace: datadog-operator
#     spec:
#       credentials:
#         apiKey: "${var.datadog_api_key}"
#         appKey: "${var.datadog_app_key}"
#       features:
#         prometheusScrape:
#           enabled: true
#           serviceEndpoints: true
#       agent:
#         config:
#           criSocket:
#             criSocketPath: /run/dockershim.sock
#           tolerations:
#           - operator: Exists
#         apm:
#           enabled: true
#         process:
#           enabled: true
#           processCollectionEnabled: true
#         log:
#           enabled: true
#         systemProbe:
#           bpfDebugEnabled: true
#         security:
#           compliance:
#             enabled: true
#           runtime:
#             enabled: false
#       clusterAgent:
#         config:
#           externalMetrics:
#             enabled: true
#           admissionController:
#             enabled: true
#           clusterChecksEnabled: true
#       clusterChecksRunner:
#         enabled: true
#   EOF

#   depends_on = [module.addons]
# }

# CloudFlare
resource "kubernetes_secret" "cert_manager_cloudflare_secret" {
  metadata {
    name      = "cert-manager-cloudflare-provider-api-token"
    namespace = "cert-manager"
    labels = {
      app = "cert-manager"
    }
  }

  data = {
    api-token = "${var.cloudflare_api_token}"
  }

  type = "Opaque"

  depends_on = [module.addons]
}

resource "kubectl_manifest" "cert_manager_cloudflare" {
  yaml_body = <<-EOF
    apiVersion: cert-manager.io/v1
    kind: ClusterIssuer
    metadata:
      annotations:
        meta.helm.sh/release-name: cert-manager-letsencrypt
        meta.helm.sh/release-namespace: cert-manager
      name: cert-manager-letsencrypt-production-cloudflare
      labels:
        ca: letsencrypt
        environment: production
        provider: cloudflare
        solver: dns01
    spec:
      acme:
        email: "${var.tech_owner}"
        server: "${local.letsencrypt_api_endpoint}"
        preferredChain: ISRG Root X1
        privateKeySecretRef:
          name: letsencrypt-cloudflare-private-key-prod
        solvers:
        - dns01:
            cloudflare:
              apiTokenSecretRef:
                name: "${kubernetes_secret.cert_manager_cloudflare_secret.metadata.0.name}"
                key: api-token
          selector:
            dnsZones: [${join(",", var.domain_name["cloudflare"])}]
  EOF

  depends_on = [kubernetes_secret.cert_manager_cloudflare_secret]
}

resource "kubernetes_namespace" "apps_namespace" {
  for_each = toset(var.apps_namespace)

  metadata {
    labels = {
      # "istio-injection"         = "enabled"
      "istio.io/dataplane-mode" = "ambient"
    }
    name = each.key
  }

  depends_on = [module.addons]
}

# resource "kubernetes_namespace" "redis_namespace" {
#   for_each = toset(var.apps_namespace)

#   metadata {
#     labels = {
#       # "istio-injection"         = "enabled"
#       "istio.io/dataplane-mode" = "ambient"
#     }
#     name = each.key
#   }

#   depends_on = [module.addons]
# }

data "http" "gateway_api" {
  url = "https://github.com/kubernetes-sigs/gateway-api/releases/download/v1.0.0/experimental-install.yaml"

  request_headers = {
    Accept = "application/octet-stream"
  }
}

data "kubectl_file_documents" "gateway_api_doc" {
  content = data.http.gateway_api.response_body
}

resource "kubectl_manifest" "gateway_api" {
  for_each  = data.kubectl_file_documents.gateway_api_doc.manifests
  yaml_body = each.value

  depends_on = [module.eks]
}

resource "kubernetes_namespace" "istio_ingress" {
  metadata {
    name = "istio-ingress"
  }

  depends_on = [module.addons]
}

locals {
  gateway_listeners = {
    for domain in concat(var.domain_name["aws"], var.domain_name["cloudflare"]) : domain => [
      {
        name     = "http-${replace(lower(domain), ".", "-")}"
        hostname = domain
        port     = 80
        protocol = "HTTP"
        allowedRoutes = {
          namespaces = {
            from = "All"
          }
        }
      },
      {
        name     = "https-${replace(lower(domain), ".", "-")}"
        hostname = domain
        port     = 443
        protocol = "HTTPS"
        allowedRoutes = {
          namespaces = {
            from = "All"
          }
        }
        tls = {
          mode = "Terminate"
          certificateRefs = [
            {
              name  = replace(lower(domain), ".", "-")
              kind  = "Secret"
              group = ""
            }
          ]
        }
      },
      {
        name     = "http-wildcard-${replace(lower(domain), ".", "-")}"
        hostname = "*.${domain}"
        port     = 80
        protocol = "HTTP"
        allowedRoutes = {
          namespaces = {
            from = "All"
          }
        }
      },
      {
        name     = "https-wildcard-${replace(lower(domain), ".", "-")}"
        hostname = "*.${domain}"
        port     = 443
        protocol = "HTTPS"
        allowedRoutes = {
          namespaces = {
            from = "All"
          }
        }
        tls = {
          mode = "Terminate"
          certificateRefs = [
            {
              name  = replace(lower(domain), ".", "-")
              kind  = "Secret"
              group = ""
            }
          ]
        }
      }
    ]
  }
}

resource "kubectl_manifest" "gateway" {
  yaml_body = yamlencode({
    apiVersion = "gateway.networking.k8s.io/v1"
    kind       = "Gateway"
    metadata = {
      name      = "cloudflare-gateway"
      namespace = "istio-ingress"
      annotations = {
        "cert-manager.io/cluster-issuer"                          = "cert-manager-letsencrypt-production-cloudflare"
        "service.beta.kubernetes.io/aws-load-balancer-type"       = "nlb"
        "service.beta.kubernetes.io/aws-load-balancer-scheme"     = "internet-facing"
        "service.beta.kubernetes.io/aws-load-balancer-attributes" = "load_balancing.cross_zone.enabled=true"
      }
    }
    spec = {
      gatewayClassName = "istio"
      listeners        = flatten(values(local.gateway_listeners))
    }
  })

  depends_on = [
    kubernetes_namespace.istio_ingress,
    kubectl_manifest.gateway_api,
    kubectl_manifest.cert_manager_cloudflare,
    helm_release.istio_ingressgateway
  ]
}
