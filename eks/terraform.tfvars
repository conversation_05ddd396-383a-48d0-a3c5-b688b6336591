###### Application specifications ########
org_name       = "PL"
dept_name      = "IT"
dept_cc        = "000000"
team_name      = "arch-sredo"
techstack      = "core-infra"
tech_owner     = "<EMAIL>"
business_owner = "<EMAIL>"
code_author    = "<EMAIL>"

region = "us-east-1"

domain_name = {
  aws = []
  cloudflare = [
    "daptapgo.io",
  ]
}

zone_id_filter = {
  aws = []
  cloudflare = [
    # daptapgo.io
    "47d7071f9a9e05ba0b566da1c0a3e079",
  ]
}

cloudflare_api_token = "****************************************"

#Networking
vpc_cidr = "10.0.0.0/16"

github_actions_arn = "arn:aws:iam::992382835264:user/github_actions"

#Conputer sizing
managed_node_group = {
  "develop" = {
    "min_size"     = 1
    "max_size"     = 2
    "desired_size" = 1
    "disk_size"    = 60
  }
  "stage" = {
    "min_size"     = 1
    "max_size"     = 1
    "desired_size" = 1
    "disk_size"    = 60
  }
  "production" = {
    "min_size"     = 2
    "max_size"     = 3
    "desired_size" = 2
    "disk_size"    = 60
  }
}

admin_cidr = [
  "0.0.0.0/0"
]

#Named rules: https://github.com/terraform-aws-modules/terraform-aws-security-group/blob/master/rules.tf
admin_port = [
  "ssh-tcp"
]

cluster_enabled_log_types = {
  "develop"    = ["api", "audit", "authenticator"]
  "stage"      = ["api", "audit", "authenticator"]
  "production" = ["api", "audit", "authenticator"]
}
