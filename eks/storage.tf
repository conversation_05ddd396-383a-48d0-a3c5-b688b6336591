# module "efs" {
#   source  = "terraform-aws-modules/efs/aws"
#   version = "~> 1.0"

#   creation_token = local.resource_name
#   name           = local.resource_name

#   # Mount targets / security group
#   mount_targets = { for k, v in toset(range(length(local.azs))) :
#     element(local.azs, k) => { subnet_id = element(module.vpc.private_subnets, k) }
#   }
#   security_group_description = "${local.resource_name} EFS security group"
#   security_group_vpc_id      = module.vpc.vpc_id
#   security_group_rules = {
#     vpc = {
#       # relying on the defaults provdied for EFS/NFS (2049/TCP + ingress)
#       description = "NFS ingress from VPC private subnets"
#       cidr_blocks = module.vpc.private_subnets_cidr_blocks
#     }
#   }

#   tags = merge(local.common_tags)
# }

resource "kubernetes_storage_class_v1" "gp3" {
  metadata {
    name = "standard"
  }

  storage_provisioner    = "ebs.csi.aws.com"
  allow_volume_expansion = true
  reclaim_policy         = "Delete"
  volume_binding_mode    = "WaitForFirstConsumer"
  parameters = {
    encrypted = true
    fsType    = "ext4"
    type      = "gp3"
  }

  depends_on = [module.addons]
}

# resource "kubernetes_storage_class_v1" "efs" {
#   metadata {
#     name = "efs"
#   }

#   storage_provisioner = "efs.csi.aws.com"
#   parameters = {
#     provisioningMode = "efs-ap" # Dynamic provisioning
#     fileSystemId     = module.efs.id
#     directoryPerms   = "700"
#   }

#   mount_options = [
#     "iam"
#   ]

#   depends_on = [module.addons]
# }
